import os
import numpy as np
import torch
import random
import json

if torch.cuda.is_available():
    dtype = 'cuda'
elif torch.backends.mps.is_available():
    dtype = 'mps'
else:
    dtype = 'cpu'
device = torch.device(dtype)

def set_seed(args):
  random.seed(args.seed)
  np.random.seed(args.seed)
  torch.manual_seed(args.seed)
  if args.n_gpu > 0:
    torch.cuda.manual_seed_all(args.seed)

def setup_gpus(args):
  n_gpu = 0  # set the default to 0
  if torch.cuda.is_available():
    n_gpu = torch.cuda.device_count()
  args.n_gpu = n_gpu
  if n_gpu > 0:   # this is not an 'else' statement and cannot be combined
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.deterministic = True
  return args

def check_directories(args):
  task_path = os.path.join("modeling", args.output_dir, args.model, args.task)
  save_path = os.path.join(task_path, args.method)

  if not os.path.exists(task_path):
    os.makedirs(task_path)
    print(f"Created {task_path} for {args.task} results")
  if not os.path.exists(save_path):
    os.makedirs(save_path)
    print(f"Created {save_path} directory")

  # cache_path = os.path.join(args.input_dir, 'cache', args.dataset)
  # if args.domain:
  #   cache_path = os.path.join(cache_path, args.domain)
  if args.debug:
    args.log_interval /= 10
  return args, save_path

def extract_flow_type(flow_name):
  # Flow names are given in 'Parent(child)' format
  parent_type, child_type = flow_name.split('(')
  child_type = child_type.rstrip(')')
  return parent_type, child_type

dialogue_acts = ['chat', 'query', 'measure', 'plot', 'retrieve', 'insert', 'update', 'delete',
                 'user', 'agent', 'table', 'row', 'column', 'multiple', 'confirm', 'deny']

def dax2dact(dax, form='string'):
  dact_list = []
  for digit in dax:
    # Convert each hexadecimal digit to index position
    dact_index = int(digit, 16)
    if dact_index > 0:   # zero is reserved for ambiguity
      dact_list.append( dialogue_acts[dact_index] )
  if len(dact_list) == 0:
    dact_list.append(dialogue_acts[0])  # default to 'chat' (ie. dialog act is ambiguous)

  if form == 'string':
    return ' + '.join(dact_list)
  return dact_list

def dact2dax(dact):
  # converts a dact list into a dax string
  dact_list = dact.split(' + ') if isinstance(dact, str) else dact
  positions = [i for i, dialog_act in enumerate(dialogue_acts) if dialog_act in dact_list]
  dax_string = ''.join(format(pos, 'X') for pos in positions)
  return dax_string.zfill(3)

def dax2intent(dax, form='hex'):
  # convert to dax if input is actually in dact form
  if form == 'string' or len(dax) > 3:
    dax = dact2dax(dax)

  # handle some special cases first
  if dax in ['089', '39B', '129', '149', '19A', '489', '9DF']:
    return 'Internal'
  elif '46' in dax:
    return 'Detect'
  elif dax.startswith('36') or dax.endswith('BD'):
    return 'Clean'

  # default to converse
  dax = dax.lstrip('0')
  intent = 'Converse'

  if dax:
    first_digit = int(dax[0], 16)  # Convert to int using base 16
    if '5' in dax or '7' in dax:
      intent = 'Transform'
    elif '3' in dax:
      intent = 'Visualize'
    elif first_digit in [1, 2]:
      intent = 'Analyze'
    elif '6' in dax:
      intent = 'Clean'
  return intent