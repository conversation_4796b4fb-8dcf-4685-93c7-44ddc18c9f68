import numpy as np
import math
import pandas as pd
from collections import defaultdict
from typing import List

from backend.components.metadata import MetaData
from backend.components.metadata.typechecks import NullSubtype, DefaultSubtype, MissingSubtype
from backend.utilities.search import sample_from_series
from backend.prompts.for_metadata import *

class Blank(MetaData):

  def __init__(self, table_name, table_properties, level, api=None):
    super().__init__(table_properties, table_name, level, api)
    self.name = 'blank'
    self.subtypes = [NullSubtype(), DefaultSubtype(), MissingSubtype()]

  def detect_blanks(self, issue_df, column):
    """ Transfers the issues from the shadow table to the metadata, double checks if column contains blanks
      - True nulls are undeniable, but missing and default values are more ambiguous
      - Use the API to determine if the detected blanks are actually issues
      - Returns the updated issue_df and the detected blank rows
    """
    import pandas as pd

    # Get current missing and default issues for this column
    col_name = column.name
    missing_issues = issue_df[(issue_df['column_name'] == col_name) &
                             (issue_df['issue_subtype'] == 'missing')]
    default_issues = issue_df[(issue_df['column_name'] == col_name) &
                             (issue_df['issue_subtype'] == 'default')]

    # Extract the detected terms from the issues
    missing_rows = missing_issues['row_id'].tolist() if not missing_issues.empty else []
    default_rows = default_issues['row_id'].tolist() if not default_issues.empty else []

    # Use API to validate the detected blanks
    if self.api and (missing_rows or default_rows):
      prompt_result = self.api.detect_blank_prompt(
        column_name=col_name,
        column_data=column.tolist(),
        detected_missing_rows=missing_rows,
        detected_default_rows=default_rows
      )

      if not prompt_result.get('accept', True):
        # Remove existing missing/default issues for this column
        issue_df = issue_df[~((issue_df['column_name'] == col_name) &
                             (issue_df['issue_subtype'].isin(['missing', 'default'])))]

        # Add new issues based on API recommendations
        new_missing_terms = prompt_result.get('missing_terms', [])
        new_default_terms = prompt_result.get('default_terms', [])

        new_issues = []
        for idx, value in enumerate(column):
          if pd.isna(value) or str(value).strip() in new_missing_terms:
            new_issues.append({
              'row_id': idx,
              'column_name': col_name,
              'original_value': value,
              'issue_type': 'blank',
              'issue_subtype': 'missing'
            })
          elif str(value).strip() in new_default_terms:
            new_issues.append({
              'row_id': idx,
              'column_name': col_name,
              'original_value': value,
              'issue_type': 'blank',
              'issue_subtype': 'default'
            })

        if new_issues:
          new_issues_df = pd.DataFrame(new_issues)
          issue_df = pd.concat([issue_df, new_issues_df], ignore_index=True)

    # Get final blank rows for return
    final_blank_issues = issue_df[(issue_df['column_name'] == col_name) &
                                 (issue_df['issue_type'] == 'blank')]
    blank_rows = final_blank_issues['row_id'].tolist()

    self.prepared = True
    return issue_df, blank_rows
    
  @staticmethod
  def type_to_nl(blank_type, count, prefix='article'):
    # Converts blank type to natural language
    if prefix == 'article':
      result = "a " if count == 1 else ""
    elif prefix == 'digit':
      result = f"{count} "
    else:
      result = ""

    result += f"{blank_type} value" if count == 1 else f"{blank_type} values"
    return result