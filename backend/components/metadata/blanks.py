import numpy as np
import math
import pandas as pd
from collections import defaultdict
from typing import List

from backend.components.metadata import MetaData
from backend.components.metadata.typechecks import NullSubtype, DefaultSubtype, MissingSubtype
from backend.utilities.search import sample_from_series
from backend.prompts.for_metadata import *

class Blank(MetaData):

  def __init__(self, table_name, table_properties, level, api=None):
    super().__init__(table_properties, table_name, level, api)
    self.name = 'blank'
    self.subtypes = [NullSubtype(), DefaultSubtype(), MissingSubtype()]

  # -------------- access methods ---------------
  def detected_row_ids(self, col_name:str, issue_type:str='') -> List[str]:
    # return the row ids of the detected issues in a specific column
    detected_rows = []
    for itype, blank in self.issues.items():
      if len(issue_type) == 0 or itype == issue_type:
        if col_name in blank:
          detected_rows.extend(blank[col_name])
    return detected_rows

  def detected_issue_types(self, col_name:str) -> List[str]:
    # return the issue types detected in a specific column
    detected_types = []
    for issue_type, blank in self.issues.items():
      if col_name in blank and len(blank[col_name]) > 0:
        detected_types.append(issue_type)
    return detected_types

  # -------------- detection methods ---------------
  def detect_blanks(self, issue_df, column):
    """ Transfers the issues from the shadow table to the metadata, double checks if column contains blanks
      - True nulls are undeniable, but missing and default values are more ambiguous
      - Use the API to determine if the detected blanks are actually issues
      - Returns the updated issue_df and the detected blank rows
    """
    blank_rows = []
    for idx, row in issue_df.iterrows():
      if row['issue_type'] == 'blank':
        blank_rows.append(idx)
        issue_df = issue_df.drop(idx)
    self.prepared = True

    return issue_df, blank_rows
    
  @staticmethod
  def type_to_nl(blank_type, count, prefix='article'):
    # Converts blank type to natural language
    if prefix == 'article':
      result = "a " if count == 1 else ""
    elif prefix == 'digit':
      result = f"{count} "
    else:
      result = ""

    result += f"{blank_type} value" if count == 1 else f"{blank_type} values"
    return result