import numpy as np
import math
import pandas as pd
from collections import defaultdict
from typing import List

from backend.components.metadata import MetaData
from backend.components.metadata.typechecks import BlankType, NullSubtype, DefaultSubtype, MissingSubtype
from backend.utilities.search import sample_from_series
from backend.prompts.for_metadata import *

class Blank(MetaData):

  def __init__(self, table, table_properties, level, api=None):
    super().__init__(table_properties, level, api)
    self.name = 'blank'
    self.detect_issues(table)
    self.types = [NullSubtype(), DefaultSubtype(), MissingSubtype()]
    self.gather_affected_columns()

  def detect_issues(self, table):
    self.issues = {
      'null': self.detect_nulls(table),
      'default': self.detect_default(table),
      'missing': self.detect_missing(table)
    }
    self.prepared = True

  # -------------- access methods ---------------
  def detected_row_ids(self, col_name:str, issue_type:str='') -> List[str]:
    # return the row ids of the detected issues in a specific column
    detected_rows = []
    for itype, blank in self.issues.items():
      if len(issue_type) == 0 or itype == issue_type:
        if col_name in blank:
          detected_rows.extend(blank[col_name])
    return detected_rows

  def detected_issue_types(self, col_name:str) -> List[str]:
    # return the issue types detected in a specific column
    detected_types = []
    for issue_type, blank in self.issues.items():
      if col_name in blank and len(blank[col_name]) > 0:
        detected_types.append(issue_type)
    return detected_types

  # -------------- detection methods ---------------
  def detect_nulls(self, df):
    """ Detects if a column has null values, and if so, returns the index values """
    nulls = defaultdict(list)

    for col_name in df.columns:
      # Directly find all nulls in the column
      null_indices = df[col_name][pd.isnull(df[col_name])].index.tolist()
      nulls[col_name].extend(null_indices)
    return nulls

  def detect_default(self, df):
    """ Detects if a column has default_values, and if so, returns the index of those rows """
    default_values = ["test", "lorem ipsum", "000-0000", "john smith", "placeholder", "example", "abc", "xyz"]
    default = defaultdict(list)

    for col_name in df.columns:
      column = df[col_name].dropna()

      ns_count = 0
      for index, row in column.items():
        if not isinstance(row, str):
          ns_count += 1
          if ns_count > 32:
            break
        elif row.lower() in default_values:
          default[col_name].append(index)

      # first check if column is a number column
      if ns_count > 32 and col_name in self.number_cols:
        # then check if it has a high ratio of positive values to make sure zeros are not considered default
        ratio_positive = len(column[column > 0]) / len(column)
        if ratio_positive > 0.95:
          zero_indices = column[column == 0].index.tolist()
          default[col_name].extend(zero_indices)

    return default

  def detect_missing(self, df):
    """ Detects if a column has empty or missing strings, and returns the index of those rows """
    empty_strings = ["", " ", "missing", "blank", "empty", "null", "na", "n/a", "nan", "none"]
    missing = defaultdict(list)

    for col_name in df.columns:
      ns_count = 0
      for index, row in df[col_name].dropna().items():
        if not isinstance(row, str):
          ns_count += 1
          if ns_count > 32:
            break
        elif row.lower() in empty_strings:
          missing[col_name].append(index)

    return missing

  @staticmethod
  def type_to_nl(blank_type, count, prefix='article'):
    # Converts blank type to natural language
    if prefix == 'article':
      result = "a " if count == 1 else ""
    elif prefix == 'digit':
      result = f"{count} "
    else:
      result = ""

    result += f"{blank_type} value" if count == 1 else f"{blank_type} values"
    return result