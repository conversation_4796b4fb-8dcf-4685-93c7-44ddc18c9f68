from backend.utilities.search import select_rows_to_display, select_columns_to_display
from collections import Counter

class MetaData(object):

  def __init__(self, tab_properties, table_name, level, api):
    self.level = level
    self.api = api
    self.tab_name = table_name
    self.tab_properties = tab_properties
    self.prepared = False

    self.unique_cols = []
    self.datetime_cols = []
    self.location_cols = []
    self.number_cols = []
    self.text_cols = []
    self.term_cols = []

    for column_name, properties in self.tab_properties.items():
      if properties['type'] == 'unique':
        self.unique_cols.append(column_name)
      elif properties['type'] == 'datetime':
        self.datetime_cols.append(column_name)
      elif properties['type'] == 'location':
        self.location_cols.append(column_name)
      elif properties['type'] == 'number':
        self.number_cols.append(column_name)
      elif properties['type'] == 'text':
        self.text_cols.append(column_name)
      if properties['subtype'] in ['status', 'category']:
        self.term_cols.append(column_name)

  @staticmethod
  def select_data(df, columns, rows, compare=False):
    selected_rows = select_rows_to_display(df, rows) if compare else rows
    selected_cols = select_columns_to_display(df, columns) if compare else columns
    data_to_display = df.loc[selected_rows][selected_cols]
    return data_to_display

  def issues_exist(self, columns:list, issue_type:str='') -> bool:
    for col_name in columns:
      if len(issue_type) > 0:
        issue_data = getattr(self, issue_type)
        if issue_data[col_name]:
          return True
      else:
        issue_count = self.affected_cols.get(col_name, 0)
        if issue_count > 0:
          return True
    return False

  def remove_issues(self, col_name:str, for_removal:list, issue_types:list=[]):
    if len(for_removal) == 0 or len(issue_types) == 0:
      for issue_type, issue_data in self.issues.items():
        if col_name in issue_data:
          del issue_data[col_name]

    else:  # remove specific issues from the column
      for row_id, issue_type in zip(for_removal, issue_types):
        self.issues[issue_type][col_name].remove(row_id)
        if len(self.issues[issue_type][col_name]) == 0:
          del self.issues[issue_type][col_name]

  # -------------- access methods ---------------
  def detected_row_ids(self, issues, col_name:str='', issue_type:str=''):
    # return the row ids of the detected issues in a specific column
    # if col_name is not specified, return all row ids with issues
    # if issue_type is specified, filter by that issue type
    if issues.empty:
      return []

    filtered_issues = issues
    if col_name:
      filtered_issues = filtered_issues[filtered_issues['column_name'] == col_name]
    if issue_type:
      filtered_issues = filtered_issues[filtered_issues['issue_type'] == issue_type]

    return filtered_issues['row_id'].unique().tolist()
  
  def detected_issue_types(self, issues, col_name:str=''):
    # return the unique issue types detected in a specific column
    # if col_name is not specified, return all issue types in the table
    if issues.empty:
      return []

    filtered_issues = issues
    if col_name:
      filtered_issues = filtered_issues[filtered_issues['column_name'] == col_name]

    return filtered_issues['issue_type'].unique().tolist()

  def num_issue_rows(self, issues, col_name:str='', issue_type:str='', subtype:str='') -> int:
    # return the number of unique rows with issues detected
    # if col_name is not specified, return the number of unique rows with issues for all columns
    # if issue_type is specified, filter by that issue type
    # if subtype is specified, return the number of issues of that subtype instead
    if issues.empty:
      return 0

    filtered_issues = issues
    if col_name:
      filtered_issues = filtered_issues[filtered_issues['column_name'] == col_name]
    if issue_type:
      filtered_issues = filtered_issues[filtered_issues['issue_type'] == issue_type]
    if subtype:
      filtered_issues = filtered_issues[filtered_issues['issue_subtype'] == subtype]

    return filtered_issues['row_id'].nunique()