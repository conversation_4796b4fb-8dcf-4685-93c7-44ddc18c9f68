from backend.utilities.search import select_rows_to_display, select_columns_to_display
from collections import Counter

class MetaData(object):

  def __init__(self, tab_properties, level, api):
    self.level = level
    self.api = api
    self.tab_properties = tab_properties
    self.prepared = False

    self.unique_cols = []
    self.datetime_cols = []
    self.location_cols = []
    self.number_cols = []
    self.text_cols = []
    self.term_cols = []

    for column_name, properties in self.tab_properties.items():
      if properties['type'] == 'unique':
        self.unique_cols.append(column_name)
      elif properties['type'] == 'datetime':
        self.datetime_cols.append(column_name)
      elif properties['type'] == 'location':
        self.location_cols.append(column_name)
      elif properties['type'] == 'number':
        self.number_cols.append(column_name)
      elif properties['type'] == 'text':
        self.text_cols.append(column_name)
      if properties['subtype'] in ['status', 'category']:
        self.term_cols.append(column_name)

  @staticmethod
  def select_data(df, columns, rows, compare=False):
    selected_rows = select_rows_to_display(df, rows) if compare else rows
    selected_cols = select_columns_to_display(df, columns) if compare else columns
    data_to_display = df.loc[selected_rows][selected_cols]
    return data_to_display

  def gather_affected_columns(self):
    self.affected_cols = Counter()
    for issue_type, issue_data in self.issues.items():
      for col_name, issue_ids in issue_data.items():
        self.affected_cols[col_name] += len(issue_ids)

  def num_detected(self, col_name:str='', issue_type:str='') -> int:
    if len(col_name) > 0:
      if len(issue_type) > 0:
        # return the number of issues of a specific type found in the column
        issue_rows = self.issues[issue_type][col_name]
        return len(issue_rows)
      else:
        # return the number of issues detected in a specific column
        return self.affected_cols[col_name]
    else:
      # return the number of columns where issues were detected
      return len([col for col, count in self.affected_cols.items() if count > 0])

  def issues_exist(self, columns:list, issue_type:str='') -> bool:
    for col_name in columns:
      if len(issue_type) > 0:
        issue_data = getattr(self, issue_type)
        if issue_data[col_name]:
          return True
      else:
        issue_count = self.affected_cols.get(col_name, 0)
        if issue_count > 0:
          return True
    return False

  def remove_issues(self, col_name:str, for_removal:list, issue_types:list=[]):
    if len(for_removal) == 0 or len(issue_types) == 0:
      for issue_type, issue_data in self.issues.items():
        if col_name in issue_data:
          del issue_data[col_name]

    else:  # remove specific issues from the column
      for row_id, issue_type in zip(for_removal, issue_types):
        self.issues[issue_type][col_name].remove(row_id)
        if len(self.issues[issue_type][col_name]) == 0:
          del self.issues[issue_type][col_name]

