import re
import json
import numpy as np
from collections import defaultdict, Counter
from typing import List
from fuzzywuzzy import fuzz

from backend.utilities.search import normalize_term
from backend.components.metadata import MetaData
from backend.components.engineer import PromptEngineer
from backend.prompts.general import similar_terms_prompt
from backend.assets.ontology import typo_corpus

class Typo(MetaData):
  def __init__(self, table, table_properties, level, api=None):
    super().__init__(table_properties, level, api)
    self.name = 'typo'

    # length of chosen terms values is equal to number of detected issue row ids
    # the remaining issues, this is the job of the tracker.results in the resolution flow
    self.chosen_terms = defaultdict(dict)   # {column_name: { issue_type: [chosen_terms] } }

    # length of chosen term keys is equal to the number of unique chosen terms
    # the correct mapping, but this is actually the mapping slot of the resolution flow
    self.typo_groups = defaultdict(dict)    # {column_name: { chosen_term: [similar_terms]} }

    # the candidate issues, but this can be held by the tracker.conflicts within the detection flow
    self.issues = {
      'misspelled': defaultdict(list),      # the word is not in the dictionary, a classic typo    
      'replacement': defaultdict(list)      # word or phrase exists, but is incorrect based on context
    }

  def detect_issues(self, table):
    self.detect_misspelled(table)           # {column_name: [row_ids]}
    self.detect_syntactic_similarity(table)
    self.detect_semantic_similarity(table)

    self.affected_cols = Counter()
    for issue_type, typo in self.issues.items():
      for col_name, issue_ids in typo.items():
        self.affected_cols[col_name] += len(issue_ids)
    self.prepared = True

  # -------------- access methods ---------------
  def detected_row_ids(self, col_name:str, issue_type:str='') -> List[str]:
    # return the row ids of the detected issues in a specific column
    detected_rows = []
    for itype, typo in self.issues.items():
      if len(issue_type) == 0 or itype == issue_type:
        if col_name in typo:
          detected_rows.extend(typo[col_name])
    return detected_rows

  def detected_issue_types(self, col_name:str) -> List[str]:
    # return the issue types detected in a specific column
    detected_types = []
    for issue_type, typo in self.issues.items():
      if col_name in typo and len(typo[col_name]) > 0:
        detected_types.append(issue_type)
    return detected_types

  # -------------- identify typos ----------------
  def detect_misspelled(self, table):
    # use a corpus of commonly used words to identify misspelled terms
    term_lookup = self.prepare_term_lookup()

    for col_name in table.columns:
      if col_name in self.text_cols or col_name in self.term_cols:
        correct_chosen_terms = []

        for row_id, row_val in table[col_name].items():
          if not isinstance(row_val, str): continue
          if len(row_val) > 128: continue

          if len(row_val.split()) > 1:
            for term in row_val.split():
              if term in term_lookup.keys():
                self.issues['misspelled'][col_name].append(row_id)
                correct_term = term_lookup[term]
                correct_text = row_val.replace(term, correct_term)

                correct_chosen_terms.append(correct_text)
                if correct_text not in self.typo_groups[col_name]:
                  self.typo_groups[col_name][correct_text] = []
                self.typo_groups[col_name][correct_text].append(term)
                break

          elif row_val in term_lookup.keys():
            self.issues['misspelled'][col_name].append(row_id)
            correct_term = term_lookup[row_val]

            correct_chosen_terms.append(correct_term)
            if correct_term not in self.typo_groups[col_name]:
              self.typo_groups[col_name][correct_term] = []
            self.typo_groups[col_name][correct_term].append(row_val)

        self.chosen_terms[col_name]['misspelled'] = correct_chosen_terms

  def prepare_term_lookup(self):
    term_lookup = {}
    for domain, common_typos in typo_corpus.items():
      for correct, incorrect in common_typos.items():
        for term in incorrect:
          term_lookup[term] = correct
    return term_lookup

  def detect_syntactic_similarity(self, tab):
    """ For each column, collect candidate terms to store within clusters.
    These clusters have the normalized term as the key rather than the canonical term. """
    similarity_threshold = {'low': 70, 'medium': 80, 'high': 90}
    self.clusters_by_column = {}

    for col_name in self.term_cols + self.text_cols:
      if tab[col_name].nunique() > 128: continue
      column_subtype = self.tab_properties[col_name]['subtype']
      if column_subtype in ['email', 'phone', 'name']: continue

      unique_terms = tab[col_name].unique()
      col_cluster = defaultdict(set)    # {normalized_term: [similar_terms]}
      token_groups = []

      # Group similar terms with fuzzywuzzy based on character overlap
      for raw_term in unique_terms:
        if not isinstance(raw_term, str): continue
        normalized, current_tokens = normalize_term(raw_term)
        if len(normalized) > 128: continue
        token_groups.append((normalized, current_tokens, raw_term))

        found = False
        for key in col_cluster.keys():
          if fuzz.ratio(normalized, key) > similarity_threshold[self.level]:
            col_cluster[key].add(raw_term)
            found = True
            break
        if not found:
          col_cluster[normalized].add(raw_term)  # store the original term

      # Group similar terms based on token overlap
      for i in range(len(token_groups) - 1):
        norm_term1, token_group1, raw_term = token_groups[i]
        # only consider the remaining terms
        for j in range(i + 1, len(token_groups)):
          norm_term2, token_group2, _ = token_groups[j]
          if self.token_group_overlap(token_group1, token_group2):
            col_cluster[norm_term2].add(raw_term)
      
      # Remove clusters with only one term and convert the set to a list
      final_clusters = {}
      for key in list(col_cluster.keys()):
        cluster_set = col_cluster[key]
        if len(cluster_set) > 1:
          final_clusters[key] = list(cluster_set)
      self.clusters_by_column[col_name] = final_clusters

  def token_group_overlap(self, group1, group2):
    if len(group1) < len(group2):
      shorter, longer = group1, group2
    else:
      shorter, longer = group2, group1

    # trim tokens that are too short
    shorter = [token for token in shorter if len(token) >= 3]
    # find the number of tokens that overlap
    matches = [token for token in shorter if token in longer]
    num_matches = len(matches)

    # if all tokens match and this makes up at least half of the larger group
    overlap = False
    if num_matches > 0 and num_matches == len(shorter):
      if (float(num_matches) / len(longer)) >= 0.5:
        overlap = True
    return overlap

  def detect_semantic_similarity(self, tab):
    """ Use API to check for semantically related terms that should be merged, going beyond spelling errors
    First key is col_name, second key is chosen term, values are a list of sim_terms  """
    self.groups_by_column = {}

    for col in self.term_cols:
      # only check columns with a 2 < n =< 16 of unique terms (ie. status or category subtype)
      unique_terms = tab[col].dropna().unique()
      if len(unique_terms) <= 2: continue

      # use prompt with API to find similar terms
      unique_term_str = ', '.join(unique_terms)
      prompt = similar_terms_prompt.format(col_name=col, unique_terms=unique_term_str)
      prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

      if len(prediction) > 0 and prediction != 'error':
        self.groups_by_column[col] = prediction
      else:
        print(f"Error finding similar terms for {col}")

  def merge_clusters_and_groups(self, table):
    """ Since the clusters have normalized terms as the key, we first need to extract the canonical terms
    from the values-list of each cluster. Then, we can merge the clusters with the groups. """
    for col_name in table.columns:

      if col_name not in self.clusters_by_column: continue
      cluster = self.clusters_by_column[col_name]
      group = self.groups_by_column.get(col_name, {})

      # assign each cluster to a chosen term
      for terms_list in cluster.values():
        match = False
        for chosen, candidates in group.items():
          if any(term in candidates for term in terms_list):
            match = True  # combine the terms into the existing group
            group[chosen].extend(terms_list)
            break
        if not match:  # create a new group and assign the first term as the chosen term
          group[terms_list[0]] = terms_list

      # deduplicate the similar terms within each group
      for chosen, similar_terms in group.items():
        group[chosen] = list(set(similar_terms))

      # build reverse mapping from similar_terms to chosen_term
      reverse_mapping = {}
      for chosen, similar_terms in group.items():
        for sim_term in similar_terms:   # duplicate terms will automatically get collapsed to a single chosen
          reverse_mapping[sim_term] = chosen

      # Set row ids and row_text for replacement typos
      correct_chosen_terms = []
      for row_id, row_val in table[col_name].items():
        if row_val in reverse_mapping.keys():
          self.issues['replacement'][col_name].append(row_id)
          correct_chosen_terms.append(reverse_mapping[row_val])

      self.chosen_terms[col_name]['replacement'] = correct_chosen_terms
      self.typo_groups[col_name] = group

  def print_to_command_line(self, concerns, itype, table):
    issue_rows = []
    for col_issues in concerns.values():
      issue_rows.extend(col_issues)
    print(f"{len(issue_rows)} {itype} found in {self.table_name}")

    if issue_rows:
      for grouping in issue_rows:
        print(grouping)

  def naive_column_assignment(self, df):
    # determine the column types of the dataframe
    numeric_cols, textual_cols, date_cols = [], [], []
    index_keywords = [r'\bindex\b', r'_id$',
                      r'\bid$']  # regex patterns to match 'id' as a standalone word or at the end of a word

    for column in df.columns:
      if any(re.search(keyword, column, re.I) for keyword in index_keywords):
        continue
      if df[column].dtypes == np.object:
        samples = df[column].sample(1000) if len(df[column]) > 1000 else df[column]
        textual_cols.append(column)
      else:
        textual_cols.append(column)

    return numeric_cols, textual_cols, date_cols

  @staticmethod
  def type_to_nl(count, prefix='article'):
    # Converts a group of similar terms to natural language
    if prefix == 'article':
      result = "a " if count == 1 else ""
    elif prefix == 'digit':
      result = f"{count} "
    else:
      result = ""

    result += "group " if count == 1 else "groups "
    result += "of similar terms"
    return result