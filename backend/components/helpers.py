import os
os.environ['NLTK_DATA'] = os.path.join(os.path.dirname(__file__), 'utils', 'nltk_data')

import re
import pandas as pd
import numpy as np
import chardet
from collections import defaultdict, Counter
from datetime import datetime as dt
from textblob import Word

import random
import duckdb
import functools
from dateutil import parser
from datetime import timezone

from backend.db import get_db
from backend.assets.ontology import error_responses, date_mappings, date_formats, default_limit, NA_string
from backend.assets.descriptions import preloaded_descriptions
from backend.components.frame import Frame
from backend.components.engineer import PromptEngineer
from backend.components.metadata.typechecks import TypeCheck
from database.tables import UserDataSource

def handle_breaking_errors(func):
  def wrapper(self, utterance, actions):
    # returns the full stacktrace
    if self.debug:
      return func(self, utterance, actions)

    try:
      return func(self, utterance, actions)
    except Exception as err:
      print(f"ERROR: {err}")
      error_message = random.choice(error_responses)
      response = {'message': error_message, 'actions': [], 'interaction': None, 'frame': None,
                'uncertainity': {'general': True, 'partial': False, 'specific': False, 'confirmation': False}}
      return response, "error"
  return wrapper

class StorageDB(object):
  """ Usage:
  storage = StorageDB(args)
  result = storage.execute("SELECT * FROM orders WHERE price > 80;")
  for row in result:
    print(row)
  storage.insert_item('INSERT INTO users (name) VALUES (:name)', {'name': 'John'})

  user = User(username='john')
  storage.session.add(user)
  storage.session.commit()

  messages = session.query(Message).filter(Message.user == user).all()
  """
  def __init__(self, args):
    self.verbose = args.verbose
    self._session = None

  def execute(self, query):
    """Execute a query with a fresh session"""
    session = get_db()
    try:
      result = session.execute(query)
      return result
    except Exception as e:
      session.rollback()
      raise
    finally:
      session.close()

  def insert_item(self, data):
    """Insert an item with a fresh session"""
    session = get_db()
    try:
      session.add(data)
      session.commit()
    except Exception as e:
      session.rollback()
      raise
    finally:
      session.close()
      
  def close(self):
    """Close the current session if it exists"""
    if self._session is not None:
      self._session.close()
      self._session = None

  def get_user_sources(self, user_id):
    session = get_db()
    try:
      return session.query(UserDataSource).filter_by(user_id=user_id).all()
    finally:
      session.close()

  def get_sources_by_ids(self, ids):
    session = get_db()
    try:
      return session.query(UserDataSource).filter(UserDataSource.id.in_(ids)).all()
    finally:
      session.close()
      
class MemoryDB(object):
  """ Usage:
  memory = MemoryDB(args.verbose, "shoe store")
  memory.execute("SELECT price > 90 FROM orders;")
  all_data = memory.fetch_all()
  """
  def __init__(self, args, api):
    self.verbose = args.verbose       
    self.debug = args.debug       
    self.api = api

    self.db_connection = duckdb.connect(':memory:')
    self.shadow = ShadowDB()
    self.init_tables()

  def init_tables(self):
    self.tables = {}
    self.description = {}
    self.primary_keys = {}
    self.engineers = {}

  def register_default_data(self, dir_name, table_names):
    self.init_tables()
    table_data = {}
    for tab_name in table_names:
      table_path = os.path.join("database", "storage", dir_name, f"{tab_name}.csv")
      rawdata = open(table_path, 'rb').read()
      encoding_type = chardet.detect(rawdata)['encoding']
      table_data[tab_name] = pd.read_csv(table_path, encoding=encoding_type) # we know the file is CSV because it's our own preset data
    return self.register_tables(dir_name, table_data)

  def register_new_data(self, ss_name, ss_goal, ss_data):
    self.init_tables()
    if " to " in ss_goal:
      parts = ss_goal.split(" to ")
      ss_goal = " to ".join(parts[1:])
    ss_goal = ss_goal[:-1] if ss_goal.endswith(".") else ss_goal
    self.description['goal'] = ss_goal.strip()
    return self.register_tables(ss_name, ss_data)

  def register_tables(self, spreadsheet_name, spreadsheet_data):
    # All missing value in MemoryDB is NaN
    properties = {}
    for tab_name, table in spreadsheet_data.items():
      print("Registering table:", tab_name)
      table = self.clean_table(tab_name, table)  # All blank cell are NaNs
      tab_properties = {}
      for col_name, column in table.items():
        col_props = TypeCheck.build_properties(col_name, column)
        column, col_props = self.shadow.convert_to_type(column, col_props)  # all NaN are remained
        tab_properties[col_name] = col_props
        table[col_name] = column

      self.engineers[tab_name] = PromptEngineer(tab_properties, tab_name, self.api)
      self.tables[tab_name] = table
      properties[tab_name] = self.set_primary_keys(tab_name, tab_properties, table)
      self.complete_registration(tab_name, spreadsheet_name)
    return properties

  def handle_duplicate_columns(self, table):
    """Renames duplicate columns by appending _2, _3, etc."""
    counts = defaultdict(int)
    new_columns = []

    for column in table.columns:
      counts[column] += 1
      if counts[column] > 1:
        new_name = f"{column}_{counts[column]}"
        new_columns.append(new_name)
      else:
        new_columns.append(column)

    table.columns = new_columns
    return table

  def clean_table(self, tab_name, table):
    # Handle duplicate column names first
    table = self.handle_duplicate_columns(table)

    # remove blank columns
    blank_col_index = []
    for col in table.columns:
      if table[col].isna().all():
        blank_col_index.append(col)
    if len(blank_col_index) > 0:
      table.drop(columns=blank_col_index, inplace=True)

    # There must be a column name for each column
    for col_name in table.columns:
      if 'Unnamed:' in col_name:
        table = table.drop(columns=[col_name])

    # remove all the rows that are blank 
    if len(table.isna().all(axis=1)) > 0:
      table.dropna(how='all', inplace=True)
    return table

  def update_table(self, tab_name, tab_schema, changes):
    # Given a table_update item with row_id and col_name, update to the new value
    null_mapper = {'int64': pd.NA, 'float64': np.nan, 'datetime64[ns]': pd.NaT, '<M8[ns]': pd.NaT, 'object': ''}
    num_changes = 0
    needs_registration = False

    for change in changes:
      try:
        value = int(change.newValue) if change.newValue.isdigit() else change.newValue
        datatype = str(self.tables[tab_name][change.col].dtype)

        if value == '':
          self.tables[tab_name].loc[change.row, change.col] = null_mapper.get(datatype, value)

        else:
          col_schema = tab_schema.get_type_info(change.col)
          col_format = col_schema['supplement'].get(col_schema['subtype'], None)
          safe_transaction = True

          if col_format:
            if datatype in ['datetime64[ns]', '<M8[ns]']:
              parsed = pd.to_datetime(value, errors='coerce')
              if pd.isna(parsed):
                safe_transaction = False
              elif value != parsed.strftime(col_format):
                safe_transaction = False
            elif datatype in ['float64', 'int64']:
              parsed = pd.to_numeric(value, errors='coerce')
              if pd.isna(parsed):
                safe_transaction = False

          if safe_transaction:
            self.tables[tab_name].loc[change.row, change.col] = value
          else:  # rather than updating the value (which changes the dtype of the column)
            # set the row in the main table as null, and
            self.tables[tab_name].loc[change.row, change.col] = null_mapper[datatype]
            # store the new value as a problem the shadow table
            self.shadow.problems[change.col][change.row] = value

        if datatype != 'object':  # object columns already update themselves automatically
          needs_registration = True
        num_changes += 1
      except Exception as exp:
        error_msg = f"Encountered {exp}; Completed {num_changes}/{len(changes)} changes"
        return False, error_msg

    if needs_registration:
      self.db_connection.register(tab_name, self.tables[tab_name])
    success_msg = f"Successfully completed {num_changes} changes"
    if self.verbose:
      print("  " + success_msg)
    return True, success_msg

  def set_primary_keys(self, tab_name, tab_properties, table):
    candidates = [col for col in table.columns if tab_properties[col]['subtype'] in ['id', 'whole']]
    candidates.extend(table.columns[:3])   # primary keys are often in the first three columns
    top_candidate = self.check_for_pkey(set(candidates), tab_name, tab_properties)

    if top_candidate:
      pkey = top_candidate
      # table.set_index(pkey, inplace=False)   purposely skipped to prevent the column from being removed
    else:
      pkey = self.make_primary_key(tab_name)
      table.reset_index(inplace=True)
      table.rename(columns={'index': pkey}, inplace=True)
      table.set_index(pkey, inplace=True)
    self.primary_keys[tab_name] = pkey

    if pkey in tab_properties:
      if tab_properties[pkey]['subtype'] in ['whole', 'general', 'category']:
        tab_properties[pkey]['type'] = 'id'
        tab_properties[pkey]['subtype'] = 'unique'
    else:
      tab_properties[pkey] = {'col_name': pkey, 'total': len(table), 'type': 'unique', 'subtype': 'id'}
    return tab_properties

  def check_for_pkey(self, candidates, tab_name, tab_properties, minimum_score=0.8):
    """ returns most likely primary key from a list of candidates if any pass the minimum score """
    top_candidate = None
    for cand_col in candidates:
      properties = tab_properties[cand_col]
      column = self.tables[tab_name][cand_col]
      total_score = 0

      # amount of overlap with the table name
      _, overlap_score = PromptEngineer.token_overlap(cand_col, tab_name)
      total_score += overlap_score * 0.3

      # special keywords of 'key' or 'id'
      if cand_col.lower().endswith('key') or cand_col.lower().endswith('id'):
        total_score += 0.05

      # percentage of unique values
      unique_score = column.nunique() / len(column)
      total_score += unique_score * 0.35

      # percentage of non-empty values
      empty_count = 0
      for empty_subtype in ['null', 'missing', 'default']:
        empty_count += properties['supplement'][empty_subtype]
      empty_score = 1 - (empty_count / len(column))
      total_score += empty_score * 0.2

      # whether all values are roughly the same size
      sizes = column.astype(str).apply(len)
      size_range = sizes.max() - sizes.min()
      size_diff = 3 - size_range
      roughly_same_size = size_diff > 0
      if roughly_same_size:
        total_score += 0.1 * size_diff

      # give a boost if the column is an id subtype
      if properties['subtype'] == 'id':
        total_score *= 1.15

      # giant boost if the values are sequential integers
      series = column.dropna().head(32)
      if (series.dtype in ['int64', 'Int64'] and series.diff().dropna().eq(1).all()):
        total_score *= 1.2

      if total_score > minimum_score:
        top_candidate = cand_col
        minimum_score = total_score

    return top_candidate

  @staticmethod
  def make_primary_key(table_name):
    if '_' in table_name:
      # split snake case into words
      parts = table_name.split('_')
    else:
      # split camel case into words
      parts = re.findall(r'[A-Z](?:[a-z]+|[A-Z]*(?=[A-Z]|$))', table_name)
    parts = [part.lower() for part in parts if len(part) > 0]

    if len(parts) >= 3:
      primary_key = "".join([part[0] for part in parts[:3]]) + "_id"
    elif len(table_name) > 8:
      stemmed = Word(table_name).stem()
      primary_key = (stemmed if stemmed else table_name[:3]) + "_id"
    else:
      primary_key = table_name + "_id"
    return primary_key

  def complete_registration(self, tab_name, db_name=''):
    if db_name in preloaded_descriptions and tab_name in preloaded_descriptions[db_name]:
      self.description['goal'] = preloaded_descriptions[db_name]['goal']
      self.description[tab_name] = preloaded_descriptions[db_name][tab_name]
    else:
      current_table, current_pkey = self.tables[tab_name], self.primary_keys[tab_name]
      engineer = self.engineers[tab_name]
      updated_description = engineer.compile_description(current_table, current_pkey)
      self.description[tab_name] = updated_description

    # register the table within DuckDB
    if len(db_name) > 0:
      self.db_name = db_name
    self.db_connection.register(tab_name, self.tables[tab_name])

  def register_new_table(self, context, tab_name, old_tab_names, table_content):
    # Create a new primary key for the new table
    pkey = self.make_primary_key(tab_name)
    table_content.reset_index(inplace=True)
    table_content.rename(columns={'index': pkey}, inplace=True)
    table_content.set_index(pkey, inplace=True)
    self.primary_keys[tab_name] = pkey

    # Set a description and register it within DuckDB
    if context.actions_include(['CREATION'], 'User'):
      tab_desc = f"Materialize a permanent view called {tab_name} from a temporary table."
    elif context.actions_include(['PIVOT'], 'Agent'):
      tab_desc = f"Pivot table named {tab_name} created when grouping by {table_content.columns[1]}"
    else:
      old_tab_string = ' and '.join(old_tab_names)
      tab_desc = f"Dynamically created table for {tab_name} that is the result of merging the {old_tab_string} tables."
    self.description[tab_name] = tab_desc
    self.db_connection.register(tab_name, table_content)

  def execute(self, query_str):
    # result = self.db_connection.execute(query_str).fetchdf()
    result = self.db_connection.query(query_str).df()
    return result

  def download_all(self, schema):
    # A generator that returns tuples of table name and table info for downloading
    for tab_name, table in self.tables.items():
      table_schema = schema[tab_name]
      output_df = table.where(pd.notnull(table), None)
      for col_name in output_df.columns:
        props = table_schema.get_type_info(col_name)
        output_column = self.shadow.display_as_type(output_df, **props)
        output_df[col_name] = output_column.replace(NA_string, '')
      yield (tab_name, output_df)

  def download_one(self, tab_name, schema):
    # Returns a single displayed table for downloading
    table_schema = schema[tab_name]
    table = self.tables[tab_name]
    output_df = table.where(pd.notnull(table), None)

    for col_name in output_df.columns:
      props = table_schema.get_type_info(col_name)
      output_column = self.shadow.display_as_type(output_df, **props)
      output_df[col_name] = output_column.replace(NA_string, '')
    return output_df

  def fetch_one(self, tab_name, schema, special_tabs, start=0, end=default_limit):
    # Fetches rows from a single table in the database
    # Always quote table names to handle numeric or special character table names
    table_ref = f'"{tab_name}"'
    # We go from 0-256, 256-512, 512-1024, 1024-2048, 2048-3072, 3072-4096, etc.
    limit = end - start if start > 0 else end
    offset = f"OFFSET {start} " if start > 0 else ""
    fetch_query = f"SELECT * FROM {table_ref} {offset}LIMIT {limit};"

    df = self.db_connection.query(fetch_query).df()
    db_output = df.where(pd.notnull(df), None)

    table_schema = schema[tab_name]
    for col_name in db_output.columns:
      try:
        props = table_schema.get_type_info(col_name)
        db_output[col_name] = self.shadow.display_as_type(db_output, **props)
      except Exception as err: # the new created primary key is not in the schema
        print(f"Error when attempting to display column {col_name} from shadow:", err)
        continue

    frame = Frame(tab_name)
    frame.set_data(db_output, fetch_query)
    frame.primary_key = self.primary_keys.get(tab_name, None)
    return frame
    
  def delete_table(self, tab_name):
    # Validate that the object exists in our tracking dictionary
    if tab_name not in self.tables:
      error_message = f"Table '{tab_name}' does not exist in the database"
      return {"success": False, "message": error_message}
    
    try:
      # First try to drop it as a view, then as a table if that fails
      try:
        # Try to drop as a view first
        self.db_connection.execute(f"DROP VIEW IF EXISTS \"{tab_name}\"")
        object_type = "view"
      except Exception as view_err:
        if "Catalog Error: Existing object" in str(view_err):
          # If it's a catalog error about object type, try dropping as a table
          self.db_connection.execute(f"DROP TABLE IF EXISTS \"{tab_name}\"")
          object_type = "table"
        else:
          # Re-raise any other error
          raise view_err
      
      # Remove table information from all tracking dictionaries
      if tab_name in self.tables:
        del self.tables[tab_name]
      if tab_name in self.description:
        del self.description[tab_name]
      if tab_name in self.primary_keys:
        del self.primary_keys[tab_name]
      if tab_name in self.engineers:
        del self.engineers[tab_name]
        
      # Clean up related entries in the shadow database
      self.shadow.delete_table_data(tab_name)
      
      success_message = f"Successfully deleted {object_type} '{tab_name}'"
      return {"success": True, "message": success_message}
      
    except Exception as exp:
      error_message = f"Failed to delete {tab_name}: {exp}"
      return {"success": False, "message": error_message}

def null_safety_decorator(func):
  """Decorator to catch exceptions due to NaN values in a DataFrame column and handle them."""
  @functools.wraps(func)
  def wrapper_to_catch_null(self, column, *args, **kwargs):
    try:
      return func(self, column, *args, **kwargs)
    except AttributeError as err:
      print(f"An exception occurred when converting columns: {err}")
      if isinstance(column, pd.Series):
        column = column.fillna(NA_string)
      return column
  return wrapper_to_catch_null

class ShadowDB(object):
  """ Potentially holds onto extra computed columns that are not in storage or memory 
  For example, given a table with 'date', we can create columns for week or quarter,
  Or given a table with 'revenue' and 'cost', we can create a column for 'profit',
  Or given a table with 'month', 'day', and 'year', we can create a column for date

  Currently holds helper functions for converting from ShadowTab to DisplayTab, and vice versa
  """
  def __init__(self):
    self.digit_to_time = self.reverse_date_mapping()
    # key is a column name, value is pandas series of the column in string format
    self.table = {}
    # key is a column name, value is {index: original_value} dict, which references a converted cell in the MemoryDB
    self.converted = {}
    # key is a column name, value is {index: original_value} dict, which references a NaN cell in MemoryDB
    self.problems = defaultdict(dict)

  def delete_table_data(self, tab_name):
    """
    Deletes all shadow data associated with the specified table.
    
    This method removes entries from problems, table, and converted dictionaries
    that are associated with the given table name.
    
    Args:
        tab_name (str): The name of the table to delete data for
        
    Returns:
        list: The list of column names that were removed
    """
    column_names = []
    
    # Clean up entries in problems dictionary
    for col_name in list(self.problems.keys()):
      if col_name.startswith(tab_name + '.') or col_name == tab_name:
        column_names.append(col_name)
        del self.problems[col_name]
    
    # Clean up entries in table dictionary
    for col_name in list(self.table.keys()):
      if col_name.startswith(tab_name + '.') or col_name == tab_name:
        del self.table[col_name]
    
    # Clean up entries in converted dictionary
    for col_name in list(self.converted.keys()):
      if col_name.startswith(tab_name + '.') or col_name == tab_name:
        del self.converted[col_name]
        
    return column_names

  def reverse_date_mapping(self):
    digit_to_time = defaultdict(dict)

    for subtype, mapping in date_mappings.items():
      for form, time_to_digit in mapping.items():
        time_list = ['added_for_zero_indexing']
        time_list.extend(time_to_digit.keys())
        digit_to_time[subtype][form] = time_list
    return digit_to_time

  def convert_to_boolean(self, column, properties):
    bool_map = {
      'True': True, 'true': True, 'TRUE': True, 'T': True, 1: True,
      'False': False, 'false': False, 'FALSE': False, 'F': False, 0: False
    }
    mapped_booleans = column.map(bool_map)
    problematic_rows = ~mapped_booleans.isin([True, False])

    candidate_problems = column[problematic_rows].to_dict()
    if len(candidate_problems) > 0:
      self.problems[properties['col_name']] = candidate_problems
    return mapped_booleans

  def convert_to_id(self, column, properties):
    samples = column.sample(min(1024, len(column)), random_state=1)
    sample_numeric = pd.to_numeric(samples, errors='coerce')
    if sample_numeric.isna().any():
      return column
    sample_integers = sample_numeric.apply(lambda x: float(x).is_integer())

    if sample_integers.all():
      original_column = column.copy()
      parsed_ids = pd.to_numeric(column, errors='coerce').astype('Int64')

      problematic_rows = parsed_ids.isna()
      candidate_problems = original_column[problematic_rows].to_dict()
      if len(candidate_problems) > 0:
        self.problems[properties['col_name']] = candidate_problems
      return parsed_ids
    else:
      return column

  def convert_to_currency(self, column, properties):
    original_column = column.copy()
    if column.dtype == 'object':
      # [-+] captures the negative sign
      # \d{1,3} is one to three digits of values
      # (?:,\d{3}) is 0 or more groups of 3-digits with commas
      # (?:\.\d+) optionally, capture the information after a decimal point
      # %? optionally, capture the percentage sign
      column = column.astype(str).str.extract(r'([-+]?\d{1,3}(?:,\d{3})*(?:\.\d+)?%?|[-+]?\d+\.?\d*%?)')[0]
      column = column.astype(str).str.replace(',', '')

    parsed_currencies = pd.to_numeric(column, errors='coerce')
    problematic_rows = parsed_currencies.isna()
    candidate_problems = original_column[problematic_rows].to_dict()
    if len(candidate_problems) > 0:
      self.problems[properties['col_name']] = candidate_problems
    return parsed_currencies, properties

  def convert_to_percent(self, column, properties):
    # Converts possible strings into floats, e.g. "100%" -> 1.0
    original_column = column.copy()
    if column.dtype == 'object':
      column = column.astype(str).str.replace('%', '')

    parsed_percentages = pd.to_numeric(column, errors='coerce')
    problematic_rows = parsed_percentages.isna()
    candidate_problems = original_column[problematic_rows].to_dict()
    if len(candidate_problems) > 0:
      self.problems[properties['col_name']] = candidate_problems

    cleaned_rows = parsed_percentages.dropna()
    samples = cleaned_rows.sample(min(128, len(cleaned_rows)), random_state=1)
    within_range = samples.mean() > -1 and samples.mean() < 1 and samples.max() < 10
    return parsed_percentages if within_range else parsed_percentages / 100

  def convert_to_decimal(self, column, properties):
    cleaned_column = column.astype(str).str.replace(',', '').str.replace(' ', '')
    parsed_decimals = pd.to_numeric(cleaned_column, errors='coerce')
    problematic_rows = parsed_decimals.isna()
    self.problems[properties['col_name']] = column[problematic_rows].to_dict()
    return parsed_decimals

  def convert_to_whole(self, column, properties):
    cleaned_column = column.astype(str).str.replace(',', '').str.replace(' ', '')
    try:
      parsed_wholes = pd.to_numeric(cleaned_column, errors='coerce').astype('Int64')
    except TypeError:
      parsed_wholes = pd.to_numeric(cleaned_column, errors='coerce').round().astype(pd.Int64Dtype())

    problematic_rows = parsed_wholes.isna()
    self.problems[properties['col_name']] = column[problematic_rows].to_dict()
    return parsed_wholes

  @staticmethod
  def supplementary_form_details(column, properties, subtype_name):
    mwq_formats = {'week': ('%a', '%A'), 'month': ('%b', '%B'), 'quarter': ('%o', '%O')}
    abbreviated_form, full_form = mwq_formats[subtype_name]
    sample = column.sample(min(len(column), 128)).astype(str).str.lower()

    format_counter = Counter()
    for row in sample:
      if row.isnumeric():
        format_counter['identity'] += 1
      elif len(str(row)) > 3:
        format_counter[full_form] += 1
      else:
        format_counter[abbreviated_form] += 1

    most_likely_format = format_counter.most_common(1)[0][0]
    properties['supplement'][subtype_name] = most_likely_format
    return properties

  def convert_month_week_quarter(self, column, properties):
    original_column = column.copy()
    column = column.astype(str).str.lower()
    subtype_name = properties['subtype']
    properties = self.supplementary_form_details(column, properties, subtype_name)

    if subtype_name == 'quarter' and properties['supplement'][subtype_name] == '%o':
      # Remove non-numeric characters from "Q1", "2nd", "q-3", "4th"
      column = column.astype(str).str.replace(r'\D+', '').astype('Int64')
    else:
      if properties['supplement'][subtype_name] == 'identity':
        parsed_mwq = pd.to_numeric(column, errors='coerce')
      else:   # store as integers rather than complex datetime objects
        time_to_digit = date_mappings[subtype_name][properties['supplement'][subtype_name]]
        parsed_mwq = column.map(time_to_digit).fillna(pd.to_numeric(column, errors='coerce'))

    problematic_rows = parsed_mwq.isna()
    self.problems[properties['col_name']] = original_column[problematic_rows].to_dict()
    return parsed_mwq, properties

  def convert_to_date(self, column, properties):
    column = column.apply(lambda x: re.sub(r'(?<=\d)(st|nd|rd|th)', '', str(x)))
    column = column.apply(lambda x: x[:-6].strip() if x.endswith('+00:00') else x)
    column = column.apply(lambda x: x[:-9].strip() if x.endswith('T00:00:00') else x)
    column = column.apply(lambda x: x[:-8].strip() if x.endswith('00:00:00') else x)

    parsed_dates = pd.to_datetime(column, format=properties['supplement']['date'], errors='coerce')
    problematic_rows = parsed_dates.isna()
    self.problems[properties['col_name']] = column[problematic_rows].to_dict()
    return parsed_dates, properties

  def convert_to_time(self, column, properties):
    original_column = column.copy()
    column = column.astype(str).str.lower()

    parsed_times = pd.to_datetime(column, format=properties['supplement']['time'], errors='coerce').dt.time
    problematic_rows = parsed_times.isna()
    self.problems[properties['col_name']] = original_column[problematic_rows].to_dict()
    return parsed_times, properties

  def convert_to_timestamp(self, column, properties):
    """
    exact=True is stricter and requires an exact match to be converted to a timestamp
    exact=False is more lenient and will convert to a timestamp if it can find any match
    For example, '2023-02-01 12:30' with exact=True is considered invalid because it's missing the seconds component
    But with exact=False, pandas fills in the missing seconds with '00' and successfully parses the datetime
    """
    original_column = column.copy()
    parsed_timestamps = pd.Series(index=column.index, dtype='datetime64[ns]')
    
    # Define timezone info for common abbreviations
    # Hours offset from UTC
    tzinfos = {
        'PDT': -7*3600,  # Pacific Daylight Time (UTC-7)
        'PST': -8*3600,  # Pacific Standard Time (UTC-8)
        'EST': -5*3600,  # Eastern Standard Time (UTC-5)
        'EDT': -4*3600,  # Eastern Daylight Time (UTC-4)
        'CST': -6*3600,  # Central Standard Time (UTC-6)
        'CDT': -5*3600,  # Central Daylight Time (UTC-5)
        'MST': -7*3600,  # Mountain Standard Time (UTC-7)
        'MDT': -6*3600,  # Mountain Daylight Time (UTC-6)
        'JST': 9*3600,   # Japan Standard Time (UTC+9)
        'AEST': 10*3600, # Australian Eastern Standard Time (UTC+10)
        'AEDT': 11*3600, # Australian Eastern Daylight Time (UTC+11)
        'GMT': 0,        # Greenwich Mean Time (UTC+0)
        'UTC': 0,        # Coordinated Universal Time (UTC+0)
        'Z': 0           # Zulu Time (UTC+0)
    }
    
    for idx, val in column.items():
        if pd.isna(val):
            continue
        
        try:
            # Use dateutil with explicit timezone info
            dt = parser.parse(str(val), tzinfos=tzinfos)
            
            # Convert to pandas timestamp without timezone info
            # (store as naive UTC timestamp to avoid pandas timezone issues)
            if dt.tzinfo is not None:
                dt = dt.astimezone(timezone.utc).replace(tzinfo=None)
                
            parsed_timestamps[idx] = dt
        except Exception as e:
            # Keep track of failures but don't break the process
            if hasattr(self, 'verbose') and self.verbose:
                print(f"Error parsing timestamp '{val}': {e}")
    
    # Track problematic rows
    problematic_rows = parsed_timestamps.isna()
    self.problems[properties['col_name']] = original_column[problematic_rows].to_dict()
    
    return parsed_timestamps

  def convert_dates_and_times(self, column, properties):
    if properties['subtype'] in ['year', 'day', 'hour', 'minute', 'second']:
      column = column.astype('Int64')
    elif properties['subtype'] in ['month', 'week', 'quarter']:
      column, properties = self.convert_month_week_quarter(column, properties)
    elif properties['subtype'] == 'date':
      column, properties = self.convert_to_date(column, properties)
    elif properties['subtype'] == 'time':
      column, properties = self.convert_to_time(column, properties)
    elif properties['subtype'] == 'timestamp':
      column = self.convert_to_timestamp(column, properties)
    return column, properties

  def convert_to_type(self, column, properties):
    already_nan = column.index[column.isna()].tolist()
    self.store_uncategorized_column(column, properties)

    if properties['subtype'] == 'id':
      column = self.convert_to_id(column, properties)
    elif properties['subtype'] == 'boolean':
      column = self.convert_to_boolean(column, properties)
    elif properties['subtype'] == 'currency':
      column, properties = self.convert_to_currency(column, properties)
    elif properties['subtype'] == 'percent':
      column = self.convert_to_percent(column, properties)
    elif properties['subtype'] == 'decimal':
      column = self.convert_to_decimal(column, properties)
    elif properties['subtype'] == 'whole':
      column = self.convert_to_whole(column, properties)
    elif properties['type'] == 'datetime':
      column, properties = self.convert_dates_and_times(column, properties)

    # no need to track NaN values in problems_dict
    converted_list = ['id', 'boolean', 'currency', 'percent', 'decimal', 'whole']
    if properties['subtype'] in converted_list or properties['type'] == 'datetime':
      for index in already_nan:
        self.problems[properties['col_name']].pop(index, None)
    return column, properties

  def store_uncategorized_column(self, column, properties):
    # hold onto the raw format for future processing
    unknown_datatype = properties['type'] == 'unknown'
    unknown_subtype = properties['subtype'] == 'unknown'
    potential_probs = len(properties['potential_problem']) > 0
    if unknown_datatype or unknown_subtype or potential_probs:
      self.table[properties['col_name']] = column

  @null_safety_decorator
  def display_as_boolean(self, df, col_name):
    def get_display_value(row):
      if pd.isna(row[col_name]):
        return self.problems[col_name].get(row.name, NA_string)
      return row[col_name]

    return df.apply(get_display_value, axis=1)

  @null_safety_decorator
  def display_as_id(self, df, col_name):
    def get_display_value(row):
      if pd.isna(row[col_name]):
        return self.problems[col_name].get(row.name, NA_string)
      return row[col_name]

    return df.apply(get_display_value, axis=1)

  @null_safety_decorator
  def display_as_currency(self, df, col_name, supplement):
    currency_sign = supplement.get('currency', '$')
    def get_display_value(row):
      if pd.isna(row[col_name]):
        return self.problems[col_name].get(row.name, NA_string)
      return f"{currency_sign}{row[col_name]:,.2f}"

    return df.apply(get_display_value, axis=1)

  @null_safety_decorator
  def display_as_percent(self, df, col_name):
    def get_display_value(row):
      if pd.isna(row[col_name]):
        return self.problems[col_name].get(row.name, NA_string)
      return f"{row[col_name]:.2%}"

    return df.apply(get_display_value, axis=1)

  @null_safety_decorator
  def display_as_decimal(self, df, col_name):
    def get_display_value(row):
      if pd.isna(row[col_name]):
        return self.problems[col_name].get(row.name, NA_string)
      return f"{row[col_name]:.3f}"

    return df.apply(get_display_value, axis=1)

  @null_safety_decorator
  def display_as_phone(self, df, col_name, supplement):
    curr_supplement = supplement.get('phone', None)
    if curr_supplement and len(curr_supplement.keys()) == 3:
      area_code = list(curr_supplement.keys())[0]
      prefix = f'({area_code}) '
    else:
      prefix = ''

    def get_display_value(row):
      digits = row[col_name]
      if pd.isna(digits):
        return self.problems[col_name].get(row.name, NA_string)
      return f"{prefix}{digits[:3]}-{digits[3:]}"

    return df.apply(get_display_value, axis=1)

  @null_safety_decorator
  def display_as_time(self, df, col_name, supplement):
    time_format = supplement.get('time', None)
    def get_display_value(row):
      if pd.isna(row[col_name]):
        return self.problems[col_name].get(row.name, NA_string)
      elif time_format:
        return row[col_name].strftime(time_format)
      else:
        return row[col_name].dt.time.astype(str)

    return df.apply(get_display_value, axis=1)

  @null_safety_decorator
  def display_as_date(self, df, col_name, supplement):
    date_format = supplement.get('date', '%Y-%m-%d')
    def get_display_value(row):
      if pd.isna(row[col_name]):
        return self.problems[col_name].get(row.name, NA_string)
      return row[col_name].strftime(date_format)

    return df.apply(get_display_value, axis=1)

  @null_safety_decorator
  def display_as_month(self, df, col_name, supplement):
    month_abbreviations = [NA_string, 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    month_full = [NA_string, 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
    month_format = supplement.get('month', '%b')

    if df[col_name].dtype == 'datetime64[ns]':
      if month_format == 'identity':
        display_column = df[col_name].dt.month
      else:
        display_column = df[col_name].dt.strftime(month_format)
    else:
      # Check if values are already in month_abbreviations or month_full
      display_column = df[col_name].astype(str).copy()
      # Only convert numeric-looking values
      mask_numeric = df[col_name].astype(str).str.match(r'^\d+$')
      
      if mask_numeric.any():
        numeric_df = pd.to_numeric(df[col_name][mask_numeric], errors='coerce')
        numeric_df = numeric_df.fillna(0).astype(int)
        
        match month_format:
          case 'identity': pass  # Keep original values
          case '%b':
            display_column[mask_numeric] = numeric_df.apply(lambda x: month_abbreviations[x])
          case '%B':
            display_column[mask_numeric] = numeric_df.apply(lambda x: month_full[x])

    for row_id, row_value in self.problems[col_name].items():
      display_column[row_id] = row_value
    return display_column.fillna(NA_string)

  @null_safety_decorator
  def display_as_week(self, df, col_name, supplement):
    week_abbreviations = [NA_string, 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    week_full = [NA_string, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    week_format = supplement.get('week', '%a')

    if df[col_name].dtype == 'datetime64[ns]':
      if week_format == 'identity':
        display_column = df[col_name].dt.dayofweek
      else:
        display_column = df[col_name].dt.strftime(week_format)
    else:
      # Check if values are already in week_abbreviations or week_full
      display_column = df[col_name].copy()
      
      # Only convert numeric-looking values
      mask_numeric = df[col_name].astype(str).str.match(r'^\d+$')
      
      if mask_numeric.any():
        numeric_df = pd.to_numeric(df[col_name][mask_numeric], errors='coerce')
        numeric_df = numeric_df.fillna(0).astype(int)
        
        match week_format:
          case 'identity': pass  # Keep original values
          case '%a': 
            display_column[mask_numeric] = numeric_df.apply(lambda x: week_abbreviations[x])
          case '%A': 
            display_column[mask_numeric] = numeric_df.apply(lambda x: week_full[x])

    for row_id, row_value in self.problems[col_name].items():
      display_column[row_id] = row_value
    return display_column.fillna(NA_string)

  @null_safety_decorator
  def display_as_quarter(self, df, col_name, supplement):
    quarter_abbreviations = [NA_string, 'Q1', 'Q2', 'Q3', 'Q4']
    quarter_full = [NA_string, 'First Quarter', 'Second Quarter', 'Third Quarter', 'Fourth Quarter']
    quarter_format = supplement.get('quarter', '%o')

    if df[col_name].dtype == 'datetime64[ns]':
      if quarter_format == 'identity':
        display_column = df[col_name].dt.quarter
      else:
        display_column = df[col_name].dt.strftime(quarter_format)
    else:
      numeric_df = pd.to_numeric(df[col_name], errors='coerce')
      numeric_df = numeric_df.fillna(0).astype(int)

      match quarter_format:
        case 'identity': display_column = df[col_name]
        case '%o': display_column = numeric_df.apply(lambda x: quarter_abbreviations[x])
        case '%O': display_column = numeric_df.apply(lambda x: quarter_full[x])

    for row_id, row_value in self.problems[col_name].items():
      display_column[row_id] = row_value
    return display_column.fillna(NA_string)

  @null_safety_decorator
  def display_as_timestamp(self, df, col_name, supplement):
    timestamp_format = supplement.get('timestamp', '%Y-%m-%dT%H:%M:%S%z')
    def get_display_value(row):
      if pd.isna(row[col_name]):
        return self.problems[col_name].get(row.name, NA_string)
      return row[col_name].strftime(timestamp_format)

    return df.apply(get_display_value, axis=1)

  def display_as_type(self, df, col_name, type, subtype, supplement={}):
    # Insert the original values for problematic rows, fill NaNs with <N/A>
    match subtype:
      case 'id':        column = self.display_as_id(df, col_name)
      case 'boolean':   column = self.display_as_boolean(df, col_name)
      case 'currency':  column = self.display_as_currency(df, col_name, supplement)
      case 'percent':   column = self.display_as_percent(df, col_name)
      case 'decimal':   column = self.display_as_decimal(df, col_name)
      case 'phone':     column = self.display_as_phone(df, col_name, supplement)
      case 'time':      column = self.display_as_time(df, col_name, supplement)
      case 'month':     column = self.display_as_month(df, col_name, supplement)
      case 'week':      column = self.display_as_week(df, col_name, supplement)
      case 'quarter':   column = self.display_as_quarter(df, col_name, supplement)
      case 'date':      column = self.display_as_date(df, col_name , supplement)
      case 'timestamp': column = self.display_as_timestamp(df, col_name, supplement)
      case _:           column = df[col_name].astype(object).fillna(NA_string)
    return column
