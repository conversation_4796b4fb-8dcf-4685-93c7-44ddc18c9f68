import copy
from collections import defaultdict
from datetime import datetime as dt
from utils.help import dax2dact, dact2dax, dax2intent
from backend.components.engineer import PromptEngineer
from backend.components.ambiguity import Ambiguity

class DialogueState(object):

  def __init__(self, entities:list, intent:str='', dact:str='', dax:str='', thought='', score=0):
    # required elements
    self.entities = entities        # list of entities, where each entity is a dict with {tab, col, ver}
    self.current_tab = None         # current table name

    # optional elements
    self.intent = intent            # intent: select, visualize, clean, transform, detect or converse
    self.store_dacts(dact, dax)     # string of final dax or dact
    self.ambiguity = Ambiguity()    # object describing the level of uncertainty in the dialogue state

    # additional elements
    self.score = score
    self.thought = thought
    self.slices = {'metrics': {}, 'operations': [], 'preferences': []}
    self.flow_stack = []
    self.errors = []

    self.command_type = ''
    self.keep_going = True
    self.natural_birth = True
    self.has_staging = False    # TODO: Convert this into a StagingFlow {19A}
    self.has_issues = False     # issue data is stored in frame
    self.has_plan = False       # user needs to pick a plan of action
    self.timestamp = dt.now().strftime("%m/%d/%Y, %H:%M:%S")

  def __str__(self):
    entity_line = self.tab_col_rep()
    flow_lines = [str(flow) for flow in self.flow_stack]
    return "\n".join([entity_line, "* Flows:", *flow_lines])

  @staticmethod
  def entity_to_dict(entity_list, needs_verification=False):
    if needs_verification:
      entity_list = [entity for entity in entity_list if entity['ver']]

    entity_dict = defaultdict(list)
    for entity in entity_list:
      entity_dict[entity['tab']].append(entity['col'])
    return entity_dict

  @staticmethod
  def dict_to_entity(column_dict):
    entities = []
    for tab_name, columns in column_dict.items():
      for col_name in columns:
        entities.append({'tab': tab_name, 'col': col_name, 'ver': False})
    return entities

  def tab_col_rep(self, with_break=False):
    tab_list = set([entity['tab'] for entity in self.entities])
    pred_tables = "; ".join(list(tab_list))

    column_dict = self.entity_to_dict(self.entities)
    pred_columns = PromptEngineer.column_rep(column_dict, with_break)
    return f"* Tables: {pred_tables}\n* Columns: {pred_columns}"

  def validate_flow(self):
    active_flow = self.get_flow()
    if active_flow:
      # reset the flow in conditions of uncertainty since previously filled slots are now invalid
      uncertainty_level = self.ambiguity.lowest_level()
      if uncertainty_level != 'none':
        active_flow.reset_flow(uncertainty_level)

      ent_slot = active_flow.entity_slot
      if active_flow.slots[ent_slot].filled and active_flow.slots[ent_slot].values[0].get('col', False):
        expansion_needed = set()
        for entity in active_flow.slots[ent_slot].values:
          if entity['col'] == '*':
            expansion_needed.add(entity['tab'])

        for tab_name in expansion_needed:
          # remove existing instances from the slot
          flow_entities = active_flow.slots[ent_slot].values
          active_flow.slots[ent_slot].values = [ent for ent in flow_entities if ent['tab'] != tab_name]
          # add all columns from the table
          for col_name in active_flow.valid_col_dict[tab_name]:
            active_flow.slots[ent_slot].add_one(tab_name, col_name)

  def has_active_flow(self, size=0):
    return len(self.flow_stack) > size

  def get_flow(self, flow_type='', allow_interject=True, return_name=False):
    # find the first flow that meets all criteria, going in reverse since is it a stack
    if self.has_active_flow():
      if allow_interject and not flow_type:
        matched_flow = self.flow_stack[-1]
      else:
        matched_flow = next((
          flow for flow in reversed(self.flow_stack)
          if (not flow_type or flow.name() == flow_type) and
             (allow_interject or not flow.interjected)
        ), None)    # defaults to None if no flow matches the requirements

      if matched_flow:
        return matched_flow.name(full=True) if return_name else matched_flow
    return 'none' if return_name else None

  def get_dialog_act(self, form='hex'):
    if form == 'list':
      return self.dacts.split(" + ") if hasattr(self, 'dacts') else None
    elif form == 'string':
      return self.dacts if hasattr(self, 'dacts') else None
    elif form == 'hex':
      return self.dax if hasattr(self, 'dax') else None
    return None  


  def store_dacts(self, dact='', dax=''):
    if len(dact) == 0 and len(dax) == 0:
      raise ValueError("Dact and Dax cannot both be empty")

    if len(dact) > 0:
      self.dacts = dact
    if len(dax) > 0:
      self.dax = dax

    if len(dact) == 0:
      self.dacts = dax2dact(dax)
    if len(dax) == 0:
      self.dax = dact2dax(dact)

    self.intent = dax2intent(self.dax)

  @classmethod
  def from_dict(cls, labels:dict, active_table:str):
    label_parts = ['intent', 'dax', 'entities', 'thought', 'score']
    label_dict = {key: copy.deepcopy(labels[key]) for key in label_parts if key in labels}
    # limit to unique entities by storing the string versions as dictionary keys
    label_dict['entities'] = list({str(ent): ent for ent in label_dict['entities']}.values())

    state = cls(**label_dict)
    state.natural_birth = labels.get('natural_birth', True)
    state.current_tab = labels['entities'][0]['tab'] if len(labels['entities']) > 0 else active_table
    return state

