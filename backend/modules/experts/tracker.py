import random
import numpy as np
import pandas as pd
from tqdm import tqdm as progress_bar

from collections import Counter, defaultdict
from backend.modules.experts.for_nlu import BaseExpert

class IssueTracker(BaseExpert):

  def __init__(self, batch_size=10):
    self.results = []
    self.conflicts = []    # holds cardsets
    self.aligned = set()
    
    self.batch_size = batch_size
    self.batch_number = 0
    self.cardset_index = 0
    self.epsilon = 1e-6

    self.num_issues = -1   # number of issues to resolve at the start
    self.confidence = 0.0
    self.side_to_tab = {'left': '', 'right': ''}
    self.tab_to_cols = defaultdict(list)

  def increment_cardset(self, active_conflicts):
    self.cardset_index += 1
    num_reviewed = len(active_conflicts)
    self.conflicts = self.conflicts[num_reviewed:]

  def increment_batch(self, active_conflicts=None):
    self.batch_number += 1
    self.cardset_index = 0
    if active_conflicts:
      self.conflicts = active_conflicts

  def sample_conflicts(self, sample_size=10, as_string=False):
    """ Finds conflicts to resolve, places them at the start of the tracker list, and then returns the batch
    TODO: sample cardset with highest entropy to maximize information gain, rather than random sampling """
    if len(self.conflicts) < sample_size:
      sampled_cards = self.conflicts
    else:
      sampled_cards = random.sample(self.conflicts, sample_size)

    if as_string:
      # convert the conflicts to strings with surrounding quotes
      sampled_cards = [f"'{card['value']}'" for card in sampled_cards]
      sampled_cards = '\n'.join(sampled_cards)
    else:
      # re-order the conflicts so that the sampled cards are at the front
      remaining_cards = [card for card in self.conflicts if card not in sampled_cards]
      self.conflicts = sampled_cards + remaining_cards

    return sampled_cards

  def labeled_cardsets(self):
    positive_cardsets, negative_cardsets = [], []
    for result in self.results:
      retain_id = result['retain'][0]

      if result['resolution'] == 'merge':
        for retire_id in result['retire']:
          pair = (retain_id, retire_id)
          positive_cardsets.append(pair)

      elif result['resolution'] == 'separate':
        for other_id in result['retain'][1:]:
          pair = (retain_id, other_id)
          negative_cardsets.append(pair)

    return positive_cardsets, negative_cardsets

  def combine_cards_action(self, frame):
    if len(frame.active_conflicts) > 0:
      if self.results[-1]['resolution'] == 'back':
        self.results = self.results[:-2]  # remove the last two cardsets
        self.cardset_index -= 1
      else:  # either to kickstart the process or to move forward to the next cardset
        self.cardset_index += 1

    frame.properties['cardset_index'] = self.cardset_index - 1
    return frame

  def still_resolving(self) -> bool:
    not_done_with_batch = self.cardset_index < self.batch_size
    going_backward = self.results[-1]['resolution'] == 'back'
    return not_done_with_batch or going_backward
  
  def forward_resolution(self) -> bool:
    positive_results = len(self.results) > 0
    going_forward = self.results[-1]['resolution'] != 'back'
    return positive_results and going_forward

  def still_empty(self) -> bool:
    total_count = len(self.conflicts) + len(self.results)
    return total_count == 0

  def store_cardsets(self, autofixes, conflicts):
    self.results.extend(autofixes)
    self.conflicts.extend(conflicts)

  def num_conflicts(self) -> int:
    return len(self.conflicts)

  def has_conflicts(self) -> bool:
    return len(self.conflicts) > 0

  def reset(self):
    self.results = []
    self.conflicts = []
    self.issues = []
    self.aligned = set()

    self.batch_number = 0
    self.cardset_index = 0
    self.confidence = 0.0
