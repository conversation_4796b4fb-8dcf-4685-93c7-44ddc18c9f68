import re
import math
import random
import numpy as np
import pandas as pd
from collections import defaultdict

from backend.prompts.mixins.for_clean import *
from backend.prompts.grounding.clean_prompts import update_flow_prompt
from backend.assets.ontology import style_mapping, common_tlds, NA_string
from backend.components.engineer import PromptEngineer
from backend.components.frame import Frame
from backend.utilities.manipulations import *

class CleanMixin:
  """ Methods to update the values or remove errors to clean up the data """

  def update_action(self, context, state, world):
    # Router for {006} to decide between cleaning a table with or without issues, also marks flow as completed when done
    flow = state.get_flow(flow_type='update')

    if flow.is_uncertain:
      self.actions.add('CLARIFY')
      flow.is_uncertain = False
      state.ambiguity.declare('specific')
      frame = Frame(state.current_tab)
      return frame, state

    if state.has_issues:
      if state.natural_birth:
        frame, state = self.modify_issues(flow, context, state, world)
      else:
        frame, state = self.interactive_clean(flow, context, state, world)
    else:
      frame, state = self.update_values(flow, context, state, world)

    if frame.is_successful():
      flow.completed = True
      unique_tables = set([ent['tab'] for ent in flow.slots['target'].values])
      if len(unique_tables) > 1:
        updated_tabs = list(unique_tables)
        frame.properties['tabs'] = updated_tabs
      else:
        updated_tabs = [state.current_tab]

      if 'rename' in frame.code:   # column header has likely been renamed
        self.replace_shadow_keys(flow)
        self.update_system_prompt(updated_tabs, world, context, flow)
      else:
        self.database.db.complete_registration(state.current_tab)
    return frame, state

  def interactive_clean(self, flow, context, state, world):
    # Deterministically resolve the typos inside inside the similar terms flow
    self.actions.add('ISSUE|typo')
    previous_frame = world.frames[-1]
    tab_name, col_name = previous_frame.issues_entity['tab'], previous_frame.issues_entity['col']

    resolve_flow = state.get_flow(flow_type='typo')
    chosen = resolve_flow.chosen_term
    similar = [term for term in resolve_flow.all_terms if term != chosen]

    # clear out the frame
    selected_rows = []
    for term in resolve_flow.all_terms:  # includes the chosen term
      for row_id, row_val in previous_frame.data[col_name].items():
        if term == row_val:
          selected_rows.append(row_id)
    previous_frame.deactivate_issues(selected_rows)

    # clear out the flow
    active_issue_type = ''
    for term in resolve_flow.all_terms:
      for issue_type in resolve_flow.issue_types:
        if term in resolve_flow.slots[issue_type].steps:
          resolve_flow.slots[issue_type].mark_as_complete(term)
          active_issue_type = issue_type

    # clear out the metadata issues
    tab_typos = world.metadata['typo'][tab_name]
    issue_types = [active_issue_type] * len(selected_rows)
    tab_typos.remove_issues(col_name, selected_rows, issue_types)

    # make the update directly on the underlying database
    self.database.db.tables[tab_name][col_name].replace(similar, chosen, inplace=True)
    resolve_flow.turns_taken += 1

    if resolve_flow.is_filled():
      resolve_flow.completed = True
      frame = Frame(tab_name)
      frame.resolved_rows = previous_frame.resolved_rows
      frame.set_data([])  # clear out the stored data to force a reload
      state.has_issues = False        # declare that the state no longer has any issues
    else:
      remaining_rows = previous_frame.issues_entity['row'].keys()
      remaining_df = self.database.db.tables[tab_name].loc[remaining_rows, [col_name]]
      pandas_code = f"df['{tab_name}']['{col_name}'].replace(similar_terms, '{chosen}', inplace=True)"
      frame = self.validate_dataframe(remaining_df, pandas_code, 'pandas', state, tab_type='dynamic')
      frame.issues_entity = previous_frame.issues_entity

    return frame, state

  def modify_issues(self, flow, context, state, world):
    """ Supports {006} when a Detect Flow is active, so we need to resolve those issues
    Starts by filtering for the column that contains issues and the selected row ids that index into that column.
    Then execute the prompt to produce the Pandas code that can clean the issue_df. If this succeeds:
      a) remove references to the selected rows and mark them instead as resolved rows
      b) create a new Frame with the cleaned data
    Finally, fill out this frame with related attributes from the previous frame and return it.
    """
    previous_frame = world.frames[-1]
    issue_tab, issue_col = previous_frame.issues_entity['tab'], previous_frame.issues_entity['col']
    issue_df = self.take_snapshot(issue_tab)
    user_text = context.last_utt(speaker='User')

    if flow.slots['exact'].value == 'ignore':
      self.actions.add('ISSUE|ignore')
      selected_rows, _ = self.select_issue_rows(flow, previous_frame, issue_col, issue_df)
      resolve_flow, previous_frame, state = self.resolve_issues(selected_rows, previous_frame, state, world)
      frame = Frame(issue_tab)
      frame.resolved_rows = previous_frame.resolved_rows

    elif flow.slots['exact'].value == 'beyond':
      frame, state = self.update_values(flow, state, world, context)
    elif flow.slots['exact'].value == 'convert':
      frame = Frame(issue_tab)
      flow.fall_back = '06E'
    elif flow.slots['exact'].value == 'delete':
      frame = Frame(issue_tab)
      flow.fall_back = '007'

    else:     # actually clean the issue rows
      self.actions.add('ISSUE|modify')
      all_cols = PromptEngineer.array_to_nl(issue_df.columns, connector='and')
      df_desc = f"The full dataframe includes {len(issue_df)} rows and additional columns: {all_cols}."

      agent_text = context.last_utt(speaker='Agent')
      if len(agent_text) == 0:
        history = f"Agent: What would you like to clean?\nUser: {user_text}"
      else:
        history = f"Agent: {agent_text}\nUser: {user_text}"
      selected_rows, subset_df = self.select_issue_rows(flow, previous_frame, issue_col, issue_df)
      subset_md = PromptEngineer.display_preview(subset_df)

      prompt = clean_issues_prompt.format(df_description=df_desc, convo_history=history, issues=subset_md)
      valid_tabs = [subset_df, issue_df, 'Detect']  # issue_df is necessary for global calculations, do not remove
      subset_df, pandas_code = self.database.manipulate_data(context, state, prompt, valid_tabs)

      if pandas_code == 'error':
        frame = previous_frame
        frame.signal_failure('code_generation', subset_df.strip())   # subset_df is actually an error message
        return frame, state
      else:
        resolve_flow, previous_frame, state = self.resolve_issues(selected_rows, previous_frame, state, world)
        self.database.update_data(issue_tab, subset_df)

        if resolve_flow.completed:
          frame = self.validate_dataframe(issue_df, pandas_code, 'pandas', state, tab_type='direct')
          frame.set_data([])  # clear out the stored data to force a reload
        else:
          remaining_rows = previous_frame.issues_entity['row'].keys()
          remaining_df = issue_df.loc[remaining_rows, [issue_col]]
          frame = self.validate_dataframe(remaining_df, pandas_code, 'pandas', state, tab_type='dynamic')
          frame.issues_entity = previous_frame.issues_entity
        frame.resolved_rows = previous_frame.resolved_rows

    return frame, state

  def update_values(self, flow, context, state, world):
    """ Supports {006} by deciding what content to change, then notifies the database accordingly
    Upon success, we also need to update across the entire world, including prompts and metadata """
    frame = world.frames[-1] if world.has_data() else self.default_frame(state, world.valid_columns)

    if flow.slots['source'].filled:
      convo_history = context.compile_history(look_back=3)

      if flow.slots['target'].filled:
        source_cols, source_tabs = [], []
        for entity in flow.slots['source'].values:
          col_name, tab_name = entity['col'], entity['tab']
          tab_schema = world.metadata['schema'][tab_name]
          col_subtype = tab_schema.get_type_info(col_name)['subtype']
          col_string = f"{col_name} ({col_subtype})"

          source_cols.append(col_string)
          source_tabs.append(tab_name)

        if len(source_cols) == 1:
          loc_rep = f"{source_cols[0]} column in {source_tabs[0]}"
        else:
          tab_list = PromptEngineer.array_to_nl(source_tabs, connector='and')
          col_list = PromptEngineer.array_to_nl(source_cols, connector='and')
          if len(source_cols) == len(world.valid_columns[source_tabs[0]]):
            col_list = "all"
          loc_rep = f"{col_list} columns in {tab_list}"

        table_desc = self.database.table_desc
        tht_rep = "I can update just as the user requested" if len(state.thought) == 0 else state.thought
        prompt = update_prompt.format(df_tables=table_desc, history=convo_history, location=loc_rep, thought=tht_rep)

        db_output, code = self.database.manipulate_data(context, state, prompt, world.valid_tables)
        if code == 'error':
          frame.signal_failure('code_generation', db_output.strip())
          self.actions.add("SHARE_MSG")
        else:
          frame = self.validate_dataframe(db_output, code, 'pandas', state, tab_type='direct')

      else:
        prediction, flow = self.predict_grounding_slots(convo_history, flow, state, world)
        if flow.fill_slot_values(state.current_tab, prediction):
          frame, state = self.update_values(flow, context, state, world)
        else:
          self.actions.add('CLARIFY')
          state.ambiguity.declare('partial', flow='update')
    else:
      self.actions.add("CLARIFY")
      frame.signal_failure('custom', 'it is unclear what the user would like to update')
      state.ambiguity.declare('partial', flow='update')
    return frame, state

  def predict_grounding_slots(self, convo_history, flow, state, world):
    """ Predicts the source and target entities for the update flow when the user has not provided them """
    tab_col_str = PromptEngineer.tab_col_rep(world)
    entity_dict = state.entity_to_dict(flow.slots['source'].values)
    flow.slots['source'].drop_unverified()

    ent_strings = [f"{tab_name} - {col_names}" for tab_name, col_names in entity_dict.items()]
    prior_state = "\n".join(ent_strings)
    prompt = update_flow_prompt.format(history=convo_history, valid_tab_col=tab_col_str, prior_state=prior_state)
    prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
    return prediction, flow

  def replace_shadow_keys(self, flow):
    source_slots = flow.slots['source'].values
    target_slots = flow.slots['target'].values

    if len(source_slots) == len(target_slots):
      for source_ent, target_ent in zip(source_slots, target_slots):
        source_col, target_col = source_ent['col'], target_ent['col']
        if source_col in self.database.db.shadow.problems.keys():
          self.database.db.shadow.problems[target_col] = self.database.db.shadow.problems.pop(source_col)

  def select_issue_rows(self, flow, previous_frame, issue_col, issue_df):
    """ Select the subset of row ids to address based on our understanding of the user's intent """
    all_rows = list(previous_frame.issues_entity['row'].keys())

    if flow.code_generation:
      # use code to filter for the appropriate dataframe, then gather the row ids from that subset
      try:
        generated_code = flow.slots[flow.entity_slot].values[0]['row']
        extra_context = {'pd': pd, 'np': np, 'issue_df': issue_df, 'all_rows': all_rows}
        exec(generated_code, extra_context)
        subset_df = extra_context['subset_df']
        selected_rows = subset_df.index.tolist()
      except Exception as ecp:
        print(f"Code Execution Error - {ecp}")
        selected_rows = all_rows
    else:
      # gather row ids directly from the source slot, then use those rows to craft the subset dataframe
      selected_rows = [entity['row'] for entity in flow.slots[flow.entity_slot].values]
      if any([row_id < 0 for row_id in selected_rows]):
        selected_rows = all_rows
      subset_df = issue_df.loc[selected_rows, [issue_col]]

    return selected_rows, subset_df

  def validate_action(self, context, state, world):
    # Supports {36D} by validating the data within the column as belonging to a predefined set
    flow = state.get_flow(flow_type='validate')

    if flow.slots['source'].filled and not flow.is_uncertain:
      # find the number of unique values in the source column
      entity = flow.slots['source'].values[0]
      tab_name, col_name = entity['tab'], entity['col']
      column = self.database.db.tables[tab_name][col_name]
      prepared_col = column.astype(str).str.lower().str.strip().dropna()

      convo_history = context.compile_history()
      if len(flow.unique_values) == 0:
        flow.unique_values = column.dropna().astype(str).str.strip().str[:64].unique().tolist()
      frame = Frame(tab_name)

      if prepared_col.nunique() < 64:  # if we have a reasonable number of unique values, we can proceed
        if flow.slots['terms'].filled:
          if 'incorrect_mapping' in state.errors:
            num_invalid_terms = 14        # set to a high number to force checkbox interaction
            state.errors.remove('incorrect_mapping')
          else:
            num_invalid_terms = len(flow.slots['mapping'].value)

          if flow.slots['terms'].verified or num_invalid_terms <= 2:
            flow = self.review_validation_results(col_name, convo_history, flow)
            column, flow, frame = self.complete_data_validation(column, flow, frame)
            self.database.db.tables[tab_name][col_name] = column
            self.update_system_prompt([tab_name], world, context, flow)
          elif num_invalid_terms < 8:     # just use chat to confirm the changes
            self.actions.add('SUGGEST')
            flow.stage = 'confirm-suggestion'
          elif num_invalid_terms < 32:    # use checkbox interaction to confirm the proposed changes
            self.actions.add('INTERACT')
            flow.stage = 'checkbox-opt'
          else:
            flow.fall_back = '46D'

        else:  # we need to fill the terms slot
          prompt = validate_prompt.format(target_column=col_name, history=convo_history, uniques=flow.unique_values)
          raw_output = self.api.execute(prompt)
          prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

          if flow.fill_slot_values(tab_name, prediction):
            frame, state = self.validate_action(context, state, world)
          else:
            self.actions.add('CLARIFY')
            state.ambiguity.declare('specific')

      else:  # consider a different approach, such as:
        sample_md = PromptEngineer.display_samples(self.database.db.tables[tab_name], [col_name], num_samples=10)
        routing_prompt = validate_routing_prompt.format(column=col_name, history=convo_history, samples=sample_md)
        raw_output = self.api.execute(routing_prompt)
        pred_situation = PromptEngineer.apply_guardrails(raw_output, 'json')

        match pred_situation:
          case 'typo': flow.fall_back = '46D'
          case 'format': flow.fall_back = '36F'
          case 'validate': flow.fall_back = '36D'
          case 'other': state.store_dacts('', '9DF')
    else:
      self.actions.add('INTERACT')
      flow.stage = 'pick-tab-col'
      frame = Frame(state.current_tab)
      frame.tab_type = 'decision'

    return frame, state

  def review_validation_results(self, col_name, convo_history, flow):
    valid_values = flow.slots['terms'].values
    remaining_terms = [term for term in flow.unique_values if term not in valid_values]
    invalid_values = list(flow.slots['mapping'].value.keys())

    if set(remaining_terms) != set(invalid_values):
      flow.slots['mapping'].value = {}    # clear out the mapping slot to prevent conflicts
      prompt = validation_grouping_prompt.format(target_column=col_name, history=convo_history,
                                        valid_terms=valid_values, invalid_terms=remaining_terms)
      raw_output = self.api.execute(prompt)
      prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

      tab_name = flow.slots['source'].values[0]['tab']
      flow.fill_slot_values(tab_name, prediction)
    return flow

  def complete_data_validation(self, column, flow, frame):
    # TODO: check if the column substype should be changed to 'status' or 'category'
    rows_affected = 0
    for invalid_term, valid_term in flow.slots['mapping'].value.items():
      # count how many times the invalid term appears in the column before replacing it
      rows_affected += column.astype(str).str.contains(invalid_term).sum()
      column.replace(invalid_term, valid_term, inplace=True)

    # usually 'total_rows' represents total remaining rows, but we use it here to represent total unique values
    total_unique_rows = len(column.dropna().unique())
    frame.properties['row_counts'] = (rows_affected, total_unique_rows)
    flow.completed = True
    return column, flow, frame

  def backup_verification(self, col_name, convo_history, flow):
    # check the selected terms against other model predictions to see if they match
    pred_valid_terms = sorted(flow.slots['terms'].values)
    unique_vals = flow.unique_values
    backup_prompt = backup_validation_prompt.format(target_column=col_name, history=convo_history, uniques=unique_vals)

    raw_output = self.api.execute(backup_prompt)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')
    pred_backup_terms = sorted(prediction['terms'])
    return pred_backup_terms == pred_valid_terms

  def format_action(self, context, state, world):
    # Supports {36F} by standardizing the data within the column to conform to a specific format
    flow = state.get_flow(flow_type='format')
    valid_ent_list = state.dict_to_entity(world.valid_columns)
    frame = world.frames[-1] if world.has_data() else self.default_frame(state, valid_ent_list)

    if flow.slots['source'].filled and not flow.is_uncertain:
      entity = flow.slots['source'].values[0]
      tab_name, col_name = entity['tab'], entity['col']
      col_props = world.metadata['schema'][tab_name].get_type_info(col_name)
      table = self.database.db.tables[tab_name]
      display_col = self.database.db.shadow.display_as_type(table, **col_props, tab_name=tab_name)

      if flow.slots['format'].filled:
        column = table[col_name]
        if not flow.slots['subtype'].filled:
          flow.slots['subtype'].assign_one(col_props['subtype'])

        if flow.slots['alignment'].filled:
          if flow.tracker.batch_number == 0 and flow.tracker.num_issues < 0:
            flow = self.initialize_format_tracker(column, display_col, flow)

          flow, frame = self.reformat_conflicting_rows(context, entity, flow, state)
          self.update_system_prompt([tab_name], world, context, flow)
          if flow.tracker.has_conflicts():
            state = self.ask_conflict_resolution(context, flow, state)
          else:
            self.database.db.shadow.problems[col_name] = {}  # reset the problems
            flow.completed = True
        else:
          tab_schema = world.metadata['schema'][tab_name]
          flow = self.initialize_alignment(context, col_props, column, flow, state, tab_schema)
          if flow.is_filled():    # recursing also allows us to re-draw the display_col
            frame, state = self.format_action(context, state, world)
          elif flow.completed:    # early exit since there are no conflicts to resolve
            frame = Frame(tab_name)
            frame.has_changes = True
            frame.properties['row_counts'] = (-1, len(column))
          else:
            self.actions.add('CLARIFY')
            state.ambiguity.declare('specific', flow='format', slot='precisely what format you want, including examples')

      else:  # predict the target format for the column
        convo_history = context.compile_history()
        column_md = PromptEngineer.display_samples(display_col.to_frame(), [col_name], num_samples=16, skip_nulls=True)
        if col_props['type'] == 'datetime':
          prompt = datetime_format_prompt.format(target_column=col_name, preview=column_md, history=convo_history)
        else:
          prompt = textual_format_prompt.format(target_column=col_name, preview=column_md, history=convo_history)
        raw_output = self.api.execute(prompt)
        prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

        if flow.fill_slot_values(col_props['subtype'], prediction):
          frame, state = self.format_action(context, state, world)
        elif not flow.slots['subtype'].filled and flow.is_uncertain:
          frame, state = self.format_rerouting(context, flow, frame, state)
        elif len(flow.slots['format'].options) > 1:
          self.actions.add('SUGGEST')   # we have multiple options for formats, so we need to ask the user to pick one
    else:
      frame, state = self.format_rerouting(context, flow, frame, state)
    return frame, state

  def format_rerouting(self, context, flow, frame, state):
    if flow.slots['source'].filled:
      tab_name = flow.slots['source'].table_name()
      columns = [entity['col'] for entity in flow.slots['source'].values if entity['tab'] == tab_name]
    else:
      tab_name = state.current_tab
      columns = []

    preview = PromptEngineer.display_preview(self.database.db.tables[tab_name], columns, max_rows=16)
    prompt = format_routing_prompt.format(history=context.compile_history(), data_preview=preview, thought=state.thought)
    decision = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

    match decision['method']:
      case 'update': flow.fall_back = '006'
      case 'validate': flow.fall_back = '36D'
      case 'format': flow.stage = 'pick-tab-col'
      case _: state.store_dacts('', '9DF')

    if flow.stage == 'pick-tab-col':
      self.actions.add('INTERACT')
      frame.tab_type = 'decision'
    elif state.get_dialog_act() == '9DF':
      self.actions.add('CLARIFY')
      state.ambiguity.declare('general')
      flow.completed = True

    return frame, state

  def initialize_format_tracker(self, column, display_col, flow):
    alignment_mask = column.astype(str).apply(flow.slots['alignment'].value)

    initial_conflicts = []
    format_problems = self.database.db.shadow.problems.get(column.name, {})
    for row_id, display_value in format_problems.items():
      initial_conflicts.append({'row_id': row_id, 'value': str(display_value) })

    for row_id in column[~alignment_mask].index:
      if pd.isna(column.loc[row_id]): continue   # skip null values
      display_string = str(display_col.loc[row_id])
      initial_conflicts.append({'row_id': row_id, 'value': display_string})

    flow.tracker.reset(initial_conflicts)
    aligned_values = column[alignment_mask][:64].astype(str)
    flow.tracker.aligned.update(aligned_values)
    return flow

  def ask_conflict_resolution(self, context, flow, state):
    self.actions.add('CLARIFY')
    state.ambiguity.declare('specific')

    convo_history = context.compile_history()
    conflict_str = flow.tracker.sample_conflicts(sample_size=5, as_string=True)
    col_type = flow.slots['subtype'].value
    target_format = flow.slots['format'].value
    task_desc = f"Reformat the {col_type}s into the '{target_format}' format"

    prompt = conflict_resolution_prompt.format(goal=flow.goal, history=convo_history, conflicts=conflict_str, task=task_desc)
    prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

    clarification = "I have reformatted most of the rows, but there are still some unresolved conflicts. "
    clarification += prediction['question']
    state.ambiguity.observation = clarification
    return state

  def initialize_alignment(self, context, col_props, display_column, flow, state, tab_schema):
    """ alignment represents a function that verifies if a row value matches the target format
    The name of the function is always `is_format_aligned(cell_value)` and returns a boolean value """
    target_format = flow.slots['format'].value
    current_subtype = flow.slots['subtype'].value
    supplement_details = col_props.get('supplement', {})
    current_format = supplement_details.get(current_subtype, '')
    col_name, col_type = col_props['col_name'], col_props['type']

    if len(current_format) > 0:
      # then the column display is dictated by a certain format, rather than its raw value in the DB
      if current_format != target_format:
        # set the target format to the supplement to become the new format
        supplement_details[current_subtype] = target_format
        tab_schema.set_type_info(col_name, col_type, current_subtype, supplement_details)

      if len(self.database.db.shadow.problems[col_name]) == 0:
        flow.completed = True       # if there are no problems, then we wrap up immediately
        return flow

    # Generate the alignment function used to find the conflicts
    if flow.is_datetime_subtype():
      match current_subtype:
        case 'date':      is_format_aligned = date_format_alignment
        case 'month':     is_format_aligned = month_format_alignment
        case 'quarter':   is_format_aligned = quarter_format_alignment
        case 'time':      is_format_aligned = time_format_alignment
        case 'timestamp': is_format_aligned = timestamp_format_alignment
        case 'week':      is_format_aligned = week_format_alignment
      function_str = '<custom internal function>'
    else:
      prompt = textual_alignment_prompt.format(target_format=target_format)
      column_preview = display_column.to_frame().head(16).to_markdown()
      custom_params = {'artifact_key': 'is_format_aligned', 'data_preview': column_preview, 'prefix_string': 'Plan:',
                       'trigger_phrase': 'Function:', 'exec_context': {'common_tlds': common_tlds} }
      is_format_aligned, function_str = self.database.generate_artifact(context, prompt, state, custom_params)

    if function_str == 'error':
      print(f"Error generating alignment function")
      self.actions.add('CLARIFY')
      state.ambiguity.declare('specific', flow='format', slot='the function used to check for alignment')
    else:
      flow.slots['alignment'].assign_one(is_format_aligned, function_str)
    return flow

  def reformat_conflicting_rows(self, context, entity, flow, state):
    tab_name, col_name = entity['tab'], entity['col']
    table_df = self.database.db.tables[tab_name]
    flow.tracker.batch_number = 0

    if flow.is_datetime_subtype():
      flow, table_df, function_str = self.reformat_conflicting_datetime(context, flow, state, col_name, table_df)
    else:
      flow, table_df, function_str = self.reformat_conflicting_text(context, flow, state, col_name, table_df)

    self.database.db.tables[tab_name] = table_df
    frame = Frame(tab_name)
    frame.set_data([], code=function_str, source='pandas')
    frame.has_changes = True
    frame.properties['row_counts'] = (len(flow.tracker.results), len(table_df))
    return flow, frame

  def reformat_conflicting_text(self, context, flow, state, col_name, table_df):
    is_format_aligned = flow.slots['alignment'].value
    target_format = flow.slots['format'].value
    function_str = ""  # if no conflicts exist, this will remain empty
    convo_history = context.compile_history(look_back=3)

    while flow.tracker.has_conflicts() and flow.tracker.batch_number < 4:
      # Sample 8 examples of the aligned format to provide as reference of correct behavior
      correct_format = random.sample(list(flow.tracker.aligned), min(8, len(flow.tracker.aligned)))
      # Sample up to 16 conflicts without replacement to identify error patterns
      conflict_str = flow.tracker.sample_conflicts(sample_size=16, as_string=True)
      # Add messages about marking unresolvable values with a sentinel string and conversation history
      sentinel_msg = first_batch_sentinel_msg if flow.tracker.batch_number == 0 else second_batch_sentinel_msg
      prompt = reformat_textual_prompt.format(target=target_format, history=convo_history, correct=correct_format,
                                              unresolvable_msg=sentinel_msg, conflicts=conflict_str)

      custom_params = {'artifact_key': 'reformat_col', 'data_preview': conflict_str, 'trigger_phrase': 'Function:',
                       'exec_context': {'common_tlds': common_tlds}, 'prefix_string': 'Thoughts:'}
      reformat_col, function_str = self.database.generate_artifact(context, prompt, state, custom_params)
      
      if function_str == 'error':
        break
      else:
        valid_mask = table_df[col_name].notna()
        conflict_series = table_df.loc[valid_mask, col_name]
        table_df.loc[valid_mask, col_name] = reformat_col(conflict_series)

      still_conflicted = []
      for conflict_card in flow.tracker.conflicts:
        row_id = conflict_card['row_id']
        reformatted_value = str(table_df.loc[row_id, col_name])
        
        if reformatted_value == '<UNRESOLVABLE>':
          table_df.loc[row_id, col_name] = conflict_card['value']  # restore the original value
          conflict_card['revised'] = False
          flow.tracker.results.append(conflict_card)
        elif is_format_aligned(reformatted_value):
          conflict_card['revised'] = True
          flow.tracker.results.append(conflict_card)
          if len(flow.tracker.aligned) < 32:
            flow.tracker.aligned.add(reformatted_value)
        else:
          conflict_card['revision'] = reformatted_value
          still_conflicted.append(conflict_card)
      flow.tracker.increment_batch(still_conflicted)

    return flow, table_df, function_str

  def reformat_conflicting_datetime(self, context, flow, state, col_name, table_df):
    is_format_aligned = flow.slots['alignment'].value
    target_subtype = flow.slots['subtype'].value
    function_str = ""  # if no conflicts exist, this will remain empty
    convo_history = context.compile_history(look_back=3)

    while flow.tracker.has_conflicts() and flow.tracker.batch_number < 4:
      # Sample 8 examples of the aligned format to provide as reference of correct behavior
      correct_format = random.sample(list(flow.tracker.aligned), min(8, len(flow.tracker.aligned)))
      # Sample up to 16 conflicts without replacement to identify error patterns
      conflict_str = flow.tracker.sample_conflicts(sample_size=16, as_string=True)
      # Add messages about marking unresolvable values with a sentinel string and conversation history
      sentinel_msg = first_batch_sentinel_msg if flow.tracker.batch_number == 0 else second_batch_sentinel_msg
      prompt = reformat_datetime_prompt.format(datatype=target_subtype, correct=correct_format,
                                              unresolvable_msg=sentinel_msg, conflicts=conflict_str)

      custom_params = {'artifact_key': 'reformat_col', 'data_preview': conflict_str,
                       'trigger_phrase': 'Function:', 'prefix_string': 'Thoughts:'}
      reformat_col, function_str = self.database.generate_artifact(context, prompt, state, custom_params)

      if function_str == 'error': break
      # pulling from table_df doesn't work because the conflict rows are set to NaT by the ShadowDB
      rows_to_clean = [conflict['value'] for conflict in flow.tracker.conflicts]
      conflict_series = pd.Series(rows_to_clean, index=range(len(rows_to_clean)))
      cleaned_series = reformat_col(conflict_series)

      still_conflicted = []
      for conflict_card, cleaned_row in zip(flow.tracker.conflicts, cleaned_series):
        row_id = conflict_card['row_id']
        reformatted_value = str(cleaned_row)

        if reformatted_value == '<UNRESOLVABLE>':
          conflict_card['revised'] = False   # keep the dataframe value as NaT
          flow.tracker.results.append(conflict_card)
        elif is_format_aligned(reformatted_value):
          conflict_card['revised'] = True
          flow.tracker.results.append(conflict_card)
          if len(flow.tracker.aligned) < 32:
            flow.tracker.aligned.add(reformatted_value)
          if target_subtype in ['date', 'time', 'timestamp']:
            table_df.loc[row_id, col_name] = pd.to_datetime(reformatted_value)
          elif target_subtype in ['month', 'week', 'quarter']:
            table_df.loc[row_id, col_name] = pd.to_numeric(reformatted_value)
        else:
          conflict_card['revision'] = reformatted_value
          still_conflicted.append(conflict_card)
      flow.tracker.increment_batch(still_conflicted)

    return flow, table_df, function_str

  def impute_missing_values(self, context, state, world):
    """ Supports {06B} by filling in missing values within the column based on the observed or external data.
    Early slots should be easy to fill, so if there is uncertainty, we re-route since we're probably in the wrong place.
    On the other hand, if the mapping or function has problems, we ask the user to clarify the method details. """
    flow = state.get_flow(flow_type='impute')
    frame = self.default_frame(state, state.entities)

    if flow.slots['target'].filled and flow.slots['source'].filled:
      target_entity = flow.slots['target'].values[0]
      tab_name, target_col = target_entity['tab'], target_entity['col']
      table_df = self.database.db.tables[tab_name]

      if flow.tracker.num_issues < 0:
        num_blank_rows, _ = self.identify_blank_rows(target_col, table_df)
        flow.tracker.num_issues = num_blank_rows
        print("Num blank issues", flow.tracker.num_issues)

      convo_history = context.compile_history()
      relevant_cols = [ent['col'] for ent in flow.slots['source'].values if ent['tab'] == tab_name] + [target_col]
      data_preview = PromptEngineer.display_preview(table_df, relevant_cols, max_rows=16)
      unique_values = unique_value_distribution(table_df[target_col], include_nulls=True, suffix=' row')

      prompt = imputation_filtering_prompt.format(target_column=target_col, history=convo_history, true_null=NA_string,
                                                  data_preview=data_preview, unique_values=unique_values)
      prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

      if flow.fill_slot_values(tab_name, prediction):
        before_df, _ = flow.tracker.apply_mask(flow.slots['mask'].values[0], table_df)
        print("Before num rows blank:", len(before_df))

        flow, state, table_df = self.function_imputation(context, flow, state, table_df, target_col)
        if state.ambiguity.present():
          return frame, state

        # check if there are still missing values
        after_df, is_blank = flow.tracker.apply_mask(flow.slots['mask'].values[0], table_df)
        print("After num rows blank:", len(after_df))
        if len(after_df) > 0:
          flow, frame = self.mapping_imputation(convo_history, flow, frame, is_blank, target_col, table_df)

        final_df, _ = flow.tracker.apply_mask(flow.slots['mask'].values[0], table_df)
        print("Final num rows blank:", len(final_df))

        self.database.db.tables[tab_name] = table_df
        self.database.db.complete_registration(tab_name)
        self.update_system_prompt([tab_name], world, context, flow)

        frame.has_changes = True
        frame.properties['row_counts'] = (flow.tracker.num_issues, len(table_df))
        flow.completed = True
    else:
      flow, state = self.imputation_rerouting(convo_history, flow, state, table_df)
    return frame, state

  def function_imputation(self, context, flow, state, table_df, target_col):
    relevant_cols = [ent['col'] for ent in flow.slots['source'].values if ent['ver']]
    if target_col not in relevant_cols:
      relevant_cols.append(target_col)
    data_preview = PromptEngineer.display_preview(table_df, columns=relevant_cols, max_rows=16)

    filtered_df, _ = flow.tracker.apply_mask(flow.slots['mask'].values[0], table_df, relevant_cols)
    flow.tracker.num_issues = len(filtered_df)

    sample_df = PromptEngineer.display_samples(table_df, relevant_cols, num_samples=28, method='dataframe')
    sample_df = pd.concat([sample_df, filtered_df.sample(n=min(4, len(filtered_df)))])
    sample_md = PromptEngineer.display_preview(sample_df, signal_limit=False)

    prompt = impute_function_prompt.format(target_column=target_col, history=context.compile_history(), 
                                            data_preview=sample_md, thought=state.thought)
    custom_params = {'artifact_key': 'fill_blanks', 'data_preview': data_preview}
    fill_blanks, function_str = self.database.generate_artifact(context, prompt, state, custom_params)

    if function_str == 'error':
      print(f"Error generating imputation function")
      self.actions.add('CLARIFY')
      state.ambiguity.declare('specific', flow='impute', slot='the formula used to fill in the blanks')
    else:
      flow.slots['function'].assign_one(fill_blanks, function_str)
      table_df = fill_blanks(table_df)
    return flow, state, table_df

  def mapping_imputation(self, convo_history, flow, frame, is_blank, target_col, table_df):
    # Get unique values from selected source columns
    source_columns = [entity['col'] for entity in flow.slots['source'].values if entity['ver']]
    source_columns = [col for col in source_columns if col != target_col]

    if len(source_columns) > 1:
      data_preview = PromptEngineer.display_samples(table_df, source_columns + [target_col], num_samples=64)
      prompt = pick_source_col_prompt.format(target_column=target_col, source_columns=source_columns, 
                                              data_preview=data_preview, history=convo_history)
      prediction = self.api.execute(prompt, version='reasoning-low', prefix='Column name: ')
      source_col = source_columns[0] if len(prediction) == 0 else prediction.strip()
    else:
      source_col = source_columns[0]
    source_keys = table_df[is_blank][source_col].dropna().unique()
    num_unique_sources = len(source_keys)

    # Create evenly sized batches, assuming each batch has at most 64 rows
    num_batches = math.ceil(num_unique_sources / 64)
    batch_size = math.ceil(num_unique_sources / num_batches)

    # Make sample pairs of source and target values for context
    source_target_pairs = []
    loops = 0
    while len(source_target_pairs) < 10 and loops < 128:
      sample_row = table_df.sample(1).iloc[0]
      source, target = sample_row[source_col], sample_row[target_col]
      if pd.notna(source) and pd.notna(target):
        source_target_pairs.append(f"{source} --> {target}")
      loops += 1

    # Iterate through batches of unique source values to create the actual mapping
    for index in range(0, num_unique_sources, batch_size):
      start_index = index
      end_index = min(index + batch_size, num_unique_sources)
      batch = source_keys[start_index:end_index]

      prompt = impute_mapping_prompt.format(history=convo_history, source_cols=source_col, target_cols=target_col,
                                            sample_pairs='\n'.join(source_target_pairs), unique_values='\n'.join(batch))
      raw_output = self.api.execute(prompt, max_tok=2048)
      prediction = PromptEngineer.apply_guardrails(raw_output, 'json')
      flow.fill_mapping_slot(prediction['mapping'])

    final_mapping = flow.slots['mapping'].value

    if len(final_mapping) > 0:
      table_df.loc[is_blank, target_col] = table_df.loc[is_blank, source_col].map(final_mapping)
      frame.set_data([], query=final_mapping, source='pandas')
    else:
      imputation_function = flow.slots['function'].value
      frame.set_data([], query=str(imputation_function), source='pandas')

    return flow, frame

  def identify_blank_rows(self, target_col, table_df):
    blank_value = find_default_value(table_df[target_col])
    is_blank = (table_df[target_col].isna() | (table_df[target_col] == blank_value))
    num_blank_rows = len(table_df[is_blank])
    return num_blank_rows, is_blank

  def imputation_rerouting(self, convo_history, flow, state, table_df, data_preview=None):
    prefix = "We think we should perform imputation, but "
    suffix = ""
    if not flow.slots['target'].filled:
      suffix = "we don't know which column to fill."

    ambiguous_sources = len(flow.slots['source'].values) >= 8
    if ambiguous_sources:
      suffix = "we just want to double check."
    if flow.tracker.num_issues == 0:
      suffix = "it looks like there are no missing values to impute."
    elif flow.tracker.num_issues == len(table_df):
      suffix = "the entire column is empty and needs to be filled in."
    elif flow.tracker.num_issues > 4096:
      flow.fall_back = '468'  # Go to Resolve flow for planning

    if len(suffix) > 0:
      warning = prefix + suffix
      if data_preview is None:
        data_preview = PromptEngineer.display_preview(table_df, max_rows=16)

      prompt = impute_routing_prompt.format(warning_msg=warning, history=convo_history,
                                            data_preview=data_preview, thought=state.thought)
      decision = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

      flow.is_uncertain = True
      match decision['scope']:
        case 'update': flow.fall_back = '006'
        case 'resolve': flow.fall_back = '468'
        case 'pattern': flow.fall_back = '0BD'
        case 'insert': flow.fall_back = '005'
        case 'impute': flow.is_uncertain = False
        case _: flow.completed = True

    return flow, state

  def pattern_fill(self, context, state, world):
    # Supports {0BD} by flash filling the data within the column based on the pattern in the existing data
    flow = state.get_flow(flow_type='pattern')
    frame = self.default_frame(state, state.entities)

    if flow.slots['target'].filled and not flow.is_uncertain:
      main_entity = flow.slots['target'].values[0]
      tab_name, target_col = main_entity['tab'], main_entity['col']
      table = self.database.db.tables[tab_name]
      convo_history = context.compile_history()

      if len(flow.slots['source'].values) == 0:
        supporting_columns = [target_col]
      else:
        supporting_columns = [entity['col'] for entity in flow.slots['source'].values]
      data_preview = PromptEngineer.display_preview(table, supporting_columns, max_rows=16, signal_limit=False)

      if flow.is_filled():
        detail_dict = {
          'Prior thoughts': flow.slots['pattern'].value,
          'Target column': f"{target_col} in {tab_name}",
          'Base value': flow.slots['base'].value,
          'Formula sketch': flow.slots['snippet'].value
        }
        supporting_details = '\n'.join([f"* {detail}: {fact}" for detail, fact in detail_dict.items()])
        prompt = pattern_code_prompt.format(df_tables=self.database.table_desc, history=convo_history,
                                            supporting=supporting_details, preview=data_preview)
        db_output, code = self.database.manipulate_data(context, state, prompt, world.valid_tables)

        if code == 'error':
          frame = Frame(state.current_tab)
          frame.signal_failure('code_generation', db_output.strip())
          self.actions.add("SHARE_MSG")
        else:
          frame = self.validate_dataframe(db_output, code, 'pandas', state, tab_type='direct')
          self.database.db.complete_registration(tab_name)
          self.update_system_prompt([state.current_tab], world, context, flow)
          flow.completed = True

      else:
        prompt = find_pattern_prompt.format(target_column=target_col, preview=data_preview, history=convo_history)
        raw_output = self.api.execute(prompt)
        prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

        if flow.fill_slot_values(tab_name, prediction):
          frame, state = self.pattern_fill(context, state, world)
        elif flow.slots['pattern'].filled:
          self.actions.add('CLARIFY')
          if not flow.slots['base'].filled:
            slot_goal = 'what the first value should be'
          elif not flow.slots['snippet'].filled:
            slot_goal = 'exactly how each row is derived from the previous row'
          state.ambiguity.declare('specific', flow='pattern', slot=slot_goal)
        else:  # pattern_slot is not filled
          frame, state = self.pattern_rerouting(context, flow, state, world)
    else:
      frame, state = self.pattern_rerouting(context, flow, state, world)

    return frame, state

  def pattern_rerouting(self, context, flow, state, world):
    frame = world.frames[-1] if world.has_data() else self.default_frame(state)
    tab_col_str = PromptEngineer.tab_col_rep(world)
    routing_prompt = pattern_routing_prompt.format(history=context.compile_history(), valid_tab_col=tab_col_str)
    pred_situation = self.api.execute(routing_prompt)

    match pred_situation:
      case 'pattern': flow.stage = 'pick-tab-col'
      case 'update': flow.fall_back = '006'
      case 'impute': flow.fall_back = '06B'
      case _: state.store_dacts('', '9DF')

    if flow.stage == 'pick-tab-col':
      self.actions.add('INTERACT')
      frame.tab_type = 'decision' # Convert the type into a 'decision' table
    elif state.get_dialog_act() == '9DF':
      self.actions.add('CLARIFY')
      state.ambiguity.declare('general')

    return frame, state

  def remove_duplicates(self, context, state, world):
    """Supports {7BD} by merging duplicate rows. If any conflicts arise, signal the Merge Style stage
    Upon success, we also need to update across the entire world, including prompts and metadata"""
    flow = state.get_flow(flow_type='dedupe')
    self.actions.add('INTERACT')
    frame, state = self.initialize_merge_frame(flow, state, world)

    if flow.slots['removal'].is_verified() and not flow.is_uncertain:

      if flow.slots['style'].value == 'question':
        no_remaining_batches = flow.tracker.batch_number > 0 and len(flow.tracker.conflicts) <= 10
        final_active_conflict = len(frame.active_conflicts) == flow.tracker.cardset_index

        if no_remaining_batches and final_active_conflict and flow.tracker.forward_resolution():
          # if we have reviewed all batches, just wrap up immediately since there are no more conflicts to review
          num_reviewed = len(frame.active_conflicts)
          flow.tracker.conflicts = flow.tracker.conflicts[num_reviewed:]
          frame, flow = self.complete_duplicate_removal(frame, flow, context, world)
        elif flow.slots['confidence'].check_if_filled():
          # high confidence may be achieved immediately if the user selects the 'automatic' merge method
          flow.slots['style'].value = ''    # clear out to the elective to get back to merge-style stage
          self.remove_duplicates(context, flow, state, world)
        else:
          data_schema = world.metadata['schema']
          frame.tab_type = 'dynamic'
          frame, flow = self.review_duplicate_rows(frame, flow, data_schema)
          if not flow.tracker.has_conflicts():  # there are no duplicates to begin with
            self.complete_duplicate_removal(frame, flow, context, world)

      elif flow.is_filled():  #  confidence is high and some merge style has been chosen, so we can wrap up
        frame, flow = self.complete_duplicate_removal(frame, flow, context, world)

      elif trivial_duplicate_case(flow, self.database.db.tables, world):
        # when user selects all columns, identifying duplicate rows is trivial, so we assign a merge style and wrap up
        flow.assign_default_style(finish=True)
        frame, flow = self.complete_duplicate_removal(frame, flow, context, world)

      else:
        flow.stage = 'merge-style'  # continue process by having the user choose a merge style
        valid_col_list = world.valid_columns[state.current_tab]
        tab_schema = world.metadata['schema'][state.current_tab]
        flow = possible_reference_columns(flow, tab_schema, valid_col_list)
        if self.rank_row_merge_styles(context, flow, valid_col_list):
          frame, flow = self.complete_duplicate_removal(frame, flow, context, world)

    else:
      flow.stage = 'pick-tab-col'   # kick off merge process by allowing user to pick tables and columns
      frame.tab_type = 'decision' # Convert the type into a 'decision' table

    return frame, state

  def prepare_duplicate_cards(self, frame, flow):
    # Sample a batch of conflict cards from the detector representing potential duplicates
    sampled_cardsets = flow.tracker.sample_conflicts()
    tab_name = flow.detector.side_to_tab['left']
    table_df = self.database.db.tables[tab_name]

    for cardset_info in sampled_cardsets:
      cardset = {'cards': [], 'tables': [tab_name], 'row_ids': []}

      for cid in cardset_info:
        row_id, columns = cid['row_id'], cid['columns']
        card = {col: serialize_for_json(table_df.at[row_id, col]) for col in columns}
        cardset['cards'].append(card)
        cardset['row_ids'].append(row_id)

      frame.active_conflicts.append(cardset)
    return frame, flow

  def complete_duplicate_removal(self, frame, flow, context, world):
    source_entity = flow.slots['removal'].values[0]
    current_tab = source_entity['tab']
    current_df = self.database.db.tables[current_tab]
    if len(flow.tracker.results) + len(flow.tracker.conflicts) == 0:
      cols_to_remove = [entity['col'] for entity in flow.slots['removal'].values]
      deduped_df = current_df.drop_duplicates(subset=cols_to_remove)  # removes with order/first style by default
      duplicates = current_df.index.difference(deduped_df.index)
      self.database.db.tables[current_tab] = deduped_df
    else:
      duplicates = flow.detect_duplicates(current_df)
      self.database.db.tables[current_tab] = current_df.drop(index=list(duplicates))

    self.update_system_prompt([current_tab], world, context, flow)
    self.actions.remove('INTERACT')

    frame.active_conflicts = []
    frame.tab_type = 'direct'
    num_remaining = len(self.database.db.tables[current_tab])
    frame.properties['row_counts'] = (len(duplicates), num_remaining)

    flow.detector.reset()
    flow.stage = 'completed'
    flow.completed = True  # mark as completed, rather than removing. Leave that to RES
    return frame, flow

  def review_duplicate_rows(self, frame, flow, data_schema):
    """ review duplicates that may have caused conflicts, and decide how to merge them """
    if flow.tracker.batch_number == 0:
      # kick off the first batch of conflict cards, which is treated slightly differently
      flow.tracker.increment_batch()
      # Embed all the rows and sample a batch of conflict cards for review
      flow.detector.set_tab_col(flow.slots['removal'].values, self.database.db.tables)

      # There should only be one table, start by determining the display columns of that table
      tab_name = flow.detector.side_to_tab['left']
      table_df = self.database.db.tables[tab_name]
      tab_schema = {col: data_schema[tab_name].get_type_info(col) for col in table_df.columns}
      flow.detector.add_display_columns(table_df.columns, tab_name, tab_schema, 'left')

      # Encode the table just once, then search for autofixes and conflicts
      flow.detector.encode(table_df, tab_name, tab_schema)
      autofixes, conflicts = flow.detector.single_tab_duplicates(table_df, tab_name, tab_schema)
      flow.tracker.store_cardsets(autofixes, conflicts)
      frame, flow = self.prepare_duplicate_cards(frame, flow)

      if flow.slots['confidence'].level > 0.5:
        flow.stage = 'combine-progress'
      else:
        flow.stage = 'combine-cards'
        frame = flow.tracker.combine_cards_action(frame)

    else:  # batch_number > 0
      if flow.tracker.still_resolving():
        flow.stage = 'combine-cards'
        frame = flow.tracker.combine_cards_action(frame)
      else:  # cardset_index == 10
        # Every N batches, merge all rows approved by the user and recompute the embeddings to keep it up-to-date
        if flow.tracker.batch_number % 8 == 0:
          tab_name = flow.slots['removal'].values[0]['tab']
          table_df, _ = self.merge_rows(flow.tracker.results, flow)
          tab_schema = {col: data_schema[tab_name].get_type_info(col) for col in table_df.columns}
          flow.detector.encode(table_df, tab_name, tab_schema)
          flow.detector.single_tab_duplicates(table_df, tab_name, tab_schema)
        frame, flow = self.combine_progress_action(frame, flow)

    # mark as incomplete even though all slots are filled to prevent it from being popped off the flow stack
    flow.completed = False
    return frame, flow

  def merge_rows(self, row_groups, flow):
    # row_groups is a list of lists, where each inner list contains the indices of rows that should be merged
    tab_name = flow.slots['removal'].values[0]['tab']
    current_df = self.database.db.tables[tab_name]
    merge_style = flow.slots['style'].value
    reference_col = flow.slots['settings'].value['reference']
    setting_detail = flow.slots['settings'].value['detail']
    bool_setting = flow.slots['settings'].value['boolean']

    # TODO: default is to keep first occurrence, but we should pre-process to keep non-null values if first row is empty
    rows_to_keep = []
    for group in row_groups:
      subset = current_df.loc[group]
      smc = subset[reference_col]

      try:
        match merge_style:
          case 'order': keep_row = subset.iloc[0] if bool_setting else subset.iloc[-1]
          case 'time': keep_row = subset.sort_values(by=reference_col, ascending=bool_setting).iloc[0]
          case 'binary': keep_row = subset[smc == bool_setting].iloc[0]
          case 'contains': keep_row = subset[smc.astype(str).str.contains(setting_detail)].iloc[0]
          case 'add': keep_row = self.combine_values(subset, reference_col, merge_style)
          case 'subtract': keep_row = self.combine_values(subset, reference_col, merge_style)
          case 'size': keep_row = subset.iloc[smc.idxmax() if bool_setting else smc.idxmin()]
          case 'length': keep_row = subset.iloc[smc.astype(str).str.len().argmax() if bool_setting else smc.astype(str).str.len().argmin()]
          case 'alpha': keep_row = subset.sort_values(by=reference_col, ascending=bool_setting).iloc[0]
          case 'mean': keep_row = self.combine_values(subset, reference_col, merge_style)
          case 'concat': keep_row = self.combine_content(subset, reference_col, sep='')
          case 'space': keep_row = self.combine_content(subset, reference_col, sep=' ')
      except IndexError:
        # This error means that after filtering, the subset is empty, so we fall back to just using the first row
        keep_row = subset.iloc[0]
      rows_to_keep.append(keep_row)

    # Make a dataframe containing newly combined rows and previously existing rows
    combined_df = pd.DataFrame(rows_to_keep).reset_index(drop=True)
    indices_to_remove = set(sum(row_groups, []))
    remaining_df = current_df.drop(index=list(indices_to_remove))
    revised_df = pd.concat([combined_df, remaining_df], ignore_index=True)
    self.database.db.tables[tab_name] = revised_df
    print(f"Started with {len(current_df)} rows, and now have {len(revised_df)} rows after removing duplicates.")
    return revised_df, tab_name

  def combine_values(self, subset, column, style, bool_setting=True):
    new_row = subset.iloc[0].copy()
    if style == 'add':
      new_row[column] = subset[column].sum()
    elif style == 'subtract':
      new_row[column] = subset[column].diff().iloc[-1]
    elif style == 'multiply':
      new_row[column] = subset[column].prod()
    elif style == 'divide':
      if bool_setting:
        new_row[column] = subset[column].iloc[0] / subset[column].iloc[-1]
      else:
        new_row[column] = subset[column].iloc[-1] / subset[column].iloc[0]
    elif style == 'mean':
      new_row[column] = subset[column].mean()
    elif style == 'power':
      if bool_setting:
        new_row[column] = subset[column].iloc[0] ** subset[column].iloc[-1]
      else:
        new_row[column] = subset[column].iloc[-1] ** subset[column].iloc[0]
    elif style == 'log':
      if bool_setting:
        new_row[column] = np.log(subset[column].iloc[0]) / np.log(subset[column].iloc[-1])
      else:
        new_row[column] = np.log(subset[column].iloc[-1]) / np.log(subset[column].iloc[0])
    return new_row

  def combine_content(self, subset, column, sep):
    # TODO: default behavior for numeric columns is to average beforehand, rather than just taking the first occurrence
    new_row = subset.iloc[0].copy()
    new_row[column] = sep.join(subset[column].astype(str))
    return new_row

  def rank_row_merge_styles(self, context, flow, valid_col_list):
    # Determine the top merge styles based on context, and store them in the Style slot
    candidate_slot = flow.slots['candidate']
    reviewed_results = [res for res in flow.tracker.results if res['reviewed']]

    if flow.tracker.batch_number > 1 and len(reviewed_results) >= 10:  # we have completed at least one full batch
      """ Test each possible merge style in order of importance. Automatically merge if the merge style matches
      at least 90% of the reviewed conflicts. Otherwise, prompt the user to pick from the top 3 options """
      source_entity = flow.slots['removal'].values[0]
      current_tab = source_entity['tab']
      current_df = self.database.db.tables[current_tab]
      merged_results = [res for res in reviewed_results if res['resolution'] == 'merge']

      style_applicability = []
      for style in candidate_slot.options:
        reference_col = flow.slots['settings'].value['reference'].get(f'{style}_col', '')
        if len(reference_col) > 0 or style == 'order':
          for style_detail in style_mapping[style]:
            style_rating = apply_merge_styles(current_df, style, style_detail, reference_col, merged_results)
            if style_rating >= 0.9:
              flow.slots['style'].assign_one(style)
              return True   # we have immediately found a suitable merge style
          style_applicability.append({'name': style, 'rate': style_rating})

      # sort the styles by success rate and store the top 3 results in the Candidate slot
      style_applicability.sort(key=lambda x: x['rate'], reverse=True)
      candidate_slot.values = [style['name'] for style in style_applicability[:3]]

    else:
      selected_cols = [entity['col'] for entity in flow.slots['removal'].values]
      col_info = make_column_list(valid_col_list, selected_cols)
      prompt = row_styles_prompt.format(history=context.compile_history(), column_info=col_info)
      raw_output = self.api.execute(prompt)
      prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

      for style, pred_col in prediction['methods']:
        candidate_slot.add_one(style)
        for style_option in candidate_slot.options:
          reference = flow.slots['settings'].value['reference']
          if isinstance(reference, str):
            reference = {}     # if reference is still a string, then convert it to a dictionary
          if style == style_option:
            reference[f'{style}_col'] = pred_col
          flow.slots['settings'].value['reference'] = reference
    return False

  def assign_datatype(self, context, state, world):
    # Supports {06E} by setting the data type of the column to a specific format
    flow = state.get_flow(flow_type='datatype')

    if flow.slots['source'].filled and not flow.is_uncertain:
      entity = flow.slots['source'].values[0]
      tab_name, col_name = entity['tab'], entity['col']
      tab_schema = world.metadata['schema'][tab_name]
      original_props = tab_schema.get_type_info(col_name)
      table = self.database.db.tables[tab_name]
      shadow_column = self.database.db.shadow.display_as_type(table, **original_props, tab_name=tab_name)

      previous_frame = world.frames[-1] if world.has_data() else self.default_frame(state, world.valid_columns)
      issue_ent = previous_frame.issues_entity
      frame = Frame(tab_name)
      
      if state.has_issues and issue_ent.get('col') == col_name and issue_ent.get('tab') == tab_name:
        flow_name = state.get_flow(issue_ent.get('flow', 'none'), return_name=True)
        if flow_name == 'Detect(problem)' and 'row' in issue_ent and not flow.slots['subtype'].filled:
          col_datatype, col_subtype = original_props['type'], original_props['subtype']
          flow.slots['datatype'].assign_one(col_datatype)
          flow.slots['subtype'].assign_one(col_subtype)

      if flow.slots['subtype'].filled:
        subtype = flow.slots['subtype'].value
        if not flow.slots['datatype'].filled:
          datatype = flow.subtype_to_datatype(subtype)
        else:
          datatype = flow.slots['datatype'].value

        col_problems = self.database.db.shadow.problems.get(col_name, {})
        if any([entity['row'] < 0 for entity in flow.slots['source'].values]):
          rows = col_problems
        else:
          rows = {}
          for entity in flow.slots['source'].values:
            if entity['row'] in col_problems:
              rows[entity['row']] = col_problems[entity['row']]
        row_ids = list(rows.keys())

        if len(rows) > 0:
          col_dtype = table[col_name].dtype # we want the original target dtype before conversion
          table[col_name] = table[col_name].astype(str)  # convert to string now for manipulation
          row_samples, table = self.compile_row_samples(rows, table, col_name)
          convo_history = context.compile_history()
          prompt = change_datatype_prompt.format(column=col_name, samples='\n'.join(row_samples),
                            datatype=datatype, subtype=subtype, dtype=col_dtype, history=convo_history)
          support = { 'prompt': prompt, 'task': flow.name(), 'row_ids': row_ids }
          db_output, code = self.database.execute_on_custom_df(state, context, support, table)

          if code == 'error':
            frame = Frame(state.current_tab)
            frame.signal_failure('code_generation', db_output.strip())
            self.actions.add("SHARE_MSG")
          else:
            frame = self.validate_dataframe(db_output, code, 'pandas', state, tab_type='direct')

        supplement = original_props.get('supplement', {})
        try:
          for row_id in row_ids:
            del self.database.db.shadow.problems[col_name][row_id]

          if col_name in self.database.db.shadow.problems and len(self.database.db.shadow.problems[col_name]) == 0:
            del self.database.db.shadow.problems[col_name]
            if col_name in self.database.db.shadow.table:
              del self.database.db.shadow.table[col_name]

          tab_schema.set_type_info(col_name, datatype, subtype, supplement)
          flow.completed = True

        except Exception as ecp:
          # put the original properties back in place, and ask the user to resolve the issue
          tab_schema.set_type_info(col_name, original_props['type'], original_props['subtype'], supplement)
          self.actions.add('CLARIFY')
          state.ambiguity.declare('specific')

      else:  # predict the subtype for the column based on conversation
        convo_history = context.compile_history()
        shadow_md = shadow_column.to_frame(name=col_name).head(32).to_markdown(index=False)

        prompt = subtype_prompt.format(column=col_name, history=convo_history, dtype=original_props['type'], 
                                       subtype=original_props['subtype'], preview=shadow_md)
        prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

        if flow.fill_slot_values(tab_name, prediction):
          frame, state = self.assign_datatype(context, state, world)
        elif flow.is_uncertain and len(flow.slots['subtype'].detail) > 0:
          self.actions.add('CLARIFY')
          subtype_options = flow.slots['subtype'].detail
          state.ambiguity.declare('confirmation', flow='datatype', values=subtype_options, generate=True)
        else:  # we have some options for formats, but need to ask the user to pick one
          self.actions.add('SUGGEST')
    else:
      valid_ent_list = state.dict_to_entity(world.valid_columns)
      frame = world.frames[-1] if world.has_data() else self.default_frame(state, valid_ent_list)
      routing_prompt = datatype_routing_prompt.format(history=context.compile_history(), tab_name=state.current_tab)
      raw_output = self.api.execute(routing_prompt)
      prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

      match prediction['task']:
        case 'validate': flow.fall_back = '36D'
        case 'format': flow.fall_back = '36F'
        case 'typo': flow.fall_back = '46D'
        case 'update': flow.fall_back = '006'
        case 'datatype': flow.stage = 'pick-tab-col'
        case _: state.store_dacts('', '9DF')

      if flow.stage == 'pick-tab-col':
        self.actions.add('INTERACT')
        frame.tab_type = 'decision'
      elif state.get_dialog_act() == '9DF':
        self.actions.add('CLARIFY')
        state.ambiguity.declare('general')

    return frame, state

  def compile_row_samples(self, rows, table, col_name):
    problem_to_row = defaultdict(list)
    for row_id, problem_value in rows.items():
      problem_string = str(problem_value)
      problem_to_row[problem_string].append(row_id)
      table.loc[row_id, col_name] = problem_string
    unique_problems = set(list(problem_to_row.keys())[:10])
    unique_values = table[col_name].fillna('<N/A>').unique()
    display_values = unique_problems.union(set(unique_values[:10]))

    row_samples = []
    for display_val in display_values:
      if display_val in unique_problems:
        if len(problem_to_row[display_val]) > 4:
          num_problems = len(problem_to_row[display_val])
          unique_str = f"{display_val} <-- {num_problems} instances"
        else:
          unique_str = f"{display_val} <-- {problem_to_row[display_val]}"
      else:
        unique_str = display_val
      row_samples.append(unique_str)

    if len(unique_values) > 16:
      diff = len(unique_values) - 16
      row_samples.append(f"[{diff} other unique values ...]")

    return row_samples, table

  def undo_action(self, context, state, world):
    # Supports {06F} by undoing the last action taken on the table
    flow = state.get_flow(flow_type='undo')
    valid_ent_list = state.dict_to_entity(world.valid_columns)
    previous_frame = world.frames[-1] if world.has_data() else self.default_frame(state, valid_ent_list)
    tab_name = state.entities[0]['tab'] if len(state.entities) > 0 else state.current_tab

    prompt = undo_prompt.format(history=context.compile_history())
    do_carry = self.api.execute(prompt)

    if 'yes' in do_carry.lower():
      db_output, sql_query = previous_frame.get_data(), previous_frame.code
    else:
      db_output, sql_query = self.database.query_data(context, flow, state, world)

    return self.validate_dataframe(db_output, sql_query, 'sql', state)

  def persist_preference(self, context, state, world):
    # Supports {068} by saving or updating a user preference
    flow = state.get_flow(flow_type='persist')
    valid_ent_list = state.dict_to_entity(world.valid_columns)
    previous_frame = world.frames[-1] if world.has_data() else self.default_frame(state, valid_ent_list)
    tab_name = state.entities[0]['tab'] if len(state.entities) > 0 else state.current_tab

    prompt = persist_prompt.format(history=context.compile_history())
    do_carry = self.api.execute(prompt)

    if 'yes' in do_carry.lower():
      db_output, sql_query = previous_frame.get_data(), previous_frame.code
    else:
      db_output, sql_query = self.database.query_data(context, flow, state, world)

    return self.validate_dataframe(db_output, sql_query, 'sql', state)