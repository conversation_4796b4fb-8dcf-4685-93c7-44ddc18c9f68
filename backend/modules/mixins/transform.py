import random
import copy
import numpy as np
import pandas as pd
from collections import defaultdict

from backend.modules.flow import flow_selection
from backend.prompts.mixins.for_transform import *
from backend.prompts.for_nlu import move_element_prompt
from backend.assets.ontology import default_limit

from backend.components.engineer import PromptEngineer
from backend.components.frame import Frame
from backend.utilities.templates import insert_clarification, delete_clarification
from backend.utilities.manipulations import *

class TransformMixin:
  """ Methods to manipulate or change the structure of the spreadsheet """

  def transform_safety_net(self, frame, snapshot_df, state):
    """ Double check that the error message is not a false positive in the case of NaNs """
    if frame.error == 'null_values_found':
      # find the columns that are causing errors
      broken_df = frame.get_data(limit=5)
      null_columns = broken_df.columns[broken_df.iloc[0].isna()]

      # if the columns you changed aren't in this list, then the error likely isn't caused by our actions
      probable_cause = False
      for entity in state.entities:
        if entity['col'] in null_columns:
          probable_cause = True

      # if the problematic columns were null to begin with, then that's not our fault either
      previous_nulls = snapshot_df.columns[snapshot_df.iloc[0].isna()]
      already_exists = [null_col in previous_nulls for null_col in null_columns]
      if all(already_exists):
        probable_cause = False

      # when faced with a true positive, fallback to a default frame and declare a warning
      if probable_cause:
        previous_warning = frame.warning
        frame = Frame(state.current_tab)
        frame.warning = previous_warning

    return frame

  def insert_action(self, context, state, world):
    # Router for {005} to decide between inserting values with or without issues, also marks flow as completed when done
    flow = state.get_flow(flow_type='insert')

    if flow.is_uncertain:
      self.actions.add('CLARIFY')
      flow.is_uncertain = False
      state.ambiguity.declare('specific')
      frame = Frame(state.current_tab)
      return frame, state

    if state.has_issues:
      frame, state = self.interpolate_issues(flow, context, state, world)
    elif state.has_plan:
      frame, state = self.execute_insert_step(flow, context, state, world)
    else:
      frame, state = self.insert_values(flow, context, state, world)

    if frame.is_successful():
      flow.completed = True
      state, context = check_slice_preferences(context, flow, state)

      unique_tables = set([ent['tab'] for ent in flow.slots['target'].values])
      if len(unique_tables) > 1:
        updated_tabs = list(unique_tables)
        frame.properties['tabs'] = updated_tabs
      else:
        updated_tabs = [state.current_tab]

      self.update_system_prompt(updated_tabs, world, context, flow)
    return frame, state

  def interpolate_issues(self, flow, context, state, world):
    previous_frame = world.frames[-1]
    issue_tab, issue_col = previous_frame.issues_entity['tab'], previous_frame.issues_entity['col']
    issue_df = self.take_snapshot(issue_tab)

    if flow.slots['exact'].value == 'ignore':
      self.actions.add('ISSUE|ignore')
      selected_rows, _ = self.select_issue_rows(flow, previous_frame, issue_col, issue_df)
      resolve_flow, previous_frame, state = self.resolve_issues(selected_rows, previous_frame, state, world)
      frame = Frame(issue_tab)
      frame.resolved_rows = previous_frame.resolved_rows

    elif flow.slots['exact'].value == 'beyond':
      frame, state = self.insert_values(flow, context, state, world)

    else:     # actually interpolate new values for the rows with issues
      self.actions.add("ISSUE|interpolate")
      all_cols = PromptEngineer.array_to_nl(issue_df.columns, connector='and')
      df_desc = f"The full dataframe includes {len(issue_df)} rows and additional columns: {all_cols}."

      agent_text = context.last_utt(speaker='Agent')
      user_text = context.last_utt(speaker='User')
      if len(agent_text) == 0:
        history = f"Agent: How would you like to interpolate the new values?\nUser: {user_text}"
      else:
        history = f"Agent: {agent_text}\nUser: {user_text}"
      selected_rows, subset_df = self.select_issue_rows(flow, previous_frame, issue_col, issue_df)
      subset_md = PromptEngineer.display_preview(subset_df)

      prompt = transform_issues_prompt.format(df_description=df_desc, convo_history=history, issues=subset_md)
      valid_tabs = [subset_df, issue_df, 'Detect']  # issue_df is necessary for global calculations, do not remove
      subset_df, pandas_code = self.database.manipulate_data(self.api, context, state, prompt, valid_tabs)

      if pandas_code == 'error':
        frame = previous_frame
        frame.signal_failure('code_generation', subset_df.strip())   # subset_df is actually an error message
        return frame, state
      else:
        resolve_flow, previous_frame, state = self.resolve_issues(selected_rows, previous_frame, state, world)
        self.database.update_data(issue_tab, subset_df)

        if resolve_flow.completed:
          frame = self.validate_dataframe(issue_df, pandas_code, 'pandas', state, tab_type='direct')
          frame.set_data([])  # clear out the stored data to force a reload
        else:
          remaining_rows = previous_frame.issues_entity['row'].keys()
          remaining_df = issue_df.loc[remaining_rows, [issue_col]]
          frame = self.validate_dataframe(remaining_df, pandas_code, 'pandas', state, tab_type='dynamic')
          frame.issues_entity = previous_frame.issues_entity
        frame.resolved_rows = previous_frame.resolved_rows

    return frame, state

  def insert_values(self, flow, context, state, world):
    """ Supports {005} by deciding what content to insert, then notifies the database accordingly """
    user_text = context.last_utt(speaker='User')

    if flow.slots['target'].filled:
      source_columns = [ent['col'] for ent in flow.slots['source'].values]
      tab_name = flow.slots['target'].values[0]['tab']  # must be target since source slot is optional
      current_cols = PromptEngineer.array_to_nl(source_columns, connector='and')
      source_desc = f"{current_cols} in {tab_name}"
      max_rows = get_row_limit(len(source_columns), default=8)

      df_head = PromptEngineer.display_preview(self.database.db.tables[tab_name], source_columns, max_rows)
      operations = ';'.join(flow.slots['operation'].values)
      new_cols = PromptEngineer.column_rep(state.entity_to_dict(flow.slots['target'].values), with_break=False)

      if len(state.thought) == 0:
        thought_desc = "I will insert the data just as the user requested"
      else:
        thought_desc = state.thought

      prompt = insert_prompt.format(df_tables=self.database.table_desc, utterance=user_text, source_cols=source_desc,
                          example_rows=df_head, target_cols=new_cols, operations=operations, thought=thought_desc)
      db_output, code = self.database.manipulate_data(self.api, context, state, prompt, world.valid_tables)
      if code == 'error':
        frame = Frame(state.current_tab)
        frame.signal_failure('code_generation', db_output.strip())
        self.actions.add("SHARE_MSG")
      else:
        frame = self.validate_dataframe(db_output, code, 'pandas', state, tab_type='direct')

    elif flow.slots['source'].filled:
      self.actions.add("CLARIFY")
      frame = Frame(state.current_tab)
      frame.signal_failure('custom', 'it is unclear what the user would like to insert')
      state.ambiguity.declare('specific', flow='insert', generate=True)
      state.ambiguity.observation = insert_clarification(flow)

    else:
      self.actions.add('INTERACT')
      flow.stage = 'pick-tab-col'     # save the process by allowing user to pick source columns
      frame = Frame(state.current_tab)
      frame.tab_type = 'decision'     # Convert the type into a 'decision' table

    return frame, state

  def delete_action(self, context, state, world):
    # Router for {007} to decide between deleting values or deleting issues, also marks flow as completed when done
    flow = state.get_flow(flow_type='delete')

    if flow.is_uncertain:
      self.actions.add('CLARIFY')
      flow.is_uncertain = False
      state.ambiguity.declare('specific')
      frame = Frame(state.current_tab)
      return frame, state

    if state.has_issues:
      frame, state = self.remove_issues(flow, context, state, world)
    else:
      frame, state = self.delete_values(flow, context, state, world)

    if frame.is_successful():
      flow.completed = True

      unique_tables = set([ent['tab'] for ent in flow.slots['removal'].values])
      if len(unique_tables) > 1:
        updated_tabs = list(unique_tables)
        frame.properties['tabs'] = updated_tabs
      else:
        updated_tabs = [state.current_tab]

      self.update_system_prompt(updated_tabs, world, context, flow)
    return frame, state

  def execute_insert_step(self, flow, context, state, world):
    """ Supports interjected Insert flows when triggered by plan within a Detect flow """
    orig_flow_name = self.dax_to_flowtype[flow.origin]
    original_flow = state.get_flow(flow_type=orig_flow_name, allow_interject=False)
    convo_history = context.compile_history()

    if not flow.slots['target'].filled:
      tab_col_string = PromptEngineer.tab_col_rep(world)
      plan = PromptEngineer.display_plan(original_flow.slots['plan'].steps, join_key='\n')

      orig_columns = [ent['col'] for ent in original_flow.slots['source'].values]
      orig_table = original_flow.slots['source'].values[0]['tab']
      current_cols = PromptEngineer.array_to_nl(orig_columns, connector='and')

      prompt = name_staging_col_prompt.format(valid_tab_col=tab_col_string, history=convo_history,
                                              plan=plan, related_cols=f"{current_cols} column in {orig_table}")
      prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

      pred_table = state.current_tab if prediction['table'] == 'current' else prediction['table']
      for pred_col_name in prediction['columns']:
        flow.slots['target'].add_one(pred_table, pred_col_name)
      if len(state.thought) == 0:
        state.thought = prediction['thought']
    tab_name = flow.slots['target'].values[0]['tab']

    user_text = context.last_utt(speaker='User')
    source_columns = [ent['col'] for ent in flow.slots['source'].values]
    current_cols = PromptEngineer.array_to_nl(source_columns, connector='and')
    source_desc = f"{current_cols} in {tab_name}"
    df_head = PromptEngineer.display_preview(self.database.db.tables[tab_name], source_columns, max_rows=8)

    if flow.slots['operation'].filled:
      operations = ';'.join(flow.slots['operation'].values)
    else:
      operations = original_flow.slots['plan'].current_step()

    if len(state.thought) == 0:
      thought_desc = "I will insert the staging column just as the user requested"
    else:
      thought_desc = state.thought
    new_cols = PromptEngineer.column_rep(state.entity_to_dict(flow.slots['target'].values), with_break=False)

    prompt = insert_prompt.format(df_tables=self.database.table_desc, utterance=user_text, source_cols=source_desc,
                        example_rows=df_head, target_cols=new_cols, operations=operations, thought=thought_desc)
    task = flow.name(full=True)
    valid_tabs = world.valid_tables
    db_output, code = self.database.execute_with_retries(self.api, state, task, prompt, convo_history, df_head, valid_tabs)

    if code == 'error':
      frame = Frame(state.current_tab)
      frame.signal_failure('code_generation', db_output.strip())
      self.actions.add("SHARE_MSG")
    else:
      frame = self.validate_dataframe(db_output, code, 'pandas', state, tab_type='direct')
      state = self.backfill_underlying_flow(context, flow, frame, state)

    return frame, state

  def remove_issues(self, flow, context, state, world):
    previous_frame = world.frames[-1]
    issue_tab, issue_col = previous_frame.issues_entity['tab'], previous_frame.issues_entity['col']
    issue_df = self.take_snapshot(issue_tab)

    if flow.slots['exact'].value == 'ignore':
      self.actions.add('ISSUE|ignore')
      selected_rows, _ = self.select_issue_rows(flow, previous_frame, issue_col, issue_df)
      resolve_flow, previous_frame, state = self.resolve_issues(selected_rows, previous_frame, state, world)
      frame = Frame(issue_tab)
      frame.resolved_rows = previous_frame.resolved_rows

    elif flow.slots['exact'].value == 'beyond':
      frame, state = self.remove_values(flow, context, state, world)
    elif flow.slots['exact'].value == 'convert':
      frame = Frame(issue_tab)
      flow.fall_back = '06E'
    elif flow.slots['exact'].value == 'update':
      frame = Frame(issue_tab)
      flow.fall_back = '006'

    else:     # actually remove the rows with issues
      self.actions.add("ISSUE|remove")
      selected_rows, _ = self.select_issue_rows(flow, previous_frame, issue_col, issue_df)
      resolve_flow, previous_frame, state = self.resolve_issues(selected_rows, previous_frame, state, world)
      self.database.remove_data(issue_tab, selected_rows)

      if resolve_flow.completed:
        frame = Frame(issue_tab)
        frame.set_data([])  # clear out the stored data to force a reload
      else:
        remaining_rows = previous_frame.issues_entity['row'].keys()
        remaining_df = issue_df.loc[remaining_rows, [issue_col]]
        issue_type = previous_frame.resolved_rows[0][1]
        pandas_code = f"df['{issue_tab}'].drop({issue_type}_rows, inplace=True)"
        frame = self.validate_dataframe(remaining_df, pandas_code, 'pandas', state, tab_type='dynamic')
        frame.issues_entity = previous_frame.issues_entity
      frame.resolved_rows = previous_frame.resolved_rows

    return frame, state

  def delete_values(self, flow, context, state, world):
    """ Supports {007} by deciding what content to delete, then notifies the database accordingly
    Upon success, we also need to update across the entire world, including prompts and metadata """
    convo_history = context.compile_history(look_back=3)
    tab_desc = self.database.table_desc

    if flow.slots['removal'].filled:
      if len(state.entities) > 0:
        loc_rep = state.tab_col_rep()
      else:
        to_remove = ';'.join([ent['col'] for ent in flow.slots['removal'].values])
        loc_rep = f"* Tables: {state.current_tab}\n* Columns: {to_remove}"
      prompt = delete_prompt.format(df_tables=tab_desc, history=convo_history, location=loc_rep, thought=state.thought)

      db_output, code = self.database.manipulate_data(self.api, context, state, prompt, world.valid_tables)
      if code == 'error':
        frame = Frame(state.current_tab)
        frame.signal_failure('code_generation', db_output.strip())
        self.actions.add("SHARE_MSG")
      else:
        frame = self.validate_dataframe(db_output, code, 'pandas', state, tab_type='direct')

    else:
      self.actions.add("CLARIFY")
      frame = Frame(state.current_tab)
      frame.signal_failure('custom', 'it is unclear what the user would like to remove')
      state.ambiguity.declare('specific', flow='delete', generate=True)
      state.ambiguity.observation = delete_clarification(flow)

    return frame, state

  def transpose_action(self, context, state, world):
    # Supports {056} by deciding how to rotate the data, then notifies the database accordingly
    user_text = context.last_utt(speaker='User')
    flow = state.get_flow(flow_type='transpose')

    if flow.slots['source'].filled and flow.slots['target'].filled:
      source_cols = [ent['col'] for ent in flow.slots['source'].values]
      target_cols = [ent['col'] for ent in flow.slots['target'].values]
      source_tab = flow.slots['source'].values[0]['tab']
      target_tab = flow.slots['target'].values[0]['tab']
      source_cols = PromptEngineer.array_to_nl(source_cols, connector='and')
      target_cols = PromptEngineer.array_to_nl(target_cols, connector='and')

      prompt = transpose_prompt.format(df_tables=self.database.table_desc, utterance=user_text,
                                      source_cols=source_cols, target_cols=target_cols, thought=state.thought)

      db_output, code = self.database.manipulate_data(self.api, context, state, prompt, world.valid_tables)
      if code == 'error':
        frame = Frame(state.current_tab)
        frame.signal_failure('code_generation', db_output.strip())
        self.actions.add("SHARE_MSG")
      else:
        frame = self.validate_dataframe(db_output, code, 'pandas', state, tab_type='direct')

    else:
      self.actions.add("CLARIFY")
      frame = Frame(state.current_tab)
      frame.warning = "it is unclear how you would like to transpose the data"

    if frame.is_successful():
      flow.completed = True
      self.update_system_prompt([state.current_tab], world, context, flow)
    return frame, state

  def cut_and_paste(self, context, state, world):
    # Supports {057} by deciding how to move the data, then notifies the database accordingly
    flow = state.get_flow(flow_type='move')

    if flow.slots['source'].filled:
      tab_name = flow.slots['source'].values[0]['tab']
      frame = Frame(tab_name)
      convo_history = context.compile_history()
      target_positions = [str(ent['rel']) for ent in flow.slots['source'].values]
      target_rows = [ent['row'] for ent in flow.slots['source'].values if len(ent['row']) > 0]

      if flow.slots['element'].filled:
        table = self.database.db.tables[tab_name]
        col_str = ', '.join([f"{col_name} ({idx+1})" for idx, col_name in enumerate(table.columns)])
        element = flow.slots['element'].value

        match element:
          case 'table':  task = 'columns in multiple tables'
          case 'column': task = 'some columns' if len(flow.slots['source'].values) > 1 else 'a column'
          case 'row':    task = 'some rows'
          case 'cell':   task = 'some cells'

        if element in ['table', 'column']:
          if flow.slots['target'].filled:
            new_col = flow.slots['target'].values[0]['col']
            add_on = f"New column name: {new_col}"
          else:
            add_on = ''
          prompt = tab_col_move_prompt.format(task=task, table=tab_name, history=convo_history,
                            columns=col_str, target=', '.join(target_positions), additional=add_on)
        elif element in ['row', 'cell']:
          source_columns = [ent['col'] for ent in flow.slots['source'].values if ent['tab'] == tab_name]
          sample_md = PromptEngineer.display_samples(table, source_columns)
          prompt = row_cell_move_prompt.format(task=task, table=tab_name, history=convo_history,
                                  columns=col_str, rows=', '.join(target_rows), samples=sample_md)

        db_output, code = self.database.manipulate_data(self.api, context, state, prompt, world.valid_tables)
        if code == 'error':
          frame.signal_failure('code_generation', db_output.strip())
          self.actions.add("SHARE_MSG")
        else:
          frame = self.validate_dataframe(db_output, code, 'pandas', state, tab_type='direct')

        self.update_system_prompt([tab_name], world, context, flow)
        flow.completed = True

      else:
        valid_col_str = PromptEngineer.column_rep(world.valid_columns, with_break=True)
        prompt = move_element_prompt.format(table=tab_name, columns=valid_col_str, history=convo_history)
        raw_output = self.api.execute(prompt)
        decisions = PromptEngineer.apply_guardrails(raw_output, 'json')

        if flow.fill_slot_values(tab_name, decisions):
          frame, state = self.cut_and_paste(context, state, world)
        else:
          self.actions.add('CLARIFY')
          state.ambiguity.declare('specific')

    else:
      self.actions.add("CLARIFY")
      self.actions.add('INTERACT')
      frame = Frame(table_name=state.current_tab, tab_type='decision')
      state.ambiguity.declare('partial', flow='move', generate=True)
      flow.stage = 'pick-tab-col'

    return frame, state

  def split_column(self, context, state, world):
    """Supports {5CD} by deciding which columns to split, which is generally viewed as text-to-columns
    Upon success, we also need to update across the entire world, including prompts and metadata """
    flow = state.get_flow(flow_type='split')
    frame = Frame(state.current_tab, tab_type='decision', source='interaction')
    self.actions.add("INTERACT")

    if flow.slots['source'].filled and not flow.is_uncertain:
      source = flow.slots['source'].values[0]
      delimiter_found = flow.slots['delimiter'].check_if_filled()
      targets_found = flow.slots['target'].check_if_filled()

      if delimiter_found or targets_found:
        convo_history = context.compile_history()
        source_df = self.database.db.tables[source['tab']][source['col']]
        source_md = PromptEngineer.display_preview(source_df, max_rows=8, signal_limit=False)
        target = [ent['col'] for ent in flow.slots['target'].values] if flow.slots['target'].filled else '<unknown>'

        symbols = []
        for symbol_name, symbol_val in flow.slots['delimiter'].value.items():
          symbol_string = f"'{symbol_val}' ({symbol_name})" if symbol_name != 'other' else f"'{symbol_val}'"
          symbols.append(symbol_string)
        delimiter_str = ' or '.join(symbols)

        prompt = text2cols_prompt.format(tab_name=source['tab'], history=convo_history, source_markdown=source_md,
                                          target_cols=target, delimiter=delimiter_str)
        db_output, pandas_code = self.database.manipulate_data(self.api, context, state, prompt, [source['tab']])

        if pandas_code == 'error':
          frame = Frame(state.current_tab)
          frame.signal_failure('code_generation', db_output.strip())
          self.actions.add("SHARE_MSG")
        else:
          frame = self.validate_dataframe(db_output, pandas_code, 'pandas', state, tab_type='direct')

        if frame.is_successful():
          self.actions.remove('INTERACT')
          flow.completed = True
          self.update_system_prompt([state.current_tab], world, context, flow)

      else:
        prompt = split_style_prompt.format(history=context.compile_history(), tab_name=source['tab'])
        raw_output = self.api.execute(prompt)
        decision = PromptEngineer.apply_guardrails(raw_output, 'json')

        if 'error' in decision or decision.get('symbol', 'unsure') == 'unsure':
          flow.stage = 'split-style'  # continue process by having the user choose what symbol to split on
        else:
          symbol_name = flow.symbol_to_name.get(decision['symbol'], 'other')
          flow.slots['delimiter'].add_one(symbol_name, decision['symbol'])
          for target_col in decision['targets']:
            flow.slots['target'].add_one(source['tab'], target_col)
          frame, state = self.text_to_cols(context, flow, state, world)
    else:
      flow.stage = 'pick-tab-col'    # kick off merge process by allowing user to pick source columns

    return frame, state

  def initialize_merge_frame(self, flow, state, world):
    """Prepare a decision table needed by Transform(join) for picking tabs/cols."""
    frame = Frame(state.current_tab, tab_type='direct', source='interaction')
    if flow.tracker.batch_number > 0:
      previous_frame = world.frames[-1]
      frame.active_conflicts = previous_frame.active_conflicts
    return frame, state

  def prepare_conflict_cards(self, frame, flow):
    # Sample a batch of conflict cards from the detector representing potential cross table matches
    sampled_cardsets = flow.tracker.sample_conflicts()
    tables = list(flow.detector.tab_to_cols.keys())

    for cardset_info in sampled_cardsets:
      cardset = {'left': [], 'right': [], 'tables': tables, 'row_ids': defaultdict(list) }

      for card in cardset_info:
        side, row_id, tab_name, columns = card['side'], card['row_id'], card['table'], card['columns']
        table_df = self.database.db.tables[tab_name]
        card = {col: serialize_for_json(table_df.at[row_id, col]) for col in columns}
        cardset[side].append(card)
        cardset['row_ids'][side].append(row_id)

      frame.active_conflicts.append(cardset)
    return frame, flow

  def combine_progress_action(self, frame, flow, cross=False):
    # When cardset_index == 10, all cardsets have been reviewed, so it's time to start learning from the examples
    flow.stage = 'combine-progress'

    # Increment the final cardset index and clear the active_conflicts list
    if len(frame.active_conflicts) > 0:
      flow.tracker.increment_cardset(frame.active_conflicts)
    frame.active_conflicts = []

    # Train the detector given the full collection of positives and negatives
    positive_groups, negative_groups = flow.tracker.labeled_cardsets()
    confidence_score = flow.detector.train(flow.tracker.conflicts, positive_groups, negative_groups, cross)
    flow.slots['confidence'].level = confidence_score
    # Re-rank the cardsets based on the updated model's prediction
    if cross:
      frame, flow = self.prepare_conflict_cards(frame, flow)
    else:
      frame, flow = self.prepare_duplicate_cards(frame, flow)

    # limit the results and cardsets lists to the most recent 512 cardsets
    if len(flow.tracker.results) > 512:
      flow.tracker.results = flow.tracker.results[-512:]

    # After model is done training, reset the flow to move onto the next batch
    flow.tracker.increment_batch()
    frame.tab_type = 'direct'
    return frame, flow

  def materialize_view(self, context, state, world):
    """ Supports {58A} converting the target derived table into a permanent direct table by registering it with the database
    Upon success, we also need to update across the entire world, including prompts and metadata """
    flow = state.get_flow(flow_type='materialize')
    frame = Frame(state.current_tab)

    if flow.slots['storage'].filled:
      storage_method = flow.slots['storage'].value

      if storage_method == 'disk':
        self.actions('INTERACT')
        tab_name = flow.slots['source'].values[0]['tab']

      elif storage_method == 'memory':
        derived_df = []
        for frame in reversed(world.frames):
          if frame.tab_type == 'derived':
            derived_df = frame.get_data()
            break

        if len(derived_df) > 0 and flow.slots['target'].filled:
          self.actions.add('CREATION')
          self.actions.add('STAGING')
          context.add_actions(['CREATION'], 'User')

          current_tab_names = list(self.database.db.tables.keys())
          new_tab_name = flow.slots['target'].values[0]['tab']
          self.database.db.tables[new_tab_name] = derived_df
          self.set_new_table_metadata(new_tab_name, world, context, derived_df, current_tab_names)

          frame = Frame(new_tab_name, source='pandas')
          frame.control['create'] = new_tab_name
          frame.has_changes = True          # set to True to force front-end to create a new tab

          flow.completed = True  # mark as completed, rather than removing. Leave that to RES
          state.current_tab = new_tab_name
      else:
        state.ambiguity.declare('specific', flow='materialize', generate=True)
        state.ambiguity.observation = "Which table are you trying to save?"

    else:
      self.actions.add("CLARIFY")
      frame.warning = "it is unclear how you would like to materialize the view"
      state.ambiguity.declare('partial', flow='materialize')

    return frame, state

  def align_connection_columns(self, context, flow, grounding):
    left_tab_name, right_tab_name = grounding['tables']
    left_cols, right_cols = grounding['columns']
    num_left, num_right = len(left_cols), len(right_cols)
    convo_history = context.compile_history()

    left_df = self.database.db.tables[left_tab_name]
    right_df = self.database.db.tables[right_tab_name]
    left_preview = PromptEngineer.display_preview(left_df, left_cols, max_rows=8)
    right_preview = PromptEngineer.display_preview(right_df, right_cols, max_rows=8)

    prompt = align_columns_prompt.format(history=convo_history, left_table=left_tab_name, right_table=right_tab_name,
                                         num_left=num_left, num_right=num_right, left_cols=left_cols, right_cols=right_cols,
                                         left_preview=left_preview, right_preview=right_preview)
    prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

    for mapping_name, mapping_list in prediction['alignment'].items():
      for connection_entity in mapping_list:
        for source_entity in flow.slots['source'].values:
          if source_entity['tab'] == connection_entity['tab'] and source_entity['col'] == connection_entity['col']:
            source_entity['rel'] = mapping_name
    return flow

  def complete_table_join(self, context, flow, state, world):
    # Supports {05A} by deciding how to merge two tables together, then notifies the database accordingly
    convo_history = context.compile_history()
    task = flow.name(full=True)

    # pull out the table names, columns names, and the dataframe preview
    left_tab_name = flow.slots['source'].values[0]['tab']
    right_tab_name = [ent['tab'] for ent in flow.slots['source'].values if ent['tab'] != left_tab_name][0]
    left_df = self.database.db.tables[left_tab_name]
    right_df = self.database.db.tables[right_tab_name]
    merged_tab_name = flow.slots['target'].values[0]['tab']
    all_tab_names = [left_tab_name, right_tab_name, merged_tab_name]

    left_keep_cols, right_keep_cols = [], []
    for entity in flow.slots['target'].values:
      if entity['col'] in left_df.columns:
        left_keep_cols.append(entity['col'])
      elif entity['col'] in right_df.columns:
        right_keep_cols.append(entity['col'])
    target_columns = [entity['col'] for entity in flow.slots['target'].values]

    left_preview = PromptEngineer.display_preview(left_df, left_keep_cols, max_rows=8)
    right_preview = PromptEngineer.display_preview(right_df, right_keep_cols, max_rows=8)

    if flow.slots['prepare'].fuzzy:
      groups = flow.detector.group_rows(left_df, right_df, left_keep_cols, right_keep_cols)
      flow.group_sizes['overlap'] = len(groups['matches'])
      # Manually construct the dataframe one column at a time, then clean up programatically
      keep_columns = {'left': left_keep_cols, 'right': right_keep_cols, 'both': target_columns}
      self.database.db.tables[merged_tab_name] = merged_df[target_columns]
      merged = self.make_join_list(groups['matches'], left_df, right_df, keep_columns)
      # Add solo rows from left or right tables
      merged += self.make_join_list(groups['left_solo'], left_df, None, keep_columns, 'left')
      merged += self.make_join_list(groups['right_solo'], None, right_df, keep_columns, 'right')
      merged_df = pd.DataFrame(merged)      # sort the columns in the order of the target columns

    else: # Directly generate code with the model to merge the tables together
      flow = self.count_overlapping_rows(flow, left_df, right_df, left_tab_name, right_tab_name)
      crew_desc, snippet = self.prepare_crew_members(flow, state, left_tab_name, right_tab_name)
      function_str = flow.slots['prepare'].str_rep
      target_col_str = "\n".join([f"  * {col}" for col in target_columns])
      prompt = join_tables_prompt.format(left_tab=left_tab_name, right_tab=right_tab_name, target_tab=merged_tab_name,
                                    left_cols=left_df.columns, right_cols=right_df.columns, target_cols=target_col_str,
                                    crew_description=crew_desc, alignment_snippet=snippet, history=convo_history, 
                                    left_preview=left_preview, right_preview=right_preview, alignment_func=function_str)
      _, python_code = self.database.execute_with_retries(self.api, state, task, prompt, convo_history, '', all_tab_names)
      merged_df = self.database.db.tables[merged_tab_name]

    flow.group_sizes['total'] = len(merged_df)
    frame = Frame(merged_tab_name, source='pandas')
    frame.set_data(merged_df, query=python_code)
    previous_tab_names = [left_tab_name, right_tab_name]
    self.set_new_table_metadata(merged_tab_name, world, context, merged_df, previous_tab_names)
    frame, state = self.frame_state_control(flow, state, merged_tab_name, frame)
    return frame, state

  def count_overlapping_rows(self, flow, left_df, right_df, left_tab_name, right_tab_name):
    left_cols = [ent['col'] for ent in flow.slots['source'].values if ent['tab'] == left_tab_name]
    right_cols = [ent['col'] for ent in flow.slots['source'].values if ent['tab'] == right_tab_name]

    left_values = set(left_df[left_cols].apply(tuple, axis=1))
    right_values = set(right_df[right_cols].apply(tuple, axis=1))

    flow.group_sizes['overlap'] = len(left_values.intersection(right_values))
    return flow

  def prepare_crew_members(self, flow, state, left_tab_name, right_tab_name):
    # Build crew groupings from source slots
    crew = defaultdict(list)
    for entity in flow.slots['source'].values:
      crew[entity['rel']].append(entity)
    num_members = len(crew)

    # Generate crew description
    if num_members == 1:
      crew_description = "is one group"
    else:
      crew_description = f"are {count_to_nl(num_members)} groups"

    # Build alignment snippet
    snippet = ""
    for crew_name, crew_members in crew.items():
      snippet += f"  * Group {crew_name} - "
      crew_dict = state.entity_to_dict(crew_members)
      left_members = PromptEngineer.array_to_nl(crew_dict[left_tab_name], connector='and')
      right_members = PromptEngineer.array_to_nl(crew_dict[right_tab_name], connector='and')
      snippet += f"{left_members} in {left_tab_name} should be aligned with {right_members} in {right_tab_name}\n"
    return crew_description, snippet

  def make_join_list(self, indexes, left_df, right_df, keep_columns, side='both'):
    merged_list = []
    for index in indexes:
      merged_row = {}
      if side != 'right':  # Process left side or both
        left_row = left_df.loc[index['left'] if side == 'both' else index].to_dict()
        for col_name, val in left_row.items():
          if col_name in keep_columns[side]:
            merged_row[col_name] = val

      if side != 'left':  # Process right side or both
        right_row = right_df.loc[index['right'] if side == 'both' else index].to_dict()
        for col_name, val in right_row.items():
          if col_name in keep_columns[side]:
            merged_row[col_name] = val

      # Fill in missing target columns with None
      for col in keep_columns['both']:
        if col not in merged_row:
          merged_row[col] = None

      merged_list.append(merged_row)
    return merged_list

  def rank_merge_methods(self, context, flow, state, valid_col_list):
    # Determine the top merge styles based on context, and store results in 'delimiter' or 'ordering' slot
    selected_cols = [entity['col'] for entity in flow.entity_values()]
    col_info = make_column_list(valid_col_list, selected_cols)
    prompt = merge_methods_prompt.format(history=context.compile_history(), column_info=col_info)
    raw_output = self.api.execute(prompt)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

    if prediction['methods'][0] == 'formula':
      flow.fall_back = '005'    # re-route to InsertFlow
      flow.verify_to_transfer = True
      state.thought = prediction['rationale']
    elif len(prediction['methods']) == 1:
      flow.slots['method'].assign_one(prediction['methods'][0])
    else:
      for method in prediction['methods']:
        flow.slots['candidate'].add_one(method)
    return flow, state

  def fill_join_tab_source(self, context, flow, state, world):
    # If the agent has not filled the source slots during NLU, then do so now
    chosen_tabs = list(set([ent['tab'] for ent in state.entities]))   # first fill in the tables already chosen
    num_needed = max(0, 2 - len(chosen_tabs))
    if num_needed > 0:
      valid_tabs = [tab for tab in world.valid_tables if tab not in chosen_tabs]
      chosen_tabs += random.sample(valid_tabs, num_needed)  # then randomly select the remaining tables

    col_list_1 = world.valid_columns[chosen_tabs[0]]
    col_list_2 = world.valid_columns[chosen_tabs[1]]
    prompt = columns_for_joining_prompt.format(history=context.compile_history(),
                table1=chosen_tabs[0], table2=chosen_tabs[1], columns1=col_list_1, columns2=col_list_2)
    raw_output = self.api.execute(prompt)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

    for table_index in range(2):
      tab_name = chosen_tabs[table_index]
      for col_name in prediction[tab_name]:
        flow.slots['source'].add_one(tab_name, col_name)
    return flow, state

  def fill_coverage_ratio(self, flow, world):
    # Fill the preparation function if it is empty, then use it to calculate the coverage rate
    if not flow.slots['prepare'].check_if_filled():
      flow = self.set_default_preparation(flow, world)

    crew_members = defaultdict(list)
    for entity in flow.slots['source'].values:
      crew_members[entity['rel']].append(entity)

    crew_coverage = []
    for crew_name, members in crew_members.items():
      left_tab = members[0]['tab']
      left_col_names = [member['col'] for member in members if member['tab'] == left_tab]
      right_members = [member for member in members if member['tab'] != left_tab]
      right_tab = right_members[0]['tab']
      right_col_names = [member['col'] for member in right_members]

      left_df = self.database.db.tables[left_tab]
      right_df = self.database.db.tables[right_tab]

      match_rate = self.calculate_coverage(flow, left_df, right_df, left_col_names, right_col_names)
      crew_coverage.append(match_rate)

    # assign the average coverage rate to the coverage slot
    flow.slots['coverage'].assign_one(sum(crew_coverage) / len(crew_coverage))
    return flow

  def set_default_preparation(self, flow, world):
    source_entity = flow.slots['source'].values[0]
    source_tab_schema = world.metadata['schema'][source_entity['tab']]
    source_col_props = source_tab_schema.get_type_info(source_entity['col'])

    match source_col_props['type']:
      case 'text': func = lambda x: x.astype(str).str.lower().strip()
      case 'number': func = lambda x: x.round(2).fillna(0)
      case 'datetime': func = lambda x: x.dt.strftime('%Y-%m-%d')
      case 'location': func = lambda x: x.astype(str).str.strip()    # Preserve capitalization
      case _: func = lambda x: x.fillna('').astype(str)

    def preparation_wrapper(left_series, right_series):
      left_prepared = func(left_series)
      right_prepared = func(right_series)
      return left_prepared, right_prepared
    flow.slots['prepare'].assign_one(preparation_wrapper)

    match source_col_props['type']:
      case 'text': flow.slots['prepare'].str_rep = "lambda x: x.astype(str).str.lower().strip()"
      case 'number': flow.slots['prepare'].str_rep = "lambda x: x.round(2).fillna(0)"
      case 'datetime': flow.slots['prepare'].str_rep = "lambda x: x.dt.strftime('%Y-%m-%d')"
      case 'location': flow.slots['prepare'].str_rep = "lambda x: x.astype(str).str.strip()"
      case _: flow.slots['prepare'].str_rep = "lambda x: x.fillna('').astype(str)"
    return flow

  def calculate_coverage(self, flow, left_df, right_df, left_columns, right_columns):
    # Select the columns to be compared and remove null values
    left_series = left_df[left_columns].squeeze()
    right_series = right_df[right_columns].squeeze()
    left_samples = left_series.dropna().sample(n=min(len(left_series), 4096))
    right_samples = right_series.dropna().sample(n=min(len(right_series), 4096))

    # Apply the preparation function to clean and prepare the data
    prepare_function = flow.slots['prepare'].value
    left_prepared, right_prepared = prepare_function(left_samples, right_samples)

    # Get unique values and find matches
    left_unique = set(left_prepared)
    right_unique = set(right_prepared)
    matches = left_unique.intersection(right_unique)
    num_matches = len(matches)

    # Calculate coverage based on the smaller set
    denominator = max(min(len(left_series), len(right_series)), 1)
    coverage = num_matches / denominator
    return coverage

  def rank_checkbox_opt(self, context, flow, frame, valid_col_dict):
    # Model ranks the most likely columns to keep after merging two tables, and store them in the Target slot
    selected_tabs = list(set( [entity['tab'] for entity in flow.entity_values()] ))
    tab1, tab2 = selected_tabs[:2]
    convo_history = context.compile_history()

    full_description = []
    for tab_name in selected_tabs:
      selected_cols = [ent['col'] for ent in flow.slots['source'].values if ent['tab'] == tab_name]
      col_desc = make_column_list(valid_col_dict[tab_name], selected_cols)
      full_description.append(f"{tab_name} - {col_desc}")
    final_desc = '\n'.join(full_description)

    prompt = checkbox_opt_prompt.format(history=convo_history, tab_one=tab1, tab_two=tab2, tab_col_info=final_desc)
    prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

    if 'error' in prediction:
      frame.signal_failure('code_generation')
    else:
      proposed_tab = prediction['new_table']
      for tab_name in selected_tabs:
        flow.options.extend(valid_col_dict[tab_name])
        table_key = tab_name if tab_name in prediction else tab_name.title()  # hack since LLMs are weak

        for col_name in prediction[table_key]:
          flow.slots['target'].add_one(proposed_tab, col_name)

    return flow, frame

  def generate_target_name(self, context, flow):
    col_str = PromptEngineer.array_to_nl([ent['col'] for ent in flow.slots['source'].values], connector='and')
    tab_str, style_str = flow.slots['source'].values[0]['tab'], flow.slots['method'].value
    convo_history = context.compile_history()
    prompt = merge_target_prompt.format(history=convo_history, columns=col_str, table=tab_str, merge_style=style_str)
    target_col_name = self.api.execute(prompt)
    flow.slots['target'].add_one(tab_str, target_col_name)
    return flow

  def join_tables(self, context, state, world):
    """Supports {05A} by deciding which columns within two separate tables should be merged.
    Upon success, we also need to update across the entire world, including prompts and metadata"""
    flow = state.get_flow(flow_type='join')
    frame, state = self.initialize_merge_frame(flow, state, world)

    if flow.slots['source'].is_verified() and not flow.is_uncertain:
      flow, grounding, reroute = self.ground_to_reality(flow, state, world)
      if reroute: return frame, state

      if any([entity['rel'] == '' for entity in flow.slots['source'].values]):
        flow = self.align_connection_columns(context, flow, grounding)

      if flow.slots['target'].check_if_filled():
        if not flow.slots['coverage'].filled:
          flow = self.fill_coverage_ratio(flow, world)

        if flow.slots['coverage'].check_if_filled():
          frame, state = self.complete_table_join(context, flow, state, world)
        elif flow.stage == 'proactive-cleaning':
          flow, frame = self.clear_interjected_action(context, flow, state, world)
        elif flow.slots['tag'].check_if_filled():
          flow, state = self.determine_join_preparation(context, flow, grounding, state)
        else:
          flow, state = self.predict_tag_type(context, flow, grounding, state, world)
      else:
        self.actions.add('INTERACT')
        flow.stage = 'checkbox-opt'  # have the user select or deselect checkboxes on the columns they want to keep
        flow, frame = self.rank_checkbox_opt(context, flow, frame, world.valid_columns)

    elif flow.slots['source'].filled:
      flow, frame, state = self.verify_table_join(context, flow, state, world)
    else:
      flow, state = self.fill_join_tab_source(context, flow, state, world)
      if flow.slots['source'].check_if_filled():
        frame, state = self.join_tables(context, state, world)

    # we are dealing with two tables rather than just one, so the alignment check won't set the raw table
    frame.raw_table = state.current_tab  # therefore we set it manually to ensure the data can be fetched
    print('Stage:', flow.stage, '// Source:', frame.source, '//', frame.raw_table)
    return frame, state

  def verify_table_join(self, context, flow, state, world):
    if can_join_by_id(flow, self.database.db, world):
      self.actions.add('CLARIFY')
      flow, state = foreign_key_column_found(flow, state)
    elif can_join_directly(flow, world):
      for entity in flow.slots['source'].values:
        entity['ver'] = True
      frame, state = self.join_tables(context, state, world)
    else:
      self.actions.add('INTERACT')
      flow.stage = 'pick-tab-col'   # kick off merge process by allowing user to pick tables and columns
      frame.tab_type = 'decision'   # Convert the type into a 'decision' table
    return flow, frame, state

  def clear_interjected_action(self, context, flow, state, world):
    dax = state.get_dialog_act(form='hex')  
    match dax:
      case '5AE': frame, state = self.complete_table_join(context, flow, state, world)
      case '5AF': flow.completed = True
      case _: flow.fall_back = dax
    return flow, frame

  def determine_join_preparation(self, context, flow, grounding, state):
    tag_type = flow.slots['tag'].value
    preparation_prompts = {
      'date': prepare_join_by_date_prompt,
      'location': prepare_join_by_loc_prompt,
      'organization': prepare_join_by_org_prompt,
      'person': prepare_join_by_per_prompt,
    }

    if tag_type == 'id':
      flow.stage = 'proactive-cleaning'
      flow.suggest_replies()
    else:
      convo_history = context.compile_history()
      tab_name1, tab_name2 = grounding['tables']
      col_names1, col_names2 = grounding['columns']
      valid_col_dict = grounding['valid']

      col_string1 = PromptEngineer.array_to_nl(valid_col_dict[tab_name1], connector='and')
      col_string2 = PromptEngineer.array_to_nl(valid_col_dict[tab_name2], connector='and')
      samples1 = PromptEngineer.display_samples(self.database.db.tables[tab_name1], columns=col_names1)
      samples2 = PromptEngineer.display_samples(self.database.db.tables[tab_name2], columns=col_names2)
      grounding['samples'] = [samples1, samples2]

      prepare_join_template = preparation_prompts[tag_type]
      prompt = prepare_join_template.format(history=convo_history, table1=tab_name1, table2=tab_name2,
                            columns1=col_string1, columns2=col_string2, samples1=samples1, samples2=samples2)
      raw_output = self.api.execute(prompt, version='claude-sonnet')
      prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

      level = determine_level(prediction['methods'])
      match level:
        case 'basic':        flow, state = self._basic_preparation(context, flow, grounding, state, prediction)
        case 'intermediate': flow, state = self._intermediate_preparation(flow, state, prediction, valid_col_dict)
        case 'advanced':     flow, state = self._advanced_preparation(flow, state, prediction)
        case _:              self.actions.add('CLARIFY'); state.ambiguity.declare('specific')
    return flow, state

  def _basic_preparation(self, context, flow, grounding, state, prediction):
    flow.slots['coverage'].reset()
    flow.stage = 'proactive-cleaning'
    flow.suggest_replies()
    state.slices['methods'] = prediction['methods'].copy()

    tab_name1, tab_name2 = grounding['tables']
    samples1, samples2 = grounding['samples']

    step_strings = []
    for index, method in enumerate(prediction['methods']):
      step_str = f"{index + 1}. "
      step_str += f"{method['name'].title()} ({method['table']}) - {method['description']}"
      step_strings.append(step_str)
    steps = '\n'.join(step_strings)

    prompt = basic_preparation_prompt.format(history=context.compile_history(), table1=tab_name1, table2=tab_name2,
                                             samples1=samples1, samples2=samples2, steps=steps)
    raw_output = self.api.execute(prompt, version='claude-sonnet', prefix='def prepare_func(left_df, right_df):')
    predicted_code = PromptEngineer.apply_guardrails(raw_output, 'python')

    namespace = {}
    exec(predicted_code, namespace)
    flow.slots['prepare'].assign_one(namespace['prepare_func'])
    return flow, state

  def _intermediate_preparation(self, flow, state, prediction, valid_col_dict):
    # stack-on the new flow rather than fallback
    method_tab, method_name = '', ''
    for method_info in prediction['methods']:
      for tab_name, method in method_info.items():
        method_tab = tab_name
        method_name = method

    match method_name:
      case 'stage': new_dax = '01A'
      case 'validate': new_dax = '36D'
      case 'merge': new_dax = '05C'
      case 'extract': new_dax = '005'
      case 'prune': new_dax = '007'
      case 'correct': new_dax = '46E'

    relevant_entities = []
    for entity in flow.slots['source'].values:
      if entity['tab'] == method_tab:
        relevant_entities.append(copy.deepcopy(entity))

    new_flow = flow_selection[new_dax](valid_col_dict)
    new_flow.slots[new_flow.entity_slot].values = relevant_entities
    new_flow.interjected = True

    self.actions.add('SHARE_MSG')
    flow.slots['coverage'].reset()
    state.thought = prediction['thought']

    state.natural_birth = False
    state.flow_stack.append(new_flow)
    state.store_dacts('', new_dax)
    state.keep_going = True
    return flow, state
  
  def _advanced_preparation(self, flow, state, prediction):
    flow.fall_back = '46D'    # re-route to ConnectionFlow
    flow.completed = True
    state.keep_going = True
    return flow, state

  def frame_state_control(self, flow, state, tab_name, frame):
    self.actions.add('CREATION')      # Signal to the front-end that a new table has been created
    flow.detector.reset()
    flow.completed = True  # mark as completed, rather than removing. Leave that to RES

    new_df = frame.get_data()
    frame.control['create'] = tab_name
    frame.has_changes = True          # set to True to force front-end to create a new tab

    for col_name in new_df.columns:
      state.entities.append({'tab': tab_name, 'col': col_name, 'ver': True})
    state.current_tab = tab_name      # change the current_tab, so the raw_table is also set correctly
    return frame, state

  def ground_to_reality(self, flow, state, world):
    """ Make sure we have the correct number of source tables and columns (left and right),
    If we do, then extract them into a grounding dict, otherwise re-route to different flow """
    grounding = {'valid': world.valid_columns}
    if flow.clarify_attempts == 0:
      return flow, grounding, False

    flow.clarify_attempts -= 1
    connection_cols = state.entity_to_dict(flow.slots['source'].values)
    reroute = True

    if len(connection_cols) == 2:
      left_tab_name, right_tab_name = list(connection_cols.keys())
      left_df, right_df = self.database.db.tables[left_tab_name], self.database.db.tables[right_tab_name]
      if len(left_df) < 3 or len(right_df) < 3:
        flow.fall_back = '005'    # re-route to InsertFlow since this is actually a lookup
      elif left_df.columns.equals(right_df.columns):
        flow.fall_back = '05B'    # re-route to AppendFlow
      else:
        grounding['tables'] = [left_tab_name, right_tab_name]
        grounding['columns'] = [connection_cols[left_tab_name], connection_cols[right_tab_name]]
        reroute = False
    else:  # there are more than two tables to join, which requires a different approach
      flow.fall_back = '46D'

    return flow, grounding, reroute

  def predict_tag_type(self, context, flow, grounding, state, world):
    tab_name1, tab_name2 = grounding['tables']
    col_names1, col_names2 = grounding['columns']
    df_1, df_2 = self.database.db.tables[tab_name1], self.database.db.tables[tab_name2]

    convo_history = context.compile_history()
    samples1 = PromptEngineer.display_samples(df_1, columns=col_names1)
    samples2 = PromptEngineer.display_samples(df_2, columns=col_names2)
    tab1_content, tab2_content = f"{tab_name1}\n{samples1}", f"{tab_name2}\n{samples2}"

    prompt = ner_tag_prompt.format(history=convo_history, table1=tab1_content, table2=tab2_content)
    tag_pred = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

    if flow.fill_slot_values(state, tag_pred):
      flow, state = self.join_tables(context, state, world)
    else:
      self.actions.add('CLARIFY')
      flow.completed = True
      state.ambiguity.declare('generic', flow='join')
      state.ambiguity.observation = "I'm not sure we can handle joining by these types of columns. Please try again."
    return flow, state

  def contains_duplicates(self, source_dict, tab_name1, tab_name2):
    # Returns True if the subset of columns in each table contains duplicates, and False otherwise
    cols1, cols2 = source_dict[tab_name1], source_dict[tab_name2]
    subset_df1 = self.database.db.tables[tab_name1][cols1]
    subset_df2 = self.database.db.tables[tab_name2][cols2]
    has_duplicates1 = len(subset_df1) != len(subset_df1.drop_duplicates())
    has_duplicates2 = len(subset_df2) != len(subset_df2.drop_duplicates())
    return has_duplicates1 or has_duplicates2

  def review_merge_conflicts(self, frame, flow, data_schema):
    if flow.tracker.batch_number == 0:
      # kick off the first batch of conflict cards, which is treated slightly differently
      flow.tracker.batch_number += 1
      # Ticket [1003]: auto-align selected columns to ensure source entities are in the right order
      flow.detector.set_tab_col(flow.slots['source'].values, self.database.db.tables)

      tables = {}
      for side in ['left', 'right']:
        tab_name = flow.detector.side_to_tab[side]
        table_df = self.database.db.tables[tab_name]
        tab_schema = {col: data_schema[tab_name].get_type_info(col) for col in table_df.columns}
        flow.detector.add_display_columns(table_df.columns, tab_name, tab_schema, side)
        # After all table names have been set, encode each table just once and find duplicates
        flow.detector.encode(table_df, tab_name, tab_schema)
        tables[tab_name] = table_df

      autofixes, conflicts = flow.detector.cross_tab_duplicates(tables, flow.slots['tag'].value)
      flow.tracker.store_cardsets(autofixes, conflicts)
      frame, flow = self.prepare_conflict_cards(frame, flow)

      if flow.slots['confidence'].level > 0.5:
        # progress through Combine Cards steps, by either starting or continuing a batch of cards.
        # Since these are interim steps, we simply update the batch_number and set the flow stage.
        flow.stage = 'combine-progress'
      else:
        flow.stage = 'combine-cards'
        frame = flow.tracker.combine_cards_action(frame)

    else:  # batch_number > 0
      if flow.tracker.still_resolving():
        flow.stage = 'combine-cards'
        frame = flow.tracker.combine_cards_action(frame)
      else:  # cardset_index == 10
        frame, flow = self.combine_progress_action(frame, flow, cross=True)

    # mark as incomplete even though all slots are filled to prevent it from being popped off the flow stack
    flow.completed = False
    return frame, flow

  def append_rows(self, context, state, world):
    """ Supports {05B} where it is possible to concatenate two tables vertically since the column names are the same """
    flow = state.get_flow(flow_type='append')
    frame = Frame(state.current_tab)

    if flow.slots['source'].filled:
      convo_history = context.compile_history()
      flow, state, source_table_names = self.verify_source_alignment(convo_history, flow, state)

      # source verification encompasses column alignment and order of the source tables
      if flow.slots['source'].is_verified() and not flow.is_uncertain:
        source_tables = PromptEngineer.array_to_nl(source_table_names, connector='and')
        data_preview = preview_tables(source_table_names, self.database.db.tables, num_rows=16)

        if flow.slots['target'].filled:
          target_entity = flow.slots['target'].values[0]
          joint_tab_name = target_entity['tab']
          state.current_tab = target_entity['tab']  # must be set before manipulate_data to access the new table

          if target_entity['rel'] == 'new':
            target_col = 'N/A'
            segment_desc = ". In our real case, we do *not* need to insert a column for segmentation because one already exists."
          else:
            target_col = target_entity['col']
            segment_desc = f", followed by the name of the column to insert, which in our case is called '{target_col}'."

          prompt = complete_append_prompt.format(source_tabs=source_tables, segment_desc=segment_desc,
                                                 history=convo_history, table_details=data_preview,
                                                 joint_tab=joint_tab_name, segment_col=target_col)
          all_table_names = source_table_names + [joint_tab_name]
          db_output, code = self.database.manipulate_data(self.api, context, state, prompt, all_table_names)

          if code == 'error':
            frame.signal_failure('code_generation', db_output.strip())
            self.actions.add("SHARE_MSG")
          else:
            frame = self.validate_dataframe(db_output, code, 'pandas', state, tab_type='direct')
            joint_table = self.database.db.tables[joint_tab_name]
            self.set_new_table_metadata(joint_tab_name, world, context, joint_table, source_table_names)

            if frame.is_successful():
              self.actions.add('CREATION')
              frame.control['create'] = joint_tab_name
              for removal_entity in flow.slots['removal'].values:
                removal_tab = removal_entity['tab']
                del self.database.db.tables[removal_tab]
                # TODO: properly remove from world, metadata, and update control to hold a list
                frame.control['drop'] = removal_tab

              self.update_system_prompt([state.current_tab], world, context, flow)
              frame.raw_table = state.current_tab
              flow.completed = True
            else:
              state.current_tab = source_table_names[0]
        else:
          prompt = append_target_prompt.format(source_tabs=source_tables, count=len(source_table_names),
                                                    history=convo_history, table_details=data_preview)
          raw_output = self.api.execute(prompt)
          prediction = PromptEngineer.apply_guardrails(raw_output, 'json')
          target_col, joint_tab_name = prediction['column'], prediction['table']

          relation = 'existing' if (target_col == 'unsure' or target_col in world.col_to_tab) else 'new'
          flow.slots['target'].add_one(joint_tab_name, target_col, rel=relation)
          if flow.slots['target'].check_if_filled():
            frame, state = self.append_rows(context, state, world)
          else:
            self.actions.add('CLARIFY')
            state.ambiguity.declare('specific', flow='append', slot='target')
      else:
        flow.is_uncertain = True
    else:
      flow.is_uncertain = True

    if flow.is_uncertain:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('partial', flow='append', generate=True)
    return frame, state

  def verify_source_alignment(self, convo_history, flow, state):
    source_table_names = list(set([entity['tab'] for entity in flow.slots['source'].values]))
    if flow.slots['source'].is_verified():
      return flow, state, source_table_names

    dataframes = [self.database.db.tables[tab_name] for tab_name in source_table_names]
    num_cols = [len(df.columns) for df in dataframes]
    # first sanity check that the number of columns are the roughly the same
    if max(num_cols) - min(num_cols) <= 1:

      # if exactly the same columns, then directly verify
      if all(df.columns.equals(dataframes[0].columns) for df in dataframes):
        for entity in flow.slots['source'].values:
          entity['ver'] = True

      else:  # use a prompt to verify alignment
        source_tabs = PromptEngineer.array_to_nl(source_table_names, connector='and')
        data_preview = preview_tables(source_table_names, self.database.db.tables, num_rows=16)
        prompt = append_alignment_prompt.format(source_tabs=source_tabs, history=convo_history, table_details=data_preview)
        prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
        flow.fill_slot_values(state.current_tab, prediction)

    return flow, state, source_table_names

  def merge_columns(self, context, state, world):
    """Supports {05C} by deciding how to create a new column based on existing ones, then notifies
    the database accordingly. Afterwards, we also update the world, including prompts and metadata"""
    flow = state.get_flow(flow_type='merge')
    frame = Frame(state.current_tab, tab_type='direct', source='interaction')

    if flow.is_uncertain:
      self.actions.add('CLARIFY')
      targets = flow.slots['target'].values
      if len(targets) > 0 and not targets[0]['ver']:  # there is an low confidence target entity
        state.ambiguity.declare('confirmation', slot='target', values=[target['col'] for target in targets])
        state.ambiguity.observation = f"What if we called the new column: {targets[0]['col']}?"
      else:
        state.ambiguity.declare('partial', flow='merge', slot='target')

    elif flow.slots['source'].is_verified():
      # filling an elective merge slots is equivalent to choosing a merge style
      if flow.slots['method'].filled:

        if not flow.slots['target'].filled:
          flow = self.generate_target_name(context, flow)
        if flow.slots['method'].value.startswith('custom'):
          frame, state, tab_name = self.custom_column_merge(flow, state)
        else:
          frame, state, tab_name = self.complete_merge_cols(context, flow, state)

        if frame.is_successful():
          self.update_system_prompt([tab_name], world, context, flow)
          frame.set_data([])  # clear out the stored data to force a reload
        flow.completed = True  # mark as completed, rather than removing. Leave that to RES
      else:
        self.actions.add('INTERACT')
        flow.stage = 'merge-style'  # continue process by having the user choose a merge style
        flow.is_uncertain = False
        valid_col_list = world.valid_columns[state.current_tab]
        flow, state = self.rank_merge_methods(context, flow, state, valid_col_list)
        if flow.slots['method'].filled:
          return self.merge_columns(context, flow, state, world)  # recurse
    else:
      self.actions.add('INTERACT')
      flow.stage = 'pick-tab-col'    # kick off merge process by allowing user to pick source columns
      frame.tab_type = 'decision'    # Convert the type into a 'decision' table

    return frame, state

  def complete_merge_cols(self, context, flow, state):
    # start by unpacking all the information from the flow slots
    method_name = flow.slots['method'].value
    method_detail = flow.slots['settings'].value['detail']
    source_columns = [ent['col'] for ent in flow.slots['source'].values]
    tab_name = flow.slots['source'].values[0]['tab']
    new_col_name = flow.slots['target'].values[0]['col']
    source_tables = list(set([ent['tab'] for ent in flow.slots['source'].values]))

    # Use engineer and context to convert lists and dataframes into natural language
    current_cols = PromptEngineer.array_to_nl(source_columns, connector='and')
    data_preview = PromptEngineer.display_preview(self.database.db.tables[tab_name], source_columns, max_rows=8)
    convo_history = context.compile_history(look_back=3)

    # construct the new thought describing the merge
    thought = f"merge the {current_cols} columns in the {tab_name} table "
    thought += f"using the {method_name} method to create a new '{new_col_name}' column"
    state.thought = f"I should {thought}"

    # finish compiling all the full description for the prompt
    if method_name in ['concat', 'space', 'underscore', 'period', 'comma']:
      definition = merge_col_thoughts[method_name]
    elif method_name in ['separator', 'contains']:
      template = merge_col_thoughts[method_name]
      definition = template.replace('<INPUT>', method_detail)
    elif method_name in ['order', 'size', 'length', 'alpha']:
      definition = merge_col_thoughts[method_name][method_detail]
    description = f"Concretely, you are trying to {thought}.\n"
    description += f"More specifically, the '{method_name}' method means to {definition}."

    prompt = merge_cols_prompt.format(thought_desc=description, source_tab=tab_name, source_cols=current_cols,
                                      target_col=new_col_name, history=convo_history, preview=data_preview)
    db_output, code = self.database.manipulate_data(self.api, context, state, prompt, source_tables)

    if code == 'error':
      frame = Frame(state.current_tab)
      frame.signal_failure('code_generation', db_output.strip())
      self.actions.add("SHARE_MSG")
    else:
      frame = self.validate_dataframe(db_output, code, 'pandas', state, tab_type='direct')

    frame.raw_table = tab_name
    return frame, state, tab_name

  def custom_column_merge(self, flow, state):
    # Handles custom merge actions by executing the user's custom code and updating the database
    style_name = flow.slots['method'].value[6:].lower()  # remove the 'custom' prefix
    raw_code = flow.slots['settings'].value['detail']
    for char in ['A', 'B', 'C', 'D']:
      raw_code = raw_code.replace(char, f"<{char}>")

    placeholders = ['<A>', '<B>', '<C>', '<D>']
    for ph, ent in zip(placeholders, flow.slots['source'].values):
      tab_name, col_name = ent['tab'], ent['col']
      code_snippet = f"df['{col_name}']"
      raw_code = raw_code.replace(ph, code_snippet)

    new_entity = flow.slots['target'].values[0]
    new_tab, new_col = new_entity['tab'], new_entity['col']
    full_code = f"df['{new_col}'] = {raw_code}"

    prompt = custom_code_prompt.format(code=full_code, merge_style=style_name)
    output = self.api.execute(prompt)
    state.current_tab = new_tab
    frame = Frame(new_tab, source='interaction')

    if output == 'error':
      frame.signal_failure('custom', "the code pattern was deemed to be potentially malicious so it could not be executed")
    else:
      final_code = full_code if output == 'good' else output
      df = self.database.db.tables[new_tab]

      try:
        exec(final_code)
      except:
        frame.signal_failure('custom', "there was an error with the custom code the user provided")

    return frame, state, new_tab

  def call_external_api(self, context, state, world):
    # Supports {456} by calling an external API to retrieve a new table and updating the database
    self.actions.add('INTERACT')
    flow = state.get_flow(flow_type='call')
    frame = Frame(state.current_tab, source='interaction')

    if flow.slots['source'].filled:
      if flow.slots['target'].filled:
        source_tab = flow.slots['source'].values[0]['tab']
        target_tab = flow.slots['target'].values[0]['tab']
        source_df = self.database.db.tables[source_tab]
        new_df = self.get_external_table(source_df)
        self.database.db.tables[target_tab] = new_df
        self.update_system_prompt([target_tab], world, context, flow)
        self.actions.remove('INTERACT')
        flow.completed = True

      else:
        flow.stage = 'pick-tab-col'
        frame.tab_type = 'decision'
        frame.raw_table = state.current_tab

    else:
      flow.stage = 'pick-tab-col'
      frame.tab_type = 'decision'
      frame.raw_table = state.current_tab

    return frame, state
