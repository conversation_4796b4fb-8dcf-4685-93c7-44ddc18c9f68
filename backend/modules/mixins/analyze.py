import json
import pandas as pd
from collections import Counter

from backend.prompts.mixins.for_analyze import *
from backend.prompts.for_executors import combine_cte_prompt
from backend.prompts.for_nlu import metric_name_prompt, segment_metric_prompt
from backend.utilities.pex_helpers import *
from backend.utilities.nlu_helpers import compile_operations_prompt
from backend.utilities.search import metric_name_finder

from utils.help import flow2dax
from backend.modules.flow.formulas import Formula, Expression, Clause
from backend.components.engineer import PromptEngineer
from backend.components.frame import Frame

class AnalyzeMixin:
  """ Methods that calculate metrics or KPI without changing the underlying data """

  def query_action(self, context, state, world):
    # generates a SQL query for the user's request based on Analyze(query) Flow
    flow = state.get_flow(flow_type='query')

    if flow.slots['source'].filled and not flow.is_uncertain:
      # passing state.entities (instead of setting error=True) preserves the state.thought
      frame = self.default_frame(state, state.entities)

      if flow.slots['operation'].filled:
        flow, reroute = query_rerouting(flow)
        if reroute: return frame, state
        flow = self.decide_time_range(context, flow, state, world, required=False)
        db_output, sql_query = self.database.query_data(context, flow, state, world)

        if sql_query == 'error':
          frame.signal_failure('code_generation', db_output.strip()) # db_output is the error message
        else:
          frame = self.validate_dataframe(db_output, sql_query, 'sql', state)
          state = self.backfill_underlying_flow(context, flow, frame, state)

      elif flow.is_uncertain:
        self.actions.add('CLARIFY')
        state.ambiguity.declare('specific', flow='query', slot='what exactly needs to be calculated')
      else:
        prompt = compile_operations_prompt(context.compile_history(), flow, state)
        raw_output = self.api.execute(prompt, version='claude-sonnet')
        prediction = PromptEngineer.apply_guardrails(raw_output, 'json')
        if flow.fill_slot_values(state.current_tab, prediction):
          frame, state = self.query_action(context, state, world)
        else:
          self.actions.add('CLARIFY')
          state.ambiguity.declare('specific', flow='query', slot='what aggregation operations are needed')

    else:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('partial')
      frame = self.default_frame(state, state.entities)

    if frame.is_successful() and not state.ambiguity.present():
      if flow.slots['operation'].filled:
        state.slices['operations'] = flow.slots['operation'].values
      if state.has_issues:
        frame = attach_issues_entity(flow, frame, state, world)
      flow, world = query_visualization(self.api, context, flow, frame, world)
      state, world = proactive_validation(self.api, context, flow, frame, state, world)
      flow.completed = True
    return frame, state

  def measure_action(self, context, state, world):
    """ generates a complex SQL query for the users's request based on Analyze(measure) Flow {002}
    When the metric is:
      * initialized - metric will have a name and Formula, but expression is empty and variables == None
      * filled - expression present, first level of variables are named, clauses are empty
      * populated - expression present, all variables are filled down to clauses, which have predicted values
      * verified - expression present, variables are verified, clauses have verified values
    It is the job of NLU or the referring flow to initialize and fill the metric.
      * `contemplate` or `measure_action` is reponsible for populating the metric variables with entities
      * Metric Builder, materialized views, or other user interaction is necessary to verify the metric
      * `complete_measurement` is responsible for querying the database and returning the final result
    """
    flow = state.get_flow(flow_type='measure')
    frame = self.default_frame(state, state.entities)

    if flow.slots['metric'].is_initialized():
      flow = self.full_formula_naming(flow)
      if flow.slots['source'].filled and not flow.is_uncertain:

        has_one_table, num_columns, first_tab_name = count_tab_cols(flow.slots['source'].values)
        num_expanded_cols = len(world.valid_columns[first_tab_name])
        if has_one_table and num_columns > 4 and num_columns == num_expanded_cols:
          flow, state = self.all_columns_ambiguity(flow, state)

        elif flow.slots['metric'].check_if_filled():
          if flow.slots['metric'].is_populated():
            # parse results to fill the time slot and verify the metric
            flow = self.decide_time_range(context, flow, state, world)
            tab_col_str = PromptEngineer.tab_col_rep(world)

            if flow.is_uncertain:   # due to time range uncertainty
              self.actions.add('CLARIFY')
              state.ambiguity.declare('specific', flow='measure', slot='what time range to consider')
            elif flow.slots['metric'].is_verified():
              state.thought = flow.slots['time'].get_description(state.thought)
              frame, state = self.complete_measurement(context, flow, state, tab_col_str)
              flow.completed = True
            else:
              if flow.slots['metric'].formula.open_for_revision:
                flow, state = self.revise_metric_expression(context, flow, state, world)
              flow, state = self.verify_formula_variables(context, flow, state, tab_col_str)
              if flow.slots['metric'].formula.verified:
                frame, state = self.measure_action(context, state, world)

          elif flow.clarify_attempts > 0:
            flow, state = self.fully_populate_variables(context, flow, state)
          else:
            flow, frame, state = self.build_variables_stage(flow, state)
        else:
          agreement, flow, state = self.establish_metric_expression(context, flow, state, world)
          if agreement == 'perfect':
            frame, state = self.measure_action(context, state, world)
      else:
        self.actions.add('CLARIFY')
        state.ambiguity.declare('partial', flow='measure')
    else:
      flow, state = self.measure_rerouting(context, flow, state, world)
    return frame, state

  def all_columns_ambiguity(self, flow, state):
    # we simply grabbed all columns from   the table, without actually checking which ones are relevant
    flow.slots['source'].drop_unverified()
    state.ambiguity.declare('partial', flow='measure', generate=True)
    state.ambiguity.observation = "Which tables or columns should we be analyzing?"
    return flow, state

  def full_formula_naming(self, flow):
    # make sure that the formula name includes a full name (expanded) and short name (acronym)
    metric = flow.slots['metric'].formula
    if metric.get_name('short') == 'N/A' or metric.get_name('long') == 'N/A':
      metric.acronym, metric.expanded = metric_name_finder(flow.slots['metric'].value)
    return flow

  def fully_populate_variables(self, context, flow, state):
    # populate the remaining variables in Expression all the way down to the Clauses
    metric = flow.slots['metric'].formula
    tab_col_dict = state.entity_to_dict(flow.slots['source'].values)
    data_preview = multi_tab_display(tab_col_dict, self.database.db.tables)
    prompt = variable_completion_prompt.format(metric=metric.get_name('long'), formula=metric.display(),
                                                preview=data_preview, history=context.compile_history())
    raw_output = self.api.execute(prompt, version='claude-sonnet')
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

    populated_formula_json = prediction['formula']
    metric_name = metric.get_name(size='short')

    if populated_formula_json['name'].lower() == 'n/a':
      flow.completed = True
      flow.stage = 'completed'
      del state.slices['metrics'][metric_name]
      return flow, state
    else:
      flow = self.refill_source_slot(flow, populated_formula_json)
      metric.expression = self.pred_to_expression(populated_formula_json)
      state.slices['metrics'][metric_name] = metric

    metric.open_for_revision = True
    # either case for confirmation on the populated metric, or ask for clarification on what's missing
    flow, state = self.clarify_latest_formula(context, flow, metric, state)
    return flow, state

  def refill_source_slot(self, flow, formula_json):
    # refill the source slot with the populated formula clauses
    num_source_cols = len(flow.slots['source'].values)
    unique_clauses = gather_unique_clauses(formula_json)
    num_unique_clauses = len(unique_clauses)
    total_clauses = num_source_cols + num_unique_clauses

    if num_source_cols >= 3 and num_unique_clauses >= 3 and total_clauses >= 8:
      flow.slots['source'].drop_unverified()

    for clause_tab, clause_col in unique_clauses:
      flow.slots['source'].add_one(clause_tab, clause_col)
    return flow

  def verify_formula_variables(self, context, flow, state, tab_col_str):
    # review the user's utterance to see if they have explicitly verified the predicted clauses
    formula = flow.slots['metric'].formula
    prompt = metric_verification_prompt.format(metric=formula.get_name(), valid_tab_col=tab_col_str,
                                               history=context.compile_history(), formula=formula.display())
    prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
    if len(state.thought) == 0:
      state.thought = prediction['thought']

    if formula.verify_variables(prediction['verified']):
      formula.verified = True
      formula.open_for_revision = False
    else:
      flow, state = self.clarify_latest_formula(context, flow, formula, state)
      formula.open_for_revision = True
    return flow, state

  def revise_metric_expression(self, context, flow, state, world):
    # revise the metric expression based on the user's utterance
    formula = flow.slots['metric'].formula
    tab_col_str = PromptEngineer.tab_col_rep(world)
    tab_col_dict = state.entity_to_dict(flow.slots['source'].values)
    data_preview = multi_tab_display(tab_col_dict, self.database.db.tables)
    metric_expr = formula.display(with_verify=True)

    if flow.name() == 'measure':
      prompt = metric_revision_prompt.format(metric=formula.get_name('full'), history=context.compile_history(),
                                            valid_tab_col=tab_col_str, preview=data_preview, expression=metric_expr)
    elif flow.name() == 'segment':
      seg_dimension, seg_type = flow.slots['segment'].value['dimension'], flow.slots['segment'].value['type']
      target_col = flow.slots['segment'].value.get('column', 'N/A')
      prompt = segment_revision_prompt.format(metric=formula.get_name('full'), dimension=seg_dimension,
                                                column=target_col, valid_tab_col=tab_col_str, expression=metric_expr,
                                                type=seg_type, preview=data_preview, history=context.compile_history())
    raw_output = self.api.execute(prompt, version='claude-sonnet', max_tok=2048)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

    if prediction['formula']['name'] == 'other':
      flow, state = self.measure_rerouting(context, flow, state, world)
    else:
      formula.expression = self.pred_to_expression(prediction['formula'])
      state.slices['metrics'][formula.get_name('short')] = formula
    return flow, state

  def predict_new_expression(self, context, flow, state, templates, world):
    # generate the variables within the metric expression, all the way down to the clause entities if possible
    metric = flow.slots['metric'].formula
    tab_str = '; '.join(world.valid_tables)
    likely_cols = ', '.join([entity['col'] for entity in flow.slots['source'].values])
    valid_col_str = PromptEngineer.column_rep(world.valid_columns, with_break=True)
    convo_history = context.compile_history()

    # predict the variables using a reasoning model
    prompt = templates['reasoning'].format(history=convo_history, metric=metric.get_name('long'),
                                              tables=tab_str, likely_cols=likely_cols, columns=valid_col_str)
    reasoning_output = self.api.execute(prompt, version='reasoning-model', max_tok=4096)
    reasoning_pred = PromptEngineer.apply_guardrails(reasoning_output, 'json')
    # predict the variables using intructions and exemplars
    prompt = templates['exemplar'].format(metric=metric.get_name('long'), thought=state.thought, tables=tab_str,
                                              likely_cols=likely_cols, columns=valid_col_str, history=convo_history)
    exemplar_output = self.api.execute(prompt, version='claude-sonnet', max_tok=2048)
    exemplar_pred = PromptEngineer.apply_guardrails(exemplar_output, 'json')

    reasoning_formula, exemplar_formula = reasoning_pred, exemplar_pred.get('formula', {})
    agreement = review_formula_agreement(reasoning_formula, exemplar_formula)

    self.actions.add('CLARIFY')
    if agreement == 'perfect':
      self.actions.remove('CLARIFY')
      formula_json = reasoning_formula  # pick either one, they are identical
    elif agreement == 'imperfect':
      formula_json = exemplar_formula   # pick the exemplar formula, and ask for confirmation
      state.ambiguity.declare('confirmation', flow=flow.name(), generate=True)
      state.thought = exemplar_pred['thought']
    else:
      relevant_entities = extract_relevant_entities(reasoning_formula, exemplar_formula, flow)
      entity_dict = state.entity_to_dict(sanitize_entities(relevant_entities, world.valid_columns))
      data_preview = multi_tab_display(entity_dict, self.database.db.tables)
      prompt = variable_disagreement_prompt.format(metric=metric.get_name('long'), history=convo_history,
                                            preview=data_preview, formula1=reasoning_formula, formula2=exemplar_formula)
      raw_output = self.api.execute(prompt, version='claude-sonnet', max_tok=2048)
      prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

      formula_json = prediction['formula']
      state.ambiguity.declare('confirmation', flow=flow.name(), generate=True)
      state.thought = prediction['thought']

    # if the formula contains a placeholder, then asking for confirmation is not enough
    if has_placeholder(formula_json):
      state.ambiguity.declare('specific', flow=flow.name(), values=[metric.get_name('long')], generate=True)
    return agreement, flow, state, formula_json

  def complete_segmentation(self, context, flow, frame, state, tab_col_str, world):
    # start by unpacking the slot information to build up the prompts
    metric_name = flow.slots['metric'].formula.get_name(size='long')
    convo_history = context.compile_history(look_back=9, keep_system=False)
    source_str = build_source_string(flow.slots['source'].values)
    source_cols = ', '.join([entity['col'] for entity in flow.slots['source'].values if entity['row'] == 'segment'])
    segmentation = flow.slots['segment'].value
    seg_dimension, target_col, segment_tab = segmentation['dimension'], segmentation['column'], segmentation['table']
    state.slices['max_tokens'] = 1024  # Double from 512 to avoid truncation errors
    source_tables = list(set([entity['tab'] for entity in flow.slots['source'].values]))

    if len(flow.slots['steps'].values) > 1:
      # TODO: This should actually be an {005} stack-on, not an extra action within the {02D} flow
      segment_df = self.database.db.tables[segment_tab]
      segment_cols = [entity['col'] for entity in flow.slots['source'].values if entity['tab'] == segment_tab]
      data_preview = PromptEngineer.display_samples(segment_df, segment_cols, num_samples=16, skip_nulls=True)
      steps_str = ''.join([f"  * {seg_step}\n" for seg_step in flow.slots['steps'].values])
      prompt = create_segmentation_prompt.format(metric=metric_name, dimension=seg_dimension, source_data=source_str,
                                                 target_col=target_col, segment_tab=segment_tab, history=convo_history,
                                                 source_cols=source_cols, segment_steps=steps_str, preview=data_preview)
      # create the staging column for the segmentation
      db_output, pandas_code = self.database.execute_with_retries(state, 'Transform(insert)',
                                                                 prompt, convo_history, '', source_tables)
      if pandas_code == 'error':
        frame.signal_failure('code_generation', db_output.strip())
        self.actions.add("SHARE_MSG")
        return frame, state
      else:
        self.update_system_prompt([segment_tab], world, context, flow)

    # finally, calculate the desired metric
    formula = flow.slots['metric'].formula
    prompt = segment_completion_prompt.format(metric=formula.get_name(), dimension=seg_dimension, target_col=target_col,
                                              valid_tab_col=tab_col_str, formula=formula.display(), history=convo_history,
                                              segment_tab=segment_tab, segment_type=segmentation['type'])

    db_output, sql_query = self.database.analyze_data(context, flow, prompt, state)

    if sql_query == 'error':
      self.actions.add('CLARIFY')
      frame = self.default_frame(state, state.entities)
      frame.signal_failure('code_generation', db_output.strip())
    else:
      new_tab_name = create_new_table_name(metric_name, seg_dimension, world.valid_tables)
      frame = Frame(new_tab_name, source='sql')  # create a direct table, rather than a derived table
      frame.control['create'] = new_tab_name
      frame.set_data(db_output, sql_query)
      frame.has_changes = True

      frame.properties['tabs'] = [new_tab_name] + source_tables  # new table must be the first one
      self.database.db.tables[new_tab_name] = db_output
      self.set_new_table_metadata(new_tab_name, world, context, db_output, world.valid_tables)
      state = self.backfill_underlying_flow(context, flow, frame, state)

      self.actions.add('CREATION')
      state.current_tab = new_tab_name
    return frame, state

  def complete_measurement(self, context, flow, state, tab_col_str):
    formula = flow.slots['metric'].formula
    thought = state.thought[0].lower() + state.thought[1:]
    metric_name = formula.get_name()

    aliases, code_snippets, previous_variables = [], [], []
    for variable in formula.expression.variables[::-1]:
      var_name = variable.name
      template = formula.build_template(code_snippets, previous_variables)
      prompt = metric_completion_prompt.format(metric=metric_name, prior_thought=thought, variable_name=var_name,
                variable_phrase=formula.describe_target(var_name), valid_tab_col=tab_col_str, template=template)
      
      raw_output = self.api.execute(prompt, version='claude-sonnet')
      alias, var_code = self.parse_generated_variable(raw_output)
      aliases.append(alias)
      code_snippets.append(var_code)
      previous_variables.append(var_name)

    if 'unsure' in aliases:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('partial', flow='measure', generate=True)
      state.ambiguity.observation = code_snippets[aliases.index('unsure')]
      return Frame(state.current_tab), state

    formula_template = formula.build_template(code_snippets, previous_variables)
    convo_history = context.compile_history(look_back=9, keep_system=False)
    prompt = combine_cte_prompt.format(metric=metric_name, history=convo_history, valid_tab_col=tab_col_str,
                                        thought=state.thought, template=formula_template)
    db_output, sql_query = self.database.analyze_data(context, flow, prompt, state)

    if sql_query == 'error':
      self.actions.add('CLARIFY')
      frame = self.default_frame(state, state.entities)
      frame.signal_failure('code_generation', db_output.strip())
    else:
      frame = self.validate_dataframe(db_output, sql_query, 'sql', state)
      state = self.backfill_underlying_flow(context, flow, frame, state)

    return frame, state

  def parse_generated_variable(self, raw_code):
    # parse the raw output from the variable generation prompt to extract the alias and SQL code
    alias, question = 'unsure', 'Can you clarify which columns to use?'
    for line in raw_code.split('\n'):
      if line.startswith('-- Alias: '):
        alias = line.split('-- Alias: ')[1].strip()
      elif line.startswith('-- Question: '):
        question = line.split('-- Question: ')[1].strip()

    if alias == 'unsure':
      var_code = question
    else:
      var_code = PromptEngineer.apply_guardrails(raw_code, 'sql')
    return alias, var_code

  def clarify_latest_formula(self, context, flow, formula, state):
    # ask the user for feedback about the latest formula expression and what to do next
    flow.clarify_attempts -= 1
    if flow.clarify_attempts < 0:
      self.actions.add('SHARE_MSG')
      state.thought = "This is getting too complex for me to handle. Can you please clean up your data and try again?"
      flow.completed = True
      flow.stage = 'completed'
    else:
      full_name, expanded = formula.get_name(size='full'), formula.get_name(size='long')
      prompt = clarify_metric_prompt.format(expanded=expanded, metric=full_name, history=context.compile_history(),
                                            thought=state.thought, formula=formula.display(with_verify=True))
      raw_answer = self.api.execute(prompt)
      prediction = PromptEngineer.apply_guardrails(raw_answer, 'json')

      self.actions.add('CLARIFY')
      state.ambiguity.declare(prediction['level'], flow=flow.name(), slot='what to do next')
      state.ambiguity.observation = prediction['question']   # clarification question
    return flow, state

  def measure_rerouting(self, context, flow, state, world):
    """ Determines whether the metric is a simple query, a basic metric, a segmentation process, or insight detection
    based on the number of predicted metrics to calculate. When the request:
      * has no intermediate variables (only clauses), then it is a simple query {001}
      * has one pre-defined metric, then it is a known analysis {002}
      * requires drilling down or splitting the metric, then it is a segmented analysis {02D}
      * has two or more metrics, then it requires some insight detection {146} """
    if flow.is_uncertain:  return flow, state
    convo_history = context.compile_history()
    if flow.slots['metric'].formula is None:
      metric_name = 'To Be Determined (N/A)'
    else:
      metric_name = flow.slots['metric'].formula.get_name()
    prompt = metric_routing_prompt.format(history=convo_history, thought=state.thought, metric=metric_name)
    results = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

    if results['scope'] == 'query':
      flow.fall_back = '001'
    elif results['scope'] == 'measure':
      valid_col_str = PromptEngineer.column_rep(world.valid_columns, with_break=True)
      prompt = metric_name_prompt.format(history=convo_history, columns=valid_col_str)
      prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
      if flow.fill_slot_values(state.current_tab, prediction):
        # if we can successfully extract a metric, then we should proceed with analysis
        _, flow, state = self.establish_metric_expression(context, flow, state, world)
    elif results['scope'] == 'segment':
      flow.fall_back = '02D'
    elif results['scope'] == 'insight':
      flow.fall_back = '146'
    else:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('partial', flow='measure', generate=True)
      flow.completed = True

    return flow, state

  def segment_rerouting(self, context, flow, state, world):
    # See measure_rerouting method for more details
    # if flow.is_uncertain:  return flow, state     # commented out as part of [1595]
    convo_history = context.compile_history()
    if flow.slots['metric'].formula is None:
      metric_name = 'To Be Determined (N/A)'
    else:
      metric_name = flow.slots['metric'].formula.get_name()
    prompt = metric_routing_prompt.format(history=convo_history, thought=state.thought, metric=metric_name)
    results = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
    clarification_needed = False

    if results['scope'] == 'query':
      flow.fall_back = '001'
    elif results['scope'] == 'measure':
      flow.fall_back = '002'
    elif results['scope'] == 'segment':
      prompt = segment_metric_prompt.format(history=convo_history)
      prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
      if flow.fill_slot_values(state.current_tab, prediction):
        # if we can successfully name a metric, then we should proceed with segmentation
        _, state = self.identify_segmentation(context, flow, state, world)
        if any([entity['ver'] for entity in flow.slots['source'].values]):
          state.ambiguity.declare('confirmation', flow='segment', generate=True)
        else:
          clarification_needed = True
      else:
        clarification_needed = True
    elif results['scope'] == 'insight':
      flow.fall_back = '146'
    else:
      clarification_needed = True

    if clarification_needed:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('partial', flow='segment', generate=True)
      flow.completed = True

    return flow, state

  def build_variables_stage(self, flow, state):
    self.actions.add('INTERACT')
    self.actions.add('CLARIFY')
    flow.stage = 'build-variables'

    state.ambiguity.declare('partial', flow=flow.name())
    message = "Sorry, I'm still having trouble understanding your request -- let's try a different approach.\n"
    message += "Can you please select the relevant columns needed to calculate this metric?"
    state.ambiguity.observation = message

    frame = Frame(state.current_tab, tab_type='decision', source='interaction')
    return flow, frame, state

  def pivot_table(self, context, state, world):
    """ Supports {01A} by creating a direct table (not a derived table like the Query flow) composed of
    at least one grouping and at least two columns involving aggregations, filters, or additional grouping """
    flow = state.get_flow(flow_type='pivot')

    if flow.slots['source'].filled:
      frame = self.default_frame(state, state.entities)

      if flow.slots['operation'].check_if_filled():
        flow = self.decide_time_range(context, flow, state, world, required=False)

        if flow.slots['target'].check_if_filled():
          db_output, sql_query = self.database.query_data(context, flow, state, world)
          if sql_query == 'error':
            frame.signal_failure('code_generation', db_output.strip())  # db_output is the error message
          elif len(db_output) == 0:
            frame.set_data(db_output, query=sql_query, source='sql')
            frame.tab_type = 'derived'
            frame.signal_failure('empty_results')
          else:
            tab_name = flow.slots['target'].table_name()
            pivot_table = db_output
            for col_name in pivot_table.columns:
              flow.slots['target'].add_one(tab_name, col_name)

            # Build the frame directly rather than relying on validate dataframe, which is not appropriate here
            frame = Frame(tab_name, source='sql')
            frame.control['create'] = tab_name
            frame.set_data(pivot_table, sql_query)
            frame.has_changes = True

            context.add_actions(['PIVOT'], 'Agent')
            self.database.db.tables[tab_name] = pivot_table
            self.set_new_table_metadata(tab_name, world, context, pivot_table, world.valid_tables)
            state = self.backfill_underlying_flow(context, flow, frame, state)

            self.actions.add('CREATION')    # must occur later, since back-filling will clear the actions
            state.current_tab = tab_name
          flow.completed = True
        else:
          flow = self.fill_pivot_target(context, flow, world)
          if flow.is_filled():
            frame, state = self.pivot_table(context, state, world)
      elif flow.is_uncertain:
        self.actions.add('CLARIFY')
        state.ambiguity.declare('partial', flow='pivot')
      else:
        prompt = compile_operations_prompt(context.compile_history(), flow, state)
        raw_output = self.api.execute(prompt, version='claude-sonnet')
        prediction = PromptEngineer.apply_guardrails(raw_output, 'json')
        if flow.fill_slot_values(state.current_tab, prediction):
          frame, state = self.pivot_table(context, state, world)
        else:
          self.actions.add('CLARIFY')
          state.ambiguity.declare('specific', flow='pivot', slot='what aggregation operations are needed')

    else:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('partial', flow='pivot', slot='source table or column')
      frame = self.default_frame(state, state.entities)

    if flow.slots['operation'].filled:
      state.slices['operations'] = flow.slots['operation'].values
    return frame, state

  def fill_pivot_target(self, context, flow, world):
    # decide whether a new table is needed, and if so, what an appropriate name should be
    tab_col_str = PromptEngineer.tab_col_rep(world)
    ops_string = PromptEngineer.array_to_nl(flow.slots['operation'].values, connector='and')
    convo_history = context.compile_history()
    prompt = pivot_table_prompt.format(valid_tab_col=tab_col_str, operations=ops_string, history=convo_history)
    prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

    tab_name = prediction['table_name']
    if len(tab_name) == 0:
      flow.fall_back = '001'
    else:
      primary_key = self.database.db.make_primary_key(tab_name)
      flow.slots['target'].add_one(tab_name, primary_key)
    return flow

  def backfill_underlying_flow(self, context, flow, frame, state):
    """ When we create a pivot table, we also need to backfill the underlying flow that created it """
    if flow.interjected:
      prev_flow = state.get_flow(allow_interject=False)

      match flow.name():
        case 'plot': state = self.backfill_from_plot(context, flow, prev_flow, state)
        case 'segment': state = self.backfill_from_segment(context, flow, prev_flow, state)
        case 'pivot': state = self.backfill_from_pivot(context, flow, prev_flow, frame, state)
        case _: self.write_to_scratchpad(flow, prev_flow, frame.get_data())

      self.actions.clear()
      underlying_dax = flow2dax(prev_flow.name())

      state.flow_stack.pop()  # remove the stack_on flow
      state.store_dacts(dax=underlying_dax)  # point back to the underlying flow
      state.keep_going = True
    return state

  def backfill_from_pivot(self, context, curr_flow, prev_flow, frame, state):
    pivot_tab = frame.raw_table
    source_tabs = [entity['tab'] for entity in curr_flow.slots['source'].values]
    target_cols = [entity['col'] for entity in curr_flow.slots['target'].values]
    tab_counts = Counter(source_tabs)
    original_tab = tab_counts.most_common(1)[0][0]

    prev_entities = state.entity_to_dict(prev_flow.slots[prev_flow.entity_slot].values)
    prev_cols = prev_entities[original_tab]
    prev_ent_slot = prev_flow.entity_slot

    if all([col in target_cols for col in prev_cols]):
      for old_column in prev_cols:
        prev_flow.slots[prev_ent_slot].replace_entity(original_tab, old_column, pivot_tab)
    elif prev_flow.name() == 'join':
      prompt = pivot_backfill_prompt.format(history=context.compile_history(), target=target_cols, previous=prev_cols)
      raw_output = self.api.execute(prompt)
      prediction = PromptEngineer.apply_guardrails(raw_output, 'json')
      for pair in prediction['mapping']:
        prev_flow.slots[prev_ent_slot].replace_entity(original_tab, pair['old'], pivot_tab, pair['new'])
    elif prev_flow.name() == 'insight':
      table_df = self.database.db.tables[pivot_tab]
      curr_flow, prev_flow = self.write_to_scratchpad(curr_flow, prev_flow, table_df)
      prev_flow = apply_new_pivot_table(prev_flow, pivot_tab, table_df)
    return state

  def segment_analysis(self, context, state, world):
    """ Supports {02D} by creating a custom metric that requires additional segmentation to calculate
    Just like MeasureFlow, SegmentFlow is responsible for creating the metric, but it also needs to handle:
      * segmentation - across time, channel, or some other dimension
      * time range - this is now required, rather than optional
      * routing - since this is so close to Insight, we need to make sure it's not too complex
      * creation - we insert a staging column to break down the metric into segments
    """
    flow = state.get_flow(flow_type='segment')
    frame = self.default_frame(state, state.entities)

    if flow.slots['metric'].is_initialized() and not flow.is_uncertain:
      flow, state = self.identify_segmentation(context, flow, state, world)
      if not flow.slots['source'].check_if_filled():
        return frame, state

      if flow.slots['metric'].check_if_filled():
        if flow.slots['metric'].is_populated():
          tab_col_str = PromptEngineer.tab_col_rep(world)

          if flow.slots['segment'].filled and flow.slots['steps'].filled:
            if flow.slots['metric'].is_verified():
              state.thought = flow.slots['time'].get_description(state.thought)
              frame, state = self.complete_segmentation(context, flow, frame, state, tab_col_str, world)
              flow.completed = True
              flow.stage = 'completed'  # reset the stage
            else:
              if flow.slots['metric'].formula.open_for_revision:
                flow, state = self.revise_metric_expression(context, flow, state, world)
              flow, state = self.verify_formula_variables(context, flow, state, tab_col_str)
              if flow.slots['metric'].formula.verified:
                frame, state = self.segment_analysis(context, state, world)
          else:
            flow, state = self.breakdown_into_buckets(context, flow, state, tab_col_str)
            if not state.ambiguity.present():
              frame, state = self.segment_analysis(context, state, world)

        elif flow.clarify_attempts > 0:
          flow, state = self.fully_populate_variables(context, flow, state)
        else:
          flow, frame, state = self.build_variables_stage(flow, state)
      else:
        agreement, flow, state = self.establish_metric_expression(context, flow, state, world)
        if agreement == 'perfect':
          frame, state = self.segment_analysis(context, state, world)

    else:
      flow, state = self.segment_rerouting(context, flow, state, world)
    return frame, state

  def establish_metric_expression(self, context, flow, state, world):
    if flow.name() == 'measure':
      templates = {'reasoning': reasoning_variables_prompt, 'exemplar': exemplar_variables_prompt}
    else:
      templates = {'reasoning': segment_reasoning_prompt, 'exemplar': segment_exemplar_prompt}
    agreement, flow, state, formula_json = self.predict_new_expression(context, flow, state, templates, world)

    try:
      metric = flow.slots['metric'].formula
      # need to first test that the formula json is valid; it should contain name and relation keys
      formula_name, formula_relation = formula_json['name'], formula_json['relation']
      metric.expression = self.pred_to_expression(formula_json)
      state.slices['metrics'][metric.get_name(size='short')] = metric
      flow.is_uncertain = False
    except Exception as ecp:
      print(f"Encountered an error while parsing the formula: {ecp}")
      self.actions.add('INTERACT')
      state.ambiguity.declare('specific', flow=flow.name(), slot='how I should calculate the metric')
      flow.stage = 'build-variables'
    return agreement, flow, state

  def pred_to_expression(self, formula_json):
    # Convert a JSON formula into an Expression object with nested Expressions and Clauses.
    def convert_variable(var_json):
      # Helper function to convert a single JSON variable into an appropriate object
      match get_variable_type(var_json):
        case 'clause': return Clause.from_entity(var_json)
        case 'expression': return self.pred_to_expression(var_json)

    expr_name, expr_relation = formula_json['name'], formula_json['relation']
    expr_relation = 'add' if expr_relation == 'sum' else expr_relation
    variables = [convert_variable(var) for var in formula_json['variables']]
    new_expr = Expression(name=expr_name, relation=expr_relation, variables=variables)
    return new_expr

  def breakdown_into_buckets(self, context, flow, state, tab_col_str):
    # figure out the target columns stored in segmentation slot, and also the steps to bucket or categorize the segments
    metric_name = flow.slots['metric'].formula.get_name()
    seg_dimension = flow.slots['segment'].value['dimension']
    source_entities = flow.slots['source'].values
    source_tab = '; '.join([entity['tab'] for entity in source_entities])
    source_cols = ', '.join([entity['col'] for entity in source_entities])

    prompt = determine_buckets_prompt.format(metric=metric_name, dimension=seg_dimension, history=context.compile_history(),
                                             source_tab=source_tab, source_cols=source_cols, valid_tab_col=tab_col_str)
    prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
    is_segmented = flow.fill_segmentation_steps(prediction)

    self.actions.add('CLARIFY')
    if is_segmented:
      dim = flow.slots['segment'].value['dimension']
      if context.contains_keyword(dim, look_back=3) or flow.slots['steps'].values[0] == 'none':
        # ie. there is an exact string match, so the dimension is obvious and we can skip clarification
        self.actions.remove('CLARIFY')
      else:
        flow.clarify_attempts -= 1
        state.ambiguity.declare('confirmation', flow='segment', slot='which dimension to segment by')
        state.ambiguity.observation = f"We have segmented the data by {dim}, does that sound right?"
    else:
      flow.clarify_attempts -= 1
      state.ambiguity.declare('specific', flow='segment', generate=True)
      state.ambiguity.observation = "What are the buckets or categories to use for segmentation?"
    return flow, state

  def identify_segmentation(self, context, flow, state, world):
    # figure out the source column(s) for segmentation, as well as the type of segmentation
    if any([entity['row'] == 'segment' for entity in flow.slots['source'].values]):
      return flow, state

    metric_name = flow.slots['metric'].formula.get_name()
    convo_history = context.compile_history()
    tab_col_str = PromptEngineer.tab_col_rep(world)
    prompt = identify_segmentation_prompt.format(metric=metric_name, valid_tab_col=tab_col_str, history=convo_history)
    raw_output = self.api.execute(prompt)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

    dimension = prediction.get('dimension', 'unsure')
    seg_type = prediction.get('type', 'unsure')

    if dimension != 'unsure' and seg_type in flow.segment_types:
      flow.slots['segment'].add_one('type', seg_type)
      flow.slots['segment'].add_one('dimension', dimension)
      for entity in prediction['source']:
        flow.slots['source'].add_one(entity['tab'], entity['col'], row='segment')

      if seg_type != 'temporal':
        flow = self.decide_time_range(context, flow, state, world)
    else:
      self.actions.add('CLARIFY')
      flow.is_uncertain = True
      state.ambiguity.declare('specific', flow='segment', slot='how to break down the analysis')
    return flow, state

  def handle_segment_clarify(self, context, flow, state, world):
    self.actions.add('CLARIFY')
    clarify_slot = flow.slots['clarify']
    tab_col_str = PromptEngineer.tab_col_rep(world)

    if flow.clarify_attempts % 2 == 0:  # every other time, we generate new questions
      prompt = clarifying_thought_prompt.format(valid_tab_col=tab_col_str, questions='\n '.join(clarify_slot.options), 
                                                history=context.compile_history(), thought=state.thought)
      results = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
      for question in results['questions']:
        clarify_slot.options.append(question)
  
    flow.clarify_attempts -= 1
    if len(clarify_slot.options) == 0:   # if we couldn't generate any questions, then we manually make one up
      segment_question = "I'm a bit lost. Please be more specific about what exact metric you want to calculate."
      clarify_slot.options.append(segment_question)
    if clarify_slot.filled:    # if we have already asked all our previous questions, then we create a new one
      follow_up_question = "I'm still confused. Can you please explain what you want to analyze in a different way?"
      clarify_slot.options.append(follow_up_question)

    top_questions = []
    for question in clarify_slot.options:
      if question not in clarify_slot.values:
        top_questions.append(question)
        clarify_slot.add_one(question)  # mark the question as asked
      if len(top_questions) >= 2: break

    state.ambiguity.declare('specific', flow='segment', values=top_questions, generate=True)
    return flow, state

  def contains_time_triggers(self, context, flow):
    # Determines if time range analysis is needed based on conversation context.
    time_triggers = ['weekly', 'monthly', 'quarterly', 'historical', 'timeline', 'yearly', 'seasonal', 'datetime', 'timestamp']
    time_triggers += flow.slots['time'].keywords
    joint_context = context.compile_history(look_back=3)
    normalized = joint_context.replace('\n', ' ').replace('user:', ' ').replace('agent:', ' ').strip()
    context_tokens = normalized.replace(',', ' ').replace('.', ' ').lower().split()
    has_triggers = any([trigger in context_tokens for trigger in time_triggers])
    return has_triggers

  def exist_time_columns(self, world):
    time_related_columns = []
    max_row_count = 0

    for tab_name, columns in world.valid_columns.items():
      for col_name in columns:
        try:
          col_type_info = world.metadata['schema'][tab_name].get_type_info(col_name)
        except KeyError:
          continue

        if col_type_info['type'] == 'datetime':
          time_related_columns.append(col_name)
          row_statistics = world.metadata['schema'][tab_name].general_stats['num_rows']
          max_row_count = max(max_row_count, row_statistics)

    return time_related_columns, max_row_count

  def decide_time_range(self, context, flow, state, world, required=True):
    if flow.slots['time'].check_if_filled():
      return flow

    # First check if there are datetime type columns
    time_columns, max_row_count = self.exist_time_columns(world)
    if len(time_columns) == 0 or max_row_count == 0:
      flow.slots['time'].unit = 'all'
      flow.slots['time'].time_len = -1

    # Then check if we need time analysis based on conversation context
    elif required or self.contains_time_triggers(context, flow):

      # Prepare prompt and determine time parameters
      prompt = time_period_prompt.format(time_columns=time_columns, history=context.compile_history())
      prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

      # Set time parameters based on type
      length, unit = prediction['length'], prediction['unit']
      uncertain_type = False
      match prediction['type']:
        case 'range': flow.slots['time'].add_one(prediction['start'], prediction['stop'], length, unit)
        case 'lookback': flow.slots['time'].add_one(prediction['start'], 'present', time_len=0.5, unit=unit)
        case 'duration': flow.slots['time'].add_one(time_len=length, unit=unit)
        case 'complex': flow.slots['time'].add_one(prediction['start'], prediction['stop'], length, unit)
        case _: uncertain_type = True

      if uncertain_type:
        # There are many rows related to time, so filtering may be helpful
        if max_row_count > 16384 and required:
          if flow.clarify_attempts > 0:
            self.actions.add('CLARIFY')
            state.ambiguity.declare('specific', flow='segment', generate=True)
            state.ambiguity.observation = "Is there a specific time range you want to focus on?"
            flow.clarify_attempts -= 1
          else:
            self.actions.add('INTERACT')
            flow.stage = 'time-range'
        else:
          flow.slots['time'].unit = 'all'
          flow.slots['time'].time_len = -1
      else:
        flow.slots['time'].value = prediction['type']
        if prediction['type'] == 'complex':
          self.actions.add('CLARIFY')
          state.ambiguity.declare('confirmation', flow='segment')
          state.ambiguity.observation = "I might struggle to handle such a complex time range. Could you simplify it?"

    return flow

  def describe_action(self, context, state, world):
    # Supports {014} by preparing a preview of the data
    flow = state.get_flow(flow_type='describe')

    if flow.slots['source'].filled:
      if flow.slots['facts'].filled:
        main_table = flow.slots['source'].values[0]['tab']
        state.current_tab = main_table

        if world.states[-2].current_tab == main_table:
          frame = Frame(state.current_tab)
        else:
          frame = Frame(state.current_tab, source='change')  # changes the selectedTab

        state, world = proactive_validation(self.api, context, flow, frame, state, world)
        flow.completed = True

        if 'existence' in flow.slots['facts'].values:
          flow.fall_back = '14C'   # fallback to the Analyze(exist) flow
        elif 'preview' in flow.slots['facts'].values:
          if any([entity['col'] == '*' for entity in flow.slots['source'].values]):
            sample_df = self.database.db.tables[state.current_tab].dropna().sample(64)
          else:
            target_tab = flow.slots['source'].values[0]['tab']
            table_df = self.database.db.tables[target_tab]
            columns = [entity['col'] for entity in flow.slots['source'].values if entity['tab'] == target_tab]
            sample_df = PromptEngineer.display_samples(table_df, columns, num_samples=64, method='dataframe')
          frame.set_data(sample_df, source='preview')

      else:
        flow.fall_back = '001'      # fallback to a Analyze(query) flow
        frame = Frame(state.current_tab)
    else:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('partial', flow='describe')
      frame = self.default_frame(state, state.entities)

    return frame, state

  def check_existence(self, context, state, world):
    # Supports {14C} by helping user answer the question, "Is there a column for X?"
    flow = state.get_flow(flow_type='exist')

    if flow.slots['source'].filled:
      main_table = flow.slots['source'].values[0]['tab']
      state.current_tab = main_table

      if world.states[-2].current_tab == main_table:
        frame = Frame(state.current_tab)   # just create a default frame
      else:
        frame = Frame(state.current_tab, source='change')

      if len(flow.slots['source'].values) == 1:
        entity = flow.slots['source'].values[0]
        found_str = f" the {entity['col']} column in the {entity['tab']} table."
      else:
        found_str = ""
        for entity in flow.slots['source'].values:
          found_str += f":\n  - {entity['col']} column in {entity['tab']}"

      convo_history = context.compile_history()
      valid_col_str = PromptEngineer.tab_col_rep(world, include_tab=False)
      prompt = check_existence_prompt.format(found=found_str, history=convo_history, columns=valid_col_str)
      raw_output = self.api.execute(prompt)
      prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

      if len(prediction['preview']) > 0:
        for entity in prediction['preview']:
          flow.slots['preview'].add_one(**entity)
      else:
        for entity in flow.slots['source'].values:
          flow.slots['preview'].add_one(**entity)

      table_df = self.database.db.tables[main_table]
      if any([entity['col'] == '*' for entity in flow.slots['preview'].values]):
        sample_df = table_df.dropna().sample(64)
      else:
        columns = [entity['col'] for entity in flow.slots['preview'].values if entity['tab'] == main_table]
        sample_df = PromptEngineer.display_samples(table_df, columns, num_samples=64, method='dataframe')
      frame.set_data(sample_df, source='preview')

    else:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('partial', flow='exist')
      frame = self.default_frame(state, state.entities)

    if frame.is_successful():
      flow.completed = True
    return frame, state

  def recommend_action(self, context, state, world):
    # Generates a recommendation for the user about what to do next
    flow = state.get_flow()
    frame = self.default_frame(state, state.entities)
    return frame, state

  def inform_metric(self, context, state, world):
    # Provides information about how a particular metric is calculated
    frame = Frame(state.current_tab)
    return frame, state

  def define_metric(self, context, state, world):
    # Defines a metric based on a formula and saves it as a user preference
    frame = Frame(state.current_tab)
    return frame, state