import json
import numpy as np

from utils.help import flow2dax
from backend.prompts.mixins.for_detect import *
from backend.utilities.search import add_issues_entity
from backend.utilities.pex_helpers import count_tab_cols
from backend.components.engineer import PromptEngineer
from backend.components.frame import Frame
from backend.modules.flow import flow_selection

class DetectMixin:
  """ Methods to fix concerns such as outliers, anomalies, problems, and other issues """

  def identify_blanks(self, context, state, world):
    # Supports {46B} by resolving missing, default, or null values within the table
    flow = state.get_flow(flow_type='blank')
    previous_frame = world.frames[-1] if world.has_data() else None

    if flow.slots['source'].filled:

      if previous_frame and len(previous_frame.issues_entity) > 0:
        tab_name, col_name = previous_frame.issues_entity['tab'], previous_frame.issues_entity['col']
      else:
        tab_name, col_name = self.decide_main_entity(world, flow, 'blank')
      tab_blanks = world.metadata['blank'][tab_name]
      num_issues = tab_blanks.num_issue_rows(col_name)

      if num_issues == 0:
        flow.clear_all_issues()
      else:
        state.current_tab = tab_name
        state.has_issues = True
      frame = Frame(state.current_tab)

      # this is the first time issues are activated, so sanity check if they truly exist
      if any([flow.slots[itype].level < 0 for itype in flow.issue_types]):
        prediction = self.sanity_check_blanks(tab_name, col_name, context, tab_blanks)
        prediction['counts'] = {itype: tab_blanks.num_issue_rows(col_name, itype) for itype in flow.issue_types}
        flow.fill_slot_values(tab_name, prediction)
        if len(previous_frame.issues_entity.get('row', {})) == 0:
          flow.is_newborn = True  # to allow issues to be activated

      if flow.is_filled():   # either because we resolve all issues, or because no legitimate issues exist
        frame, flow, state = self.wrap_up_issues(flow, previous_frame, state)
        if flow.is_newborn and flow.interjected:
          state.keep_going = True
          state.flow_stack.pop()  # drop the interjected flow

      elif flow.is_newborn:
        frame.activate_issues(tab_blanks, tab_name, col_name, flow.name())
        flow = self.attach_follow_up(context, flow, frame, state)
        state = add_issues_entity(state, frame)

        display_df = self.display_issues(frame, state)
        issue_query = flow.describe_issues(frame)
        frame.set_data(display_df, issue_query, source=flow.name())
        frame.tab_type = 'dynamic'

      else:      # issues are already activated, but NLU didn't predict a resolution
        resolution = self.decide_issue_redirection(context, tab_blanks, col_name)
        match resolution:
          case 'ignore': frame, flow, state = self.wrap_up_issues(flow, frame, state)
          case 'update': flow.fall_back = '006'
          case 'remove': flow.fall_back = '007'
          case 'recommend': flow.fall_back = '049'
          case _: self.actions.add('CLARIFY'); state.ambiguity.declare('specific')

    else:
      self.actions.add('CLARIFY')
      slot_desc = 'source table or column'
      state.ambiguity.declare('confirmation', flow='blank', slot=slot_desc, values=[state.current_tab], generate=True)
      frame = world.frames[-1] if world.has_data() else self.default_frame(state, state.entities)

    return frame, state

  def sanity_check_blanks(self, tab_name, col_name, context, blanks):
    convo_history = context.compile_history(look_back=3)
    blank_df = self.database.db.tables[tab_name]
    blanks_column = blank_df[col_name].astype('string').fillna('<N/A>')
    occurrence_counts = blanks_column.value_counts()

    unique_blanks = set()
    for blank_type, issue_data in blanks.issues.items():
      unique_blanks.update([blanks_column[row_id] for row_id in issue_data[col_name]])

    displayed = False
    lines = []
    for value, count in occurrence_counts.items():
      if len(value) > 128:
        value = value[:128] + " ..."
      sample_line = f"{value} - {count} instance"
      if count > 1:
        sample_line += "s"
      if value in unique_blanks:
        displayed = True
        sample_line += " <--"

      lines.append(sample_line)
      if len(lines) >= 8:
        break

    if not displayed:
      for blank in unique_blanks:
        blank_str = '<N/A>' if blank == np.nan else str(blank)
        if len(blank_str) > 128:
          blank_str = blank_str[:128] + " ..."
        sample_line = f"{blank_str} - {occurrence_counts[blank_str]} instance <--"
        lines.append(sample_line)

    if len(occurrence_counts) > 8:
      num_remaining = len(occurrence_counts) - 8
      lines.append(f"({num_remaining} other unique values ...)")

    prompt = blank_type_prompt.format(history=convo_history, column=col_name, samples='\n'.join(lines))
    raw_output = self.api.execute(prompt)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')
    return prediction

  def identify_concerns(self, context, state, world):
    # Supports {46C} by resolving concerns such as outliers, anomalies, and other issues
    flow = state.get_flow(flow_type='concern')
    previous_frame = world.frames[-1] if world.has_data() else None

    if flow.slots['source'].filled:

      if previous_frame and len(previous_frame.issues_entity) > 0:
        tab_name, col_name = previous_frame.issues_entity['tab'], previous_frame.issues_entity['col']
      else:
        tab_name, col_name = self.decide_main_entity(world, flow, 'concern')
      tab_concerns = world.metadata['concern'][tab_name]
      num_issues = tab_concerns.num_issue_rows(col_name)

      if num_issues == 0:
        flow.clear_all_issues()
      else:
        state.current_tab = tab_name
        state.has_issues = True
      frame = Frame(state.current_tab)

      # this is the first time issues are activated, so sanity check if they truly exist
      if any([flow.slots[itype].level < 0 for itype in flow.issue_types]):
        prediction = self.sanity_check_concerns(tab_name, col_name, context, tab_concerns, world)
        prediction['counts'] = {itype: tab_concerns.num_issue_rows(col_name, itype) for itype in flow.issue_types}
        flow.fill_slot_values(tab_name, prediction)
        if len(previous_frame.issues_entity.get('row', {})) == 0:
          flow.is_newborn = True  # to allow issues to be activated

      if flow.is_filled():   # either because we resolve all issues, or because no legitimate issues exist
        frame, flow, state = self.wrap_up_issues(flow, previous_frame, state)
        if flow.is_newborn and flow.interjected:
          state.keep_going = True
          state.flow_stack.pop()  # drop the interjected flow

      elif flow.is_newborn:
        frame.activate_issues(tab_concerns, tab_name, col_name, flow.name())
        flow = self.attach_follow_up(context, flow, frame, state)
        state = add_issues_entity(state, frame)

        display_df = self.display_issues(frame, state)
        issue_query = flow.describe_issues(frame)
        frame.set_data(display_df, issue_query, source=flow.name())
        frame.tab_type = 'dynamic'

      else:      # issues are already activated, but NLU didn't predict a resolution
        resolution = self.decide_issue_redirection(context, tab_concerns, col_name)
        match resolution:
          case 'ignore': frame, flow, state = self.wrap_up_issues(flow, frame, state)
          case 'update': flow.fall_back = '006'
          case 'remove': flow.fall_back = '007'
          case 'recommend': flow.fall_back = '049'
          case _:        self.actions.add('CLARIFY'); state.ambiguity.declare('specific')

    else:
      self.actions.add('CLARIFY')
      slot_desc = 'source table or column'
      state.ambiguity.declare('confirmation', flow='concern', slot=slot_desc, values=[state.current_tab], generate=True)
      frame = world.frames[-1] if world.has_data() else self.default_frame(state, state.entities)

    return frame, state

  def sanity_check_concerns(self, tab_name, col_name, context, concerns, world):
    issue_types = {'number': 'outlier', 'text': 'anomaly', 'datetime': 'date_issue', 'location': 'loc_issue'}
    tab_schema = world.metadata['schema'][tab_name]
    col_info = tab_schema.get_type_info(col_name)
    main_type = issue_types.get(col_info['type'], 'none')

    convo_history = context.compile_history(look_back=3)
    concern_df = self.database.db.tables[tab_name]
    concern_column = concern_df[col_name].astype('string').fillna('<N/A>')
    occurrence_counts = concern_column.value_counts()
    unique_concerns = set([concern_column[row_id] for row_id in concerns.issues[main_type][col_name]])

    displayed = False
    lines = []
    for value, count in occurrence_counts.items():
      if len(value) > 128:
        value = value[:128] + " ..."
      sample_line = f"{value} - {count} instance"
      if count > 1:
        sample_line += "s"
      if value in unique_concerns:
        displayed = True
        sample_line += " <--"

      lines.append(sample_line)
      if len(lines) >= 8:
        break

    if not displayed:
      for concern in unique_concerns:
        concern_str = str(concern)
        if len(concern_str) > 128:
          concern_str = concern_str[:128] + " ..."
        sample_line = f"{concern_str} - {occurrence_counts[concern_str]} instance <--"
        lines.append(sample_line)

    if len(occurrence_counts) > 8:
      num_remaining = len(occurrence_counts) - 8
      lines.append(f"({num_remaining} other unique values ...)")

    compiled = '\n'.join(lines)
    prompt = concern_type_prompt.format(main_type=main_type, history=convo_history, column=col_name, samples=compiled)
    raw_output = self.api.execute(prompt)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')
    return prediction

  def identify_typos(self, context, state, world):
    # Supports {46E} by resolving similar terms or typos within the table
    flow = state.get_flow(flow_type='typo')
    previous_frame = world.frames[-1] if world.has_data() else None
    print("aaa")

    if flow.slots['source'].filled:
      print("bbb")

      unique_tabs = {ent['tab'] for ent in flow.slots['source'].values}
      if len(unique_tabs) > 1:
        self.actions.add('CLARIFY')
        state.ambiguity.declare('confirmation', flow='typo', values=list(unique_tabs))
        return previous_frame, state

      if previous_frame and len(previous_frame.issues_entity) > 0:
        print("ccc")
        tab_name, col_name = previous_frame.issues_entity['tab'], previous_frame.issues_entity['col']
      else:
        print("ddd")
        tab_name, col_name = self.decide_main_entity(world, flow, 'typo')
      tab_typos = world.metadata['typo'][tab_name]
      print('tab_name', tab_name, 'col_name', col_name)
      issue_df = self.database.db.shadow.issues[tab_name]
      num_issues = tab_typos.num_issue_rows(issue_df, col_name)
      print('num_issues', num_issues)

      if num_issues == 0:
        print("eee")
        flow.clear_all_issues()
      else:
        print("fff")
        state.current_tab = tab_name
        state.has_issues = True
      frame = Frame(state.current_tab)

      if flow.is_filled():   # either because we resolve all issues, or because no legitimate issues exist
        print("ggg")
        frame, flow, state = self.wrap_up_issues(flow, previous_frame, state)
        if flow.is_newborn and flow.interjected:
          print("hhh")
          state.keep_going = True
          state.flow_stack.pop()  # drop the interjected flow

      # Activate all typo issues without model review step, since typos are reviewed already in interactive panel
      elif any([not flow.slots[itype].approved for itype in flow.issue_types]) or flow.is_newborn:
        print("iii")
        frame.activate_issues(tab_typos, tab_name, col_name, flow.name())
        flow = self.attach_follow_up(context, flow, frame, state)
        state = add_issues_entity(state, frame)

        for issue_type in flow.issue_types:
          unique_terms = set(tab_typos.chosen_terms[col_name][issue_type])
          flow.slots[issue_type].steps = list(unique_terms)
          flow.slots[issue_type].approved = True

        self.actions.add('INTERACT')
        display_df = self.display_issues(frame, state)
        issue_query = flow.describe_issues(frame)
        frame.set_data(display_df, issue_query, source=flow.name())
        frame.tab_type = 'dynamic'

        column = self.database.db.tables[tab_name][col_name]
        flow.valid_terms = column.fillna('<N/A>').unique().tolist()

      else:      # issues are already activated, but NLU didn't predict a resolution
        print("jjj")
        resolution = self.decide_issue_redirection(context, tab_typos, col_name)
        match resolution:
          case 'ignore': frame, flow, state = self.wrap_up_issues(flow, frame, state)
          case 'update': flow.fall_back = '006'
          case 'remove': flow.fall_back = '007'
          case 'recommend': flow.fall_back = '049'
          case _:        self.actions.add('CLARIFY'); state.ambiguity.declare('specific')

    else:
      print("kkk")
      self.actions.add('CLARIFY')
      slot_desc = 'source table or column'
      state.ambiguity.declare('confirmation', flow='typo', slot=slot_desc, values=[state.current_tab], generate=True)
      frame = world.frames[-1] if world.has_data() else self.default_frame(state, state.entities)

    print("lll")
    return frame, state

  def identify_problems(self, context, state, world):
    # Supports {46F} by resolving problems such as mixed datatypes and unsupported data structures
    flow = state.get_flow(flow_type='problem')
    previous_frame = world.frames[-1] if world.has_data() else None

    if flow.slots['source'].filled:

      if previous_frame and len(previous_frame.issues_entity) > 0:
        tab_name, col_name = previous_frame.issues_entity['tab'], previous_frame.issues_entity['col']
      else:
        tab_name, col_name = self.decide_main_entity(world, flow, 'problem')
      tab_problems = world.metadata['problem'][tab_name]
      num_issues = tab_problems.num_issue_rows(col_name)

      if num_issues == 0:
        flow.clear_all_issues()
      else:
        state.current_tab = tab_name
        state.has_issues = True
      frame = Frame(state.current_tab)

      # this is the first time problems are activated, so sanity check if they truly exist
      if any([flow.slots[itype].level < 0 for itype in flow.issue_types]):
        prediction = self.sanity_check_problems(tab_name, col_name, context, tab_problems, world)
        prediction['counts'] = {itype: tab_problems.num_issue_rows(col_name, itype) for itype in flow.issue_types}
        flow.fill_slot_values(tab_name, prediction)
        if len(previous_frame.issues_entity.get('row', {})) == 0:
          flow.is_newborn = True  # to allow issues to be activated

      if flow.is_filled():   # either because we resolve all issues, or because no legitimate issues exist
        frame, flow, state = self.wrap_up_issues(flow, previous_frame, state)
        if flow.is_newborn and flow.interjected:
          state.keep_going = True
          state.flow_stack.pop()  # drop the interjected flow

      elif flow.is_newborn:
        frame.activate_issues(tab_problems, tab_name, col_name, flow.name())
        flow = self.attach_follow_up(context, flow, frame, state)
        state = add_issues_entity(state, frame)

        display_df = self.display_issues(frame, state)
        issue_query = flow.describe_issues(frame)
        frame.set_data(display_df, issue_query, source=flow.name())
        frame.tab_type = 'dynamic'

      else:      # issues are already activated, but NLU didn't predict a resolution
        resolution = self.decide_issue_redirection(context, tab_problems, col_name)
        match resolution:
          case 'ignore': frame, flow, state = self.wrap_up_issues(flow, frame, state)
          case 'update': flow.fall_back = '006'
          case 'remove': flow.fall_back = '007'
          case 'recommend': flow.fall_back = '049'
          case _:        self.actions.add('CLARIFY'); state.ambiguity.declare('specific')

    else:
      self.actions.add('CLARIFY')
      slot_desc = 'source table or column'
      state.ambiguity.declare('confirmation', flow='problem', slot=slot_desc, values=[state.current_tab], generate=True)
      frame = world.frames[-1] if world.has_data() else self.default_frame(state, state.entities)

    return frame, state

  def sanity_check_problems(self, tab_name, col_name, context, problems, world):
    convo_history = context.compile_history(look_back=3)

    col_props = world.metadata['schema'][tab_name].get_type_info(col_name)
    problem_df = self.database.db.tables[tab_name]
    problem_column = self.database.db.shadow.display_as_type(problem_df, **col_props)
    occurrence_counts = problem_column.value_counts()

    unique_problems = {}
    for problem_type, issue_data in problems.issues.items():
      for row_id, subtype in zip(issue_data[col_name], problems.child_types[col_name]):
        problem_value = str(problem_column[row_id])
        if len(problem_value) > 128:
          problem_value = problem_value[:128] + " ..."
        unique_problems[problem_value] = subtype

    displayed = False
    lines = []
    for value, count in occurrence_counts.items():
      if len(value) > 128:
        value = value[:128] + " ..."
      sample_line = f"{value} - {count} instance"

      if count > 1:
        sample_line += "s"
      if value in unique_problems:
        displayed = True
        subtype = unique_problems[value]
        sample_line += f" <-- {subtype}"

      lines.append(sample_line)
      if len(lines) >= 8:
        break

    if not displayed:
      for problem_str, problem_subtype in unique_problems.items():
        sample_line = f"{problem_str} - {occurrence_counts[problem_str]} instance <-- {problem_subtype}"
        lines.append(sample_line)

    if len(occurrence_counts) > 8:
      num_remaining = len(occurrence_counts) - 8
      lines.append(f"({num_remaining} other unique values ...)")

    main_subtype = col_props['subtype']
    col_description = f"{col_name} ({main_subtype})"
    prompt = problem_type_prompt.format(history=convo_history, column=col_description, samples='\n'.join(lines))
    raw_output = self.api.execute(prompt)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')
    return prediction

  def decide_issue_redirection(self, context, concerns, col_name):
    convo_history = context.compile_history()
    issue_types = concerns.detected_issue_types(col_name)

    issue_lines = []
    for issue_type in issue_types:
      count_issues = len(concerns.issues[issue_type][col_name])
      issue_lines.append(concerns.type_to_nl(issue_type, count_issues))
    issue_desc = PromptEngineer.array_to_nl(issue_lines, connector='and')

    prompt = issue_redirection_prompt.format(history=convo_history, description=issue_desc, column=col_name)
    raw_output = self.api.execute(prompt)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')
    return prediction['method']

  def attach_follow_up(self, context, flow, frame, state):
    if flow.interjected:
      flow.follow_up['query'] = frame.code
      flow.follow_up['thought'] = state.thought
      flow.follow_up['history'] = context.compile_history(look_back=3)
    return flow

  def display_issues(self, frame, state):
    # Get tables, rows, and columns from issue_entity
    tab_name, col_name = frame.issues_entity['tab'], frame.issues_entity['col']
    rows = list(frame.issues_entity['row'].keys())
    issue_col = {col_name}

    if col_name in self.database.db.shadow.table:
      display_series = self.database.db.shadow.table[col_name].loc[rows]
      display_df = display_series.to_frame()
    else:
      # Add on supporting columns from state entity
      support_cols = {ent['col'] for ent in state.entities if ent['tab'] == tab_name}
      columns = list(issue_col | support_cols)
      display_df = self.database.db.tables[tab_name].loc[rows, columns]
    return display_df

  def wrap_up_issues(self, flow, frame, state):
    if 'row' in frame.issues_entity:
      frame.deactivate_issues([])
    flow.completed = True
    state.has_issues = False
    return frame, flow, state

  def decide_main_entity(self, world, flow, issue_type):
    # select the one column with the most issues
    main_tab = flow.slots['source'].table_name()
    main_col = ''
    tab_metadata = world.metadata[issue_type][main_tab]
    issue_df = self.database.db.shadow.issues[main_tab]

    if any(ent['col'] == '*' for ent in flow.slots['source'].values):
      candidate_columns = world.valid_columns[main_tab]
    else:
      candidate_columns = [ent['col'] for ent in flow.slots['source'].values if ent['tab'] == main_tab]

    if not tab_metadata.prepared:
      for col_name in candidate_columns:
        column = self.database.db.tables[main_tab][col_name]
        issue_df, _ = tab_metadata.detect_issues(issue_df, column)
      self.database.db.shadow.issues[main_tab] = issue_df

    max_count = 0
    for col_name in candidate_columns:
      count = tab_metadata.num_issue_rows(issue_df, col_name, issue_type)
      if count > max_count:
        max_count, main_col = count, col_name
    return main_tab, main_col

  def resolve_issues(self, selected_rows, frame, state, world):
    """ Resolving an issue is acheived by performing one or more of the following techniques:
      * interpolate: which will be executed using insert {005}
      * modify: which will be executed using update {006}
      * remove: which will be executed using delete {007}
      * ignore: which will be executed using deny {00F}
    However, users do not need to resolve all issues at once. Accordingly, the selected rows represent
    the subset of row ids that we are dealing with right now. This function uses those row ids to:
      1. Deactivate issues being tracked in the frame
      2. Removing issues being tracked in the world issues metadata
    The selected rows are now considered resolved rows, and stored in the frame for future reference.
    Lastly, if the Detect flow has no more issues, we do some house-keeping logic to wrap up the flow.
    """
    issue_tab, issue_col = frame.issues_entity['tab'], frame.issues_entity['col']
    resolve_flow = state.get_flow(flow_type=frame.issues_entity['flow'])
    issue_metadata = world.metadata[resolve_flow.name()][issue_tab]
    issue_types = [frame.issues_entity['row'][row_id] for row_id in selected_rows]

    no_more_issues = frame.deactivate_issues(selected_rows)
    issue_metadata.remove_issues(issue_col, selected_rows, issue_types)
    frame.raw_table = issue_tab
    resolve_flow.turns_taken += 1

    if no_more_issues:
      resolve_flow.completed = True   # mark as completed, rather than removing. Leave that to RES
      state.has_issues = False        # declare that the state no longer has any issues
    else:
      frame.code = resolve_flow.describe_issues(frame)
      state = add_issues_entity(state, frame)
    return resolve_flow, frame, state

  def connect_information(self, context, state, world):
    # Supports {46D} which is a generic request to combine two data sources together, returns a proposed table
    flow = state.get_flow(flow_type='connect')
    frame = Frame(state.current_tab, source='interaction')

    if flow.slots['source'].filled:
      if flow.slots['target'].filled:
        source_tab = flow.slots['source'].values[0]['tab']
        target_tab = flow.slots['target'].values[0]['tab']
        source_df = self.database.db.tables[source_tab]
        new_df = self.combine_tables(source_df)
        self.database.db.tables[target_tab] = new_df

        unique_tables = set([ent['tab'] for ent in flow.slots['target'].values])
        if len(unique_tables) > 1:
          connected_tabs = list(unique_tables)
          frame.properties['tabs'] = connected_tabs
        else:
          connected_tabs = [state.current_tab]
        self.update_system_prompt(connected_tabs, world, context, flow)

        self.actions.remove('INTERACT')
        flow.completed = True

      else:
        flow.stage = 'pick-tab-col'
        frame.tab_type = 'decision'
        frame.raw_table = state.current_tab
    return frame, state

  def resolve_issues(self, context, state, world):
    # Supports {468} which is a planning request to identify issues within the table
    flow = state.get_flow(flow_type='resolve')

    if flow.slots['source'].filled and not flow.is_uncertain:
      tab_name = flow.slots['source'].values[0]['tab']
      if state.has_plan:
        if len(flow.slots['plan'].options) > 0:

          if flow.slots['plan'].filled:
            for selected_opt in flow.slots['plan'].values:
              fall_back_dax = flow2dax(selected_opt)
              if fall_back_dax != 'none':
                flow.fall_back = fall_back_dax
            flow.completed = True
            state.has_plan = False
          else:
            self.actions.add('CLARIFY')
            slot_desc = 'type of fix to apply'
            fix_options = flow.slots['plan'].options
            state.ambiguity.declare('confirmation', slot=slot_desc, values=fix_options, generate=True)
          frame = self.default_frame(state, state.entities)

        else:
          # propose different options for the user to consider
          for fix_opt in flow.slots['plan'].options:
            if fix_opt in ['dedupe', 'validate', 'format']:
              flow = self.collect_cleaning_option(tab_name, flow, fix_opt, world)
            else:
              flow = self.collect_issue_option(fix_opt, tab_name, flow, world)
            if len(flow.slots['plan'].options) >= 3:
              break
          # if no options are found, just randomly select two after shuffling
          if len(flow.slots['plan'].options) == 0:
            randomized_options = np.random.choice(flow.slots['plan'].options, size=2, replace=False)
            for random_opt in randomized_options:
              flow.slots['plan'].options.append(random_opt)
          frame = Frame(tab_name)

      else:
        frame, state = self.propose_fix_plan(context, flow, state, tab_name, world)
    else:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('partial', flow='resolve')
      if len(flow.slots['source'].active_tab) > 0:
        state.ambiguity.observation = "Is there a specific column you want to focus on?"
      else:
        state.ambiguity.observation = "Is there a specific table or column you have in mind?"
      frame = world.frames[-1] if world.has_data() else self.default_frame(state, state.entities)

    return frame, state

  def propose_fix_plan(self, context, flow, state, tab_name, world):
    routing_prompt = resolve_routing_prompt.format(table=tab_name, history=context.compile_history())
    raw_output = self.api.execute(routing_prompt)
    pred_situation = PromptEngineer.apply_guardrails(raw_output, 'json')

    match pred_situation:
      case 'blank': flow.fall_back = '46B'
      case 'concern': flow.fall_back = '46C'
      case 'typo': flow.fall_back = '46E'
      case 'problem': flow.fall_back = '46F'
      case 'validate': flow.fall_back = '36D'
      case 'format': flow.fall_back = '36F'
      case 'dedupe': flow.fall_back = '7BD'
      case _: flow.fall_back = ''

    if len(flow.fall_back) == 0:
      # then we are truly in a situation dealing with an open-ended request to fix issues
      state.has_plan = True
      frame, state = self.resolve_issues(context, state, world)
    else:
      frame = world.frames[-1] if world.has_data() else self.default_frame(state, state.entities)
    return frame, state

  def collect_issue_option(self, issue, tab_name, flow, world):
    # Run through the different issues to see if anything turns up
    issue_metadata = world.metadata[issue][tab_name]
    if not issue_metadata.prepared:
      issue_metadata.detect_issues(self.database.db.tables[tab_name])

    for entity in flow.slots['source'].values:
      if issue_metadata.num_issue_rows(entity['col']) > 0:
        if issue not in flow.slots['plan'].options:
          flow.slots['plan'].options.append(issue)

    return flow

  def collect_cleaning_option(self, tab_name, flow, option, world):
    option_subtypes = {
      'dedupe': ['name', 'email', 'address'],
      'validate': ['status', 'category', 'city', 'state'],
      'format': ['date', 'time', 'month', 'phone']
    }
    candidate_subtypes = option_subtypes.get(option, [])
    tab_schema = world.metadata['schema'][tab_name]

    for entity in flow.slots['source'].values:
      if entity['tab'] == tab_name:
        col_info = tab_schema.get_type_info(entity['col'], include_supplement=False)
        if col_info['subtype'] in candidate_subtypes:
          flow.slots['plan'].options.append(option)
    return flow

  def uncover_insights(self, context, state, world):
    """ Supports {146} as a method for performing advanced analysis requiring multiple metrics and variables. This flow
    also serves as a fallback for when the user makes an open-ended request for insights where they do not have the
    intention of analyzing the data, but merely want to see what the agent is capable of doing. If this happens, we set
    the stage of the flow as 'automatic-execution'. We keep going until all steps in the plan have been executed."""
    flow = state.get_flow(flow_type='insight')
    frame = world.frames[-1] if world.has_data() else self.default_frame(state, state.entities)

    if flow.slots['source'].filled and not flow.is_uncertain:
      tab_col_str = PromptEngineer.tab_col_rep(world)

      if state.has_plan and len(flow.scratchpad) > 0:

        if flow.slots['plan'].is_verified():
          flow, state = self.finish_up(context, flow, state, tab_col_str)
        elif flow.slots['plan'].filled:
          flow, state = self.execute_select_flow(context, flow, state, world)
        elif flow.slots['plan'].approved:
          flow, state = self.convert_to_stack_on(context, flow, state, world)
        else:  # user saw the plan, but gave feedback on how to change it
          flow, state = self.revise_plan(context, flow, state, tab_col_str)

      elif flow.stage.endswith('proposal'):
        if state.ambiguity.present():
          flow, state = self.review_insight_proposal(context, flow, state, tab_col_str)
        else:
          flow, state = self.propose_insight_plan(context, flow, state, tab_col_str)

      else:
        flow, state = self.insight_routing(context, flow, state, tab_col_str)
    else:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('partial', flow='insight')

    if flow.completed and not state.ambiguity.present():
      # fill frame with data so that we can summarize results
      table_df = self.database.db.tables[state.current_tab]
      query = f'SELECT * FROM {state.current_tab}'
      frame.set_data(table_df, query)
    return frame, state

  def insight_routing(self, context, flow, state, tab_col_str):
    prompt = insight_routing_prompt.format(history=context.compile_history())
    raw_output = self.api.execute(prompt)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

    flow.slots['analysis'].value = prediction['type']
    scenario = prediction['scenario'].lower()
    match scenario:
      case 'basic': flow.fall_back = '001'
      case 'intermediate': flow.fall_back = '002'
      case 'advanced': flow.stage = 'initialize-proposal'
      case 'vague': flow.stage = 'automatic-proposal'
      case _: flow.clarify_attempts = 0

    if flow.stage.endswith('proposal'):
      flow, state = self.review_insight_proposal(context, flow, state, tab_col_str)
      context.set_bookmark()    # so we can reference the first turn later
    elif flow.clarify_attempts == 0:
      state.thought = "Your request is out of scope and is not something I can handle."
      flow.completed = True
    return flow, state

  def propose_insight_plan(self, context, flow, state, tab_col_str):
    # generate a natural language plan to present to the user for approval
    convo_history = context.compile_history()
    if flow.stage.startswith('automatic'):
      prompt = automatic_plan_prompt.format(history=convo_history, valid_tab_col=tab_col_str)
    else:
      analysis_type = flow.slots['analysis'].value
      prompt = insight_plan_prompt.format(history=convo_history, type=analysis_type, valid_tab_col=tab_col_str)
    raw_output = self.api.execute(prompt, version='reasoning-model', max_tok=2048)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

    if len(prediction['plan']) <= 1:
      flow.fall_back = '001'
      flow.completed = True
    else:
      flow.scratchpad = prediction['plan']
      state.has_plan = True
    return flow, state

  def review_insight_proposal(self, context, flow, state, tab_col_str):
    # propose the type of analysis to run and ask for clarification on any missing information to conduct the analysis
    state.ambiguity.resolve()
    flow.clarify_attempts -= 1
    analysis_type = flow.slots['analysis'].value

    if flow.clarify_attempts > 0:
      prompt = proposal_confirmation_prompt.format(history=context.compile_history(), analysis_type=analysis_type,
                                                  valid_tab_col=tab_col_str)
      prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')
    else:
      prediction = {'questions': [], 'type': analysis_type}
      flow.completed = True

    if prediction['type'] != analysis_type:
      flow.slots['analysis'].value = prediction['type']
    # purposely *avoid* adding the CLARIFY action, since we want to use the flow to manage the ambiguity
    if len(prediction['questions']) > 0:
      state.ambiguity.declare('specific', flow='insight', slot='proposal', generate=True)
      state.ambiguity.observation = ' '.join(prediction['questions'][:2])
    return flow, state

  def revise_plan(self, context, flow, state, tab_col_str, restart=False):
    # the user has seen the plan, but has given feedback on how to change it
    convo_history = context.compile_history(look_back=7)
    if restart:
      prior_plan = PromptEngineer.display_plan(flow.slots['plan'].steps, join_key='\n')
    else:
      prior_plan = '\n'.join(flow.scratchpad)           # natural language plan
    analysis_type = flow.slots['analysis'].value

    revision_prompt = revise_hypothesis_prompt if flow.stage.startswith('automatic') else revise_plan_prompt
    prompt = revision_prompt.format(history=convo_history, previous_plan=prior_plan, analysis_type=analysis_type,
                                    valid_tab_col=tab_col_str)
    raw_output = self.api.execute(prompt, version='claude-sonnet')
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

    state.ambiguity.resolve()
    flow.scratchpad = prediction['plan']
    flow.clarify_attempts -= 1

    if flow.clarify_attempts < 0:
      flow.completed = True   # situation is too complex for the agent to handle, so we exit instead
    else:
      state.ambiguity.declare('specific', flow='insight', slot='plan', generate=True)
    return flow, state

  def convert_to_stack_on(self, context, flow, state, world):
    # convert the plan written in natural language into a plan composed of a series of stack_on flows
    analysis_type = flow.slots['analysis'].value
    nl_plan = ' '.join(flow.scratchpad)
    tab_col_str = PromptEngineer.tab_col_rep(world)
    prompt = convert_to_flow_prompt.format(analysis_type=analysis_type, plan_steps=nl_plan, valid_tab_col=tab_col_str)
    raw_output = self.api.execute(prompt, max_tok=1024)
    prediction = PromptEngineer.apply_guardrails(raw_output, 'json')

    flow.scratchpad = []
    flow.fill_slot_values(prediction, adjusted=False)
    if flow.slots['plan'].filled:
      match flow.stage:
        case 'automatic-proposal': flow.stage = 'automatic-execution'
        case 'initialize-proposal': flow.stage = 'plan-execution'
      flow, state = self.execute_select_flow(context, flow, state, world)
    else:
      self.actions.add('CLARIFY')
      state.ambiguity.declare('specific', flow='insight', slot='plan', generate=True)
    return flow, state

  def execute_select_flow(self, context, flow, state, world):
    """ cycle through a battery of analyses to find anything that can be considered interesting """
    if flow.turns_taken > 0 and len(flow.scratchpad) > 0:  # we have already executed a stack_on flow
      past_results = '\n'.join([f" * {summary['text']}" for summary in flow.scratchpad])
      prior_plan = PromptEngineer.display_plan(flow.slots['plan'].steps, join_key='\n')
      match flow.turns_taken:
        case 1: iteration = 'one step'
        case 2: iteration = 'two steps'
        case 3: iteration = 'three steps'
        case _: iteration = 'a few steps'

      prompt = adjust_plan_prompt.format(analysis_type=flow.slots['analysis'].value, previous_plan=prior_plan,
                            iteration=iteration,  history=context.compile_history(), past_results=past_results)
      prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

      state.thought = prediction['thought']
      if prediction['needs_adjustment']:
        flow.fill_slot_values(prediction, adjusted=True)

    # in either case, prepare the next stack_on flow for execution
    flow.turns_taken += 1
    self.actions.clear()
    next_step = next((step for step in flow.slots['plan'].steps if not step['checked']), None)
    if next_step is None or flow.turns_taken > 7:
      tab_col_str = PromptEngineer.tab_col_rep(world)
      return self.finish_up(context, flow, state, tab_col_str)
    for step in PromptEngineer.display_plan(flow.slots['plan'].steps):
      print(step)

    flow_dax, flow_desc = next_step['dax'], next_step['description']
    stack_on_flow = flow_selection[flow_dax](world.valid_columns)
    stack_on_flow.interjected = True
    if len(flow_desc) > 0 and 'operation' in stack_on_flow.slots:
      stack_on_flow.slots['operation'].values.append(flow_desc)

    stack_on_flow = self.transfer_metrics_and_entities(context, flow, stack_on_flow, next_step)
    state.flow_stack.append(stack_on_flow)
    state.store_dacts(dax=flow_dax)
    state.keep_going = True
    return flow, state

  def transfer_metrics_and_entities(self, context, flow, stack_on, next_step):
    # transfer over the source entities to the analyze flow
    for entity in flow.slots['source'].values:
      stack_on.slots['source'].add_one(**entity)

    if next_step['dax'] == '002' or next_step['dax'] == '02D':
      acronym = next_step.get('acronym', 'N/A')
      expanded = next_step.get('expanded', 'N/A')
      stack_on.slots['metric'].assign_metric(acronym, expanded)

      has_one_table, num_columns, tab_name = count_tab_cols(stack_on.slots['source'].values)
      if has_one_table and num_columns > 4:
        convo_history = context.compile_history()
        metric_name = stack_on.slots['metric'].formula.get_name()
        col_list = ', '.join([ent['col'] for ent in stack_on.slots['source'].values])
        prompt = focus_metric_prompt.format(history=convo_history, metric=metric_name, table=tab_name, columns=col_list)
        raw_output = self.api.execute(prompt, prefix='Columns:')
        pred_columns = [pred.strip() for pred in raw_output[8:].split(',')]

        stack_on.slots['source'].drop_unverified()
        for col_name in pred_columns:
          stack_on.slots['source'].add_one(tab_name, col_name)

    elif next_step['dax'] == '39B':
      stack_on.slots['style'].assign_one('sample')
      for step in flow.slots['plan'].steps:
        if not step['checked']:
          desc_str = f"  * {step['description']}"
          stack_on.slots['task'].add_one(desc_str)

    stack_on.origin = '146'
    return stack_on

  def write_to_scratchpad(self, curr_flow, prev_flow, table_df):
    # use all the information from metric or variable info to summarize results
    plan_description = PromptEngineer.display_plan(prev_flow.slots['plan'].steps, join_key='\n')
    iteration_map = ['<skip>', 'first', 'second', 'third', 'fourth', 'fifth', 'sixth', 'seventh', 'last']
    analysis_type = prev_flow.slots['analysis'].value
    task = f'performing {analysis_type}' if 'analysis' in analysis_type.lower() else f'analyzing {analysis_type}'

    prompt = summarize_results_prompt.format(analysis=task, iteration=iteration_map[prev_flow.turns_taken],
                                        current_plan=plan_description, table_md=PromptEngineer.display_preview(table_df))
    prediction = PromptEngineer.apply_guardrails(self.api.execute(prompt), 'json')

    for summary_text in prediction['summary']:
      summary_point = {'flow_name': curr_flow.name(full=True), 'text': summary_text}
      prev_flow.scratchpad.append(summary_point)

    prev_flow.slots['plan'].mark_as_complete(curr_flow.name(full=False))
    return curr_flow, prev_flow

  def finish_up(self, context, flow, state, tab_col_str):
    # wrap up the flow by presenting the results to the user
    # use all the information from metric or variable info to summarize results
    summaries = '\n'.join([f"  * {summary['text']}" for summary in flow.scratchpad])
    prior_plan = PromptEngineer.display_plan(flow.slots['plan'].steps, join_key='\n')

    if flow.stage.startswith('automatic'):
      prompt = interesting_enough_prompt.format(history=context.compile_history(), summaries=summaries,
                                          analysis_type=flow.slots['analysis'].value, previous_plan=prior_plan)
      raw_output = self.api.execute(prompt)
      result = PromptEngineer.apply_guardrails(raw_output, 'json')
      if not result['is_interesting']:
        return self.revise_plan(context, flow, state, tab_col_str, restart=True)

    state.has_plan = False
    flow.completed = True
    flow.stage = 'complete'
    return flow, state

