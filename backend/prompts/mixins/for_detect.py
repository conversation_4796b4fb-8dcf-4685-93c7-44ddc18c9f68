issue_redirection_prompt = """When interacting with the {column} column, we encountered {description}.
Given the final turn in the conversation history, decide how the user wants to address the issue. Possible methods include:
  * update - change the values to a new value based on user specification, including interpolating new values
  * remove - delete the rows with the issues
  * ignore - do nothing, leave the issues as they are
  * recommend - suggest a course of action to the user
  * unsure - the user has not given a clear resolution, potentially because they are exploring alternatives

Start by thinking carefully about what the user is saying in the last utterance, and then output the appropriate resolution.
Your entire response should only contain well-formatted JSON including the thought (string) and method (string), with no further explanations after the JSON output.

For example,
#############
_Conversation History_
User: Yes, please show me.
Agent: The email column has 3 missing values. We can fill in the values, remove the rows, or just ignore the problem.
User: This is fine, not much we can do

_Output_
```json
{{
  "thought": "the user is dismissing the missing emails as not an issue",
  "method": "ignore"
}}
```

_Conversation History_
Agent: Agent: I found 3 outliers in the DiscountAmount column. Would you like to investigate?
User: Sure, what are the values?
Agent: The outliers are 50, 75, and 100. What should I do with them?
User: Divide by 10 so they are in the correct range

_Output_
```json
{{
  "thought": "Calculating new values is an update action",
  "method": "update"
}}
```

_Conversation History_
User: So what's the most popular city then?
Agent: I found 4 missing values in the ResolvedStatus column. Please see the table for details.
User: I guess those weren't resolved then.

_Output_
```json
{{
  "thought": "the user has not made a clear decision",
  "method": "unsure"
}}
```

_Conversation History_
User: What does that mean?
Agent: I found 6 default values in the OrderDeliveryName column. We can update the values, remove the rows, or just ignore the problem.
User: Dunno, what do you think?

_Output_
```json
{{
  "thought": "the user is asking the agent to recommend a course of action",
  "method": "recommend"
}}
```

_Conversation History_
User: Are there any issues with the enrollment data?
Agent: I found 3 textual anomalies in the Enrollment Status column. Please see the table for more.
User: Let's just drop the ones that are misspelled

_Output_
```json
{{
  "thought": "The user wants to remove some subset of the anomalies",
  "method": "remove"
}}
```

_Conversation History_
User: How many clicks is that in total?
Agent: The total is 5,486 clicks. However, I found some concerns which may affect the answer. See the table for more.
User: What exactly are the concerns?
Agent: There are 7 numeric outliers in the Impressions column. How would you like to proceed?
User: We should compare with the average to see if they are significantly different

_Output_
```json
{{
  "thought": "the user is conducting some preliminary analysis before deciding on a resolution",
  "method": "unsure"
}}
```
#############
Please think about how the user wants to deal with the problem and output the appropriate JSON. Remember to only choose a method from one of the four valid options.

_Conversation History_
{history}

_Output_
"""

blank_type_prompt = """Given a sample of the data and rows containing potential blanks, determine the type of issue found in the column.
The potential blanks will be highlighted with an arrow (<--). The possible issue types are:
  * missing - tokens that represent empty or missing data, such as 'not available', 'NaN', 'NULL', or 'unknown'
  * default - tokens used as placeholders or default values, such as 'example', 'john doe', 'lorem ipsum', or 'test'
  * null - actual null values, these are very straightforward to recognize because they will be represented as '<N/A>'

Please start by constructing a concise thought concerning why the highlighted rows might be considered blank.
After deciding whether the blank is legitimate or not, output the most likely blank types in a list.
If the detected issues are actually reasonable given the context, then they are not real blanks, so the output should be an empty list.
Your entire response should be in well-formatted JSON with thought (string) and issues (list) as keys. There should be no further explanations after the JSON output.

For example,
#############
_Sample Data_
Column: Age
Under 18 - 75 instances
18 to 24 - 124 instances
35 to 44 - 150 instances
25 to 34 - 229 instances
N/A - 13 instances <--
45 to 54 - 17 instances
55 to 64 - 58 instances
65 and older - 30 instances

_Output_
```json
{{
  "thought": "The blank values show 'N/A', which are considered missing type",
  "issues": ["missing"]
}}
```

_Sample Data_
Column: Split Type
Train - 661 instances
Test - 100 instances <--
Dev - 150 instances
NULL - 3 instances <--

_Output_
```json
{{
  "thought": "The word 'Test' has a meaning here as part of the test set, rather than being a placeholder value.",
  "issues": [ ]
}}
```

_Sample Data_
Column: Shipping Address
5236 Lorraine Ave - 2 instances
home - 6 instances
8825 West 3rd St - 3 instances
1920 N. Cahuenga Blvd - 1 instances
<N/A> - 73 instances <--
3921 Sunset Blvd - 3 instances
878 Houston St - 3 instances
325 Rosalind Ave - 2 instances
(186 other unique values ...)

_Output_
```json
{{
  "thought": "These are obviously null values based on the presence of '<N/A>'.",
  "issues": ["null"]
}}
```

_Sample Data_
Column: Hobby
reading - 613 instances
running - 230 instances
swimming - 211 instances
cycling - 198 instances
hiking - 177 instances
fill in later - 19 instances <--
dancing - 156 instances
cooking - 143 instances
(7 other unique values ...)

_Output_
```json
{{
  "thought": "Text of 'fill in later' represents a placeholder value.",
  "issues": ["default"]
}}
```

_Sample Data_
Column: voter registration
Hudson County - 45 instances
North Hampton - 38 instances
not applicable - 17 instances <--
Middlesex County - 52 instances
Bergen County - 29 instances
Passaic County - 21 instances
Cape May - 13 instances
Rockland County - 8 instances
(16 other unique values ...)

_Output_
```json
{{
  "thought": "Text of 'not applicable' represents a missing value.",
  "issues": ["missing"]
}}
```

_Sample Data_
Column: Site Visits
5 - 43 instances
12 - 45 instances
8 - 42 instances
3 - 41 instances
<N/A> - 30 instances <--
7 - 53 instances
2 - 43 instances
4 - 42 instances
None - 17 instances <--
(161 other unique values ...)

_Output_
```json
{{
  "thought": "There are nulls with '<N/A>' and also missing values with 'None'.",
  "issues": ["null", "missing"]
}}
```

_Sample Data_
Column: continent
SA - 38 instances
NA - 43 instances <--
EU - 75 instances
AS - 42 instances
AF - 22 instances

_Output_
```json
{{
  "thought": "The token 'NA' has a meaning here of North America, rather than being a missing value.",
  "issues": [ ]
}}
```
#############
Now it's your turn! Think carefully about the highlighted rows and provide the most likely issue type in well-formatted JSON output.
For reference, we have the following conversation history and supporting details:

_Conversation History_
{history}

_Sample Data_
Column: {column}
{samples}

_Output_
"""

concern_type_prompt = """Given a sample of the data and rows containing potential concerns, determine the type of issues found in the column, if any.
Rows with potential concerns will be highlighted with an arrow (<--). The possible issue types are:
  * outlier - a numeric value that is significantly different from the rest of the data
  * anomaly - a textual value that is unexpected or inconsistent with the rest of the data
  * date_issue - a datetime that is inconsistent or set to some meaningless default
  * loc_issue - a location or address that is inconsistent or set to some meaningless default

Based on our understanding of the column's data type, the only issue is a '{main_type}', but this is exactly what we want to review.
Please start by constructing a concise thought concerning why the highlighted rows might be considered concerns.
After deciding whether the concern is legitimate or not, output the most likely concern types in a list.
If the detected issues are actually reasonable given the context, then they are not real concerns, so the output should be an empty list.
Your entire response should be in well-formatted JSON with thought (string) and issues (list) as keys. There should be no further explanations after the JSON output.

For example,
#############
_Sample Data_
Column: Total Spend
$3.45 - 2 instances
$2.67 - 5 instances
$2.34 - 1 instance
$300.00 - 1 instance <--
$1.23 - 3 instances
$2.01 - 3 instances
$2.92 - 2 instances
$2.30 - 4 instances
(1641 other unique values ...)

_Output_
```json
{{
  "thought": "The value of $300.00 is significantly higher than the rest of the data.",
  "issues": ["outlier"]
}}
```

_Sample Data_
Column: Projected Profit
$1,000 - 2 instances
$3,000 - 5 instances
$2,000 - 4 instance
$3,500 - 9 instance
-$1,500 - 3 instances <--
$2,500 - 3 instances
-$2,500 - 2 instances <--
$4,000 - 1 instances
(5 other unique values ...)

_Output_
```json
{{
  "thought": "A negative value is unusual, but can be interpreted as a loss, so this is not an outlier.",
  "issues": [ ]
}}
```

_Sample Data_
Column: arrival_date
May 12, 2023 - 2 instances
May 15, 2023 - 5 instances
May 17, 2023 - 4 instance
May 16, 2023 - 9 instance
May 0, 2023 - 1 instances <--
May 14, 2023 - 3 instances
May 13, 2023 - 3 instances
May 11, 2023 - 2 instances
(236 other unique values ...)

_Output_
```json
{{
  "thought": "The date of May 0, 2023 is not a valid date.",
  "issues": ["date_issue"]
}}
```

_Sample Data_
Column: Drop-off Location
Philadelphia - 24 instances
Lancaster - 18 instances
Harrisburg - 15 instances
Allenstown - 12 instances
Pittsburgh - 17 instances
Scranton - 13 instances
PA - 9 instances <--
Bethlehem - 7 instances
(8 other unique values ...)

_Output_
```json
{{
  "thought": "The location 'PA' is not a specific city and is likely a concern.",
  "issues": ["loc_issue"]
}}
```

_Sample Data_
Column: Deadline
2020-01-03 - 15 instances
2020-01-01 - 9 instances <--
2020-01-07 - 13 instances
2020-01-04 - 18 instances
2020-01-02 - 12 instances
2020-01-05 - 17 instances
2020-01-06 - 23 instances
2020-01-08 - 27 instances
(38 other unique values ...)

_Output_
```json
{{
  "thought": "Given that all the other dates are also the same month and year, 2020-01-01 does not seem out of place.",
  "issues": [ ]
}}
```

_Sample Data_
Column: Product Name
Radiant Glow Liquid Foundation - 1 instance
Rejuvenating Face Oil - 2 instances
Soothing Chamomile Eye Cream - 1 instance
Deep Moisture Hair Mask - 1 instance
Calming Chamomile Sleep Spray - 1 instance
Jasmine Joy Perfume - 1 instance
Brightening Under-Eye Cream - 1 instance
Protect your skin from harmful UV rays with our lightweight, non-greasy sun cream. Just spray on before heading out for a healthy protection. - 1 instance <-- 
Detangling Silk Protein Hair Spray - 1 instance
(227 other unique values ...)

_Output_
```json
{{
  "thought": "It seems a product description was mistakenly added to the product name.",
  "issues": ["anomaly"]
}}
```
#############
Now it's your turn! Think carefully about the highlighted rows and provide the most likely issue type in well-formatted JSON output.
For reference, we have the following conversation history and supporting details:

_Conversation History_
{history}

_Sample Data_
Column: {column}
{samples}

_Output_
"""

typo_type_prompt = """Given a sample of the data and rows containing potential typos, determine the type of issue found in the column.
The potential typos will be highlighted with an arrow (<--). The possible issue types are:
  * replacement: the word or phrase has meaning, but is likely incorrect as inferred from the context
    - suppose the values in the column are 'apple', 'pear', 'grape', 'orange', 'great', and 'banana'. Then 'great' should be replaced with 'grape'.
    - suppose the values in the column are 'LinkedIn', 'Facebook', 'Twitter', 'tiktok', 'Snapchat', and 'TikTok'. Then 'tiktok' should be replaced with 'TikTok'.
  * misspelled: the word is not found in a standard dictionary
    - suppose the values in the column are 'apple', 'pear', 'grape', 'ornage', 'banana', and 'kiwi'. Then 'ornage' is likely a misspelling of 'orange'.
    - suppose the values in the column are 'LinkedIn', 'Facebook', 'Twitter', 'TikToc', 'Snapchat', and 'TikTok'. Then 'TikToc' is likely a misspelling of 'TikTok'.
  * none: detected issues are not really typos, they are actually spelled correctly given the context

Please start by constructing a concise thought concerning why the highlighted rows might be considered a typo.
After deciding whether the typo is legitimate or not, output the most likely typo type as a single token.
If multiple issue types are present, then output the option which seems to appear most frequently.
Your entire response should be in well-formatted JSON with thought and issues as keys. There should be no further explanations after the JSON output.

For example,
#############
_Sample Data_
Column: first_contact_source
LinkedN - 18 instances <--
Phone - 19 instances
Salesforce - 15 instances
Direct Mail - 2 instances
LinkedIn - 23 instances
In Person - 7 instances
linkedin - 3 instances <--
Phone call - 11 instances <--
(12 other unique values ...)

_Output_
```json
{{
  "thought": "There are many typos, but the most prevalent is 'LinkedN' instead of 'LinkedIn'.",
  "issue_type": "misspelled"
}}
```

_Sample Data_
Column: Supplier
Radiant Revival Remedies - 135 instances
CrownGlow Creations - 98 instances
SilkenStrand Solutions - 126 instances
HydraHeal Holistics - 103 instances
Made You Blush - 152 instances
Radiant Revival Remedy - 3 instances <--
BellaCanvas Colors - 87 instances
Pure Touch Therapeutics - 112 instances
(6 other unique values ...)

_Output_
```json
{{
  "thought": "Although 'Remedy' is a valid word, it should be 'Remedies' to match the other values.",
  "issue_type": "replacement"
}}
```

_Sample Data_
Column: topline metrics
CVR - 21 instances
CPC - 18 instances
CPM - 15 instances
CAC - 12 instances
retention - 3 instances <--
CTR - 7 instances
CPA - 11 instances
ROI - 9 instances
(7 other unique values ...)

_Output_
```json
{{
  "thought": "While retention isn't an acronym, it is a valid metric, so it is not a typo.",
  "issue_type": "none"
}}
```

_Sample Data_
Column: paymentMethod
Paypal - 98 instances
Credit Card - 135 instances <--
Debit Card - 126 instances
Apple Pay - 103 instances
Credit - 152 instances

_Output_
```json
{{
  "thought": "It's unclear if 'Credit' or 'Credit Card' is correct, but they are likely the same thing.",
  "issue_type": "replacement"
}}
```

_Sample Data_
Column: final_mp_loc
Los Angeles - 135 instances
San Francisco - 126 instances
Santa Monica - 103 instances
Riverside - 98 instances
San Diego - 152 instances
Pasadena - 39 instances
San Deigo - 4 instances <--
Santa Barbara - 112 instances
(19 other unique values ...)

_Output_
```json
{{
  "thought": "The city 'San Deigo' is a common misspelling of 'San Diego'.",
  "issue_type": "misspelled"
}}
```

_Sample Data_
Column: EmailAddress
<EMAIL> - 1 instance
<EMAIL> - 1 instance <--
<EMAIL> - 1 instance
<EMAIL> - 1 instance <--
<EMAIL> - 1 instance
<EMAIL> - 1 instance
<EMAIL> - 1 instance
<EMAIL> - 1 instance
(514 other unique values ...)

_Output_
```json
{{
  "thought": "Although 'tomorrow' and 'license' are common typos, this is part of an email address where anything goes.",
  "issue_type": "none"
}}
```
#############
Now it's your turn! Think carefully about the highlighted rows and provide the most likely issue type in well-formatted JSON output.
For reference, we have the following conversation history and supporting details:

_Conversation History_
{history}

_Sample Data_
Column:
{samples}

_Output_
"""

problem_type_prompt = """Given a sample of the data and rows containing potential problems, determine the type of issue found in the column.
The potential problems will be highlighted with an arrow (<--) along with its supposed datatype. The possible issue types are:
  * mix_type - the column contains more than one data type, such as mixing numbers and text together. Data types are unique, datetime, location, number, or text.
  * mix_subtype - the column contains more than one subtype of a data type, such as mixing months and hours together in a datetime column
  * unsupported - the column contains an unrecognizable data structure. This is most likely becuase the content is a dictionary, list, or nested object.
  * none - detected issues are not real problems, they are actually reasonable given the context

In more detail, the datatypes with their subtypes are:
  * unique - each value holds a unique meaning. Includes IDs (often used as primary keys), pre-defined categories, set of statuses, and boolean values.
  * datetime - related to dates or times. Includes quarter, month, day, year, week, date, time, hour, minute, second, and timestamp.
  * location - the values are related to geographical locations or addresses. Includes streets, cities, states, countries, zip codes, and full addresses.
  * number - the values are numeric and can be used for calculations. Includes currency, percent, whole numbers, and decimals.
  * text - the values are textual and can include any characters. Includes email addresses, phone numbers, URLs, names, and general text.

Please start by constructing a concise thought concerning why the highlighted rows might be considered a problem.
If a column contains values from two subtypes of the same datatype, it is considered a mix_subtype issue. If the subtypes are from different datatypes, it is a mix_type issue.
If the candidate rows are actually the same subtype, then there is no problem, and the output should be an empty list.
After deciding whether there exists any legitimate problems, output any discovered issues in a list. When in doubt, assume no issues are present, which results in an empty list.
Your entire response should be in well-formatted JSON with thought (thought) and issues (list) as keys. There should be no further explanations after the JSON output.

For example,
#############
_Sample Data_
Column: Amount Paid
$25.00 - 26 instances
$30.00 - 51 instances
$32.00 - 19 instance
$39.00 - 29 instance
$28.00 - 30 instances
$40.00 - 31 instances
thirty-two dollars - 1 instance <-- general
$29.00 - 21 instances
(86 other unique values ...)

_Output_
```json
{{
  "thought": "The value 'thirty-two dollars' is text datatype, while the rest are number datatype.",
  "issues": ["mix_type"]
}}
```

_Sample Data_
Column: Avg. Rating
54.505 - 2 instances
70.266 - 1 instances
18.251 - 1 instances
49.891 - 1 instances
78.329 - 2 instances
64.103 - 2 instances
36 - 1 instances <-- whole
72.021 - 1 instances
33.617 - 1 instances
(496 other unique values ...)

_Output_
```json
{{
  "thought": "Although it is not a decimal subtype, the whole number '36' is perfectly reasonable as a rating.",
  "issues": [ ]
}}
```

_Sample Data_
Column: review_score
good - 26 instances
bad - 51 instances
great - 19 instance
ok - 31 instances <-- state
excellent - 29 instance
average - 30 instances

_Output_
```json
{{
  "thought": "The value 'ok' does not represent a state subtype. It is reasonable in this setting, and therefore not a problem.",
  "issues": [ ]
}}
```

_Sample Data_
Column: final_destination
New Haven - 25 instance
Bridgeport - 27 instances
Hartford - 71 instance
Stamford - 43 instances
Boston - 54 instances
New Jersey - 12 instances <-- state
Springfield - 62 instances
Providence - 38 instances
(54 other unique values ...)

_Output_
```json
{{
  "thought": "A state is mixed in with the city values, which are both the location datatype.",
  "issues": ["mix_subtype"]
}}
```

_Sample Data_
Column: Responses
{{status_code: 200, message: 'success'}} - 293 instances <-- general
{{status_code: 404, message: 'not found'}} - 51 instances  <-- general
Server Error - 19 instance
Forbidden - 29 instance
Unauthorized - 30 instances
{{status_code: 400, message: 'bad request'}} - 31 instances <-- general
<N/A> - 21 instances
{{status_code: 200, message: 'completed'}} - 17 instances <-- general
(86 other unique values ...)

_Output_
```json
{{
  "thought": "The column contains JSON values, which are not supported.",
  "issues": ["unsupported"]
}}
```

_Sample Data_
Column: ViewDate
2022-09-28 - 219 instance
2022-09-27 - 126 instances
2022-09-29 - 129 instance
2022-10-02 - 221 instances
2022-09-30 - 190 instances
2022-09-27 00:01:00 - 3 instances <-- timestamp
2022-10-01 - 131 instances
2022-10-03 - 167 instances
(36 other unique values ...)

_Output_
```json
{{
  "thought": "A timestamp is mixed in with the date values, which are both the datetime datatype.",
  "issues": ["mix_subtype"]
}}
```

_Sample Data_
Column: LastName
Hyun - 17 instances
Kim - 98 instances
Lee - 16 instances
Jun - 36 instances <-- month
Park - 52 instances
Choi - 29 instances
Sun - 8 instances
Min - 43 instances
(28 other unique values ...)

_Output_
```json
{{
  "thought": "The value 'Jun' is reasonable as a last name given the context rather than being the month of June.",
  "issues": [ ]
}}
```
#############
Now it's your turn! Think carefully about whether any data type or data structure problems exist and list them out in well-formatted JSON output.
For reference, we have the following conversation history and supporting details:

_Conversation History_
{history}

_Sample Data_
Column: {column}
{samples}

_Output_
"""

resolve_routing_prompt = """Based on the conversation history, the user is attempting to fix some issues in the '{table}' table.
However, we need to better understand the exact type of fix being requested. The different options are:
  * concern - identifying numeric outliers, textual anomalies, datetime inconsistencies, or location issues
  * blank - interpolating missing values, removing default values, or replacing null values
  * typo - correcting misspelled words or replacing similar terms that mean that same thing
  * problem - resolving mixed data types, mixed data subtypes, or unsupported data structures
  * dedupe - removing duplicate entries, such as trimming down to unique users, accounts, or campaigns
  * format - ensuring that the data adheres to a specific format, such as email addresses or phone numbers
  * validate - making sure that all data within a column falls within a specific range or set of values
  * open - an open-ended request that does not fit into any of the other categories

Please carefully consider the user's request and output the most likely fix type as a single token.
If there is no clear indication of the fix type, then output 'open'. This is most likely option, so prefer 'open' when in doubt.
Your entire response should be in well-formatted JSON with keys for thought (string) and fix_type (string), with no further explanations after the JSON output.

For example,
#############
_Conversation History
User: I'm concerned about some strange values in the column. Can you help me with that?

_Output_
```json
{{
  "thought": "Despite using the term 'concerned', this is actually an ambiguous open-ended request.",
  "fix_type": "open"
}}
```

_Conversation History
User: Those aren't important, we can get rid of them.
Agent: I have successfully deleted 19 rows, what would you like to do with the rest?
User: Now, let's figure out a way to fill in the missing values.

_Output_
```json
{{
  "thought": "The user wants to interpolate missing values",
  "fix_type": "blank"
}}
```

_Conversation History
User: Where are the top 5 cities with the highest satisfaction ratings?
Agent: The top cities are New York, NYC, Chicago, Houston, and New York City.
User: I think these names need to be cleaned up, cuz NYC and New York are the same thing

_Output_
```json
{{
  "thought": "The user wants to validate that all city names point to their canonical version",
  "fix_type": "validate"
}}
```

_Conversation History
User: What is the data type of the VersionNumber column?
Agent: The VersionNumber column is store as a whole number data type.
User: Yea, so that's a problem that needs fixing.

_Output_
```json
{{
  "thought": "Based on the context, the issue is about the data type of a column, which represents a problem",
  "fix_type": "problem"
}}
```

_Conversation History
User: Should we take a look at the Mixpanel activity now?
Agent: You got it, what would you like to do with the Mixpanel data?
User: Yea, so there's a problem that needs fixing.

_Output_
```json
{{
  "thought": "Despite using the term 'problem', this is actually an ambiguous open-ended request.",
  "fix_type": "open"
}}

_Conversation History
User: What is the CPC for each ad channel?
Agent: Sure, I will need to verify the cost and clicks first. Does this look right?
User: Yea, clickCount looks good.
Agent: The average CPC is $0.2553 for Google Ads and $0.1519 for Facebook Ads. See table for full breakdown.
User: It looks like many of the channels are actually repeated, which should be fixed.

_Output_
```json
{{
  "thought": "The specific issue is targeting duplicate ad channels",
  "fix_type": "dedupe"
}}
```

_Conversation History
User: Please change them into the standard format.
Agent: Ok, I have converted them all into MM/DD/YYYY
User: Do we have any issues with dates on any of the other tables?

_Output_
```json
{{
  "thought": "The user wants to format dates consistently",
  "fix_type": "format"
}}
```

_Conversation History
User: What are the different Earned Revenue amounts for each client?
Agent: The Earned Revenue for each client ranges from $1204 to $62134700.
User: Some of the numbers are way larger than the rest, which we should look into.

_Output_
```json
{{
  "thought": "Addressing outliers is considered a 'concern'",
  "fix_type": "concern"
}}
```

_Conversation History
User: What did you find?
Agent: There are 7 rows containing 'guerilla marketing', which should be corrected to 'guerrilla marketing'.
User: Can we fix the spelling across all the columns?

_Output_
```json
{{
  "thought": "The user wants to correct a common misspelling across all the data",
  "fix_type": "typo"
}}
```

_Conversation History
User: I've got a big report that needs to be delivered by the end of the week.
Agent: What specific issues are you facing with the report?
User: I need to clean up the data before I can analyze it.

_Output_
```json
{{
  "thought": "The user has not specified a clear issue to tackle.",
  "fix_type": "open"
}}
```
#############
Now it's your turn! Think carefully about the user's request and provide the most likely fix type in well-formatted JSON output.
Be sure to only choose from one of the eight valid options: concern, blank, typo, problem, dedupe, format, validate, or open.

_Conversation History_
{history}

_Output_
"""

# When drafting the analysis type, be as specific as possible. For example, prefer 'Ad Group Performance' over 'Advertising Analysis', or 'Customer Retention' over 'Churn Prediction'.
insight_routing_prompt = """Based on the conversation history, your task is to determine the scope of the analysis presented in the user's request.
Specifically, there are five possible scenarios we could be facing:
  1. basic: retrieve data from a table by filtering, grouping, sorting, or applying aggregation operations directly on the columns
    - typical aggregations include sum, count, average, min, max, or string_agg
    - aggregating the values within a column produces a 'variable'
  2. intermediate: calculate a metric that builds on intermediate variables rather than directly accessing the columns
    - popular metrics include Average Order Value (AOV), Customer Acquisition Cost (CAC), Cost per Click (CPC), Click-Thru Rate (CTR), Daily Active Users (DAU), Net Promoter Score (NPS), Email Open Rate, Profit, or Return on Ad Spend (ROAS)
    - commonly formed by subtracting, adding, multiplying, or dividing multiple variables
  3. advanced: open-ended exploration of the data to identify patterns or trends; complex analysis associated with calculating multiple metrics
    - the classic form of advanced analysis involves comparing two metrics to each other (ie. is LTV > CAC?)
    - if the user asks for the 'best' of something without specifying a metric, this is also considered 'advanced' because we need to calculate a few different metrics to reach a conclusion
  4. vague: generic request for insights or observations in the data without specifying any details or motivation
    - not only does the user fail to specify a metric, but also fails to provide much context for what they are trying to accomplish
    - if we can infer a plan of action from the conversation, then this is 'advanced', but the lack of a clear goal would make it 'vague'
  5. other: any predictive analysis involving training a machine learning model or prompting a large language model (LLM) is considered out of scope

Concretely, suppose the user is operating an e-commerce website and wants to better understand the customer journey:
  * if there already exist columns for 'conversions' and 'visits', then we can directly calculate CVR = conversions / visits, which is a 'basic' query.
  * if we calculate Conversion Rate by first preparing a conversion variable (ie. filter for the 'purchase' event type where 'purchase amount > 0'), then we are performing 'intermediate' analysis.
  * if our goal is to find the most effective channel, then we might consider CTR and CPC along with CVR. This is more than one metric, which implies 'advanced' analysis to uncover insights.
  * if the user simply asks us to "tell me something interesting about the data", then we are in 'vague' territory because there is no clear plan of action.
  * if the user asks us to "predict which sales leads are likely to convert", then we are in 'other' territory because this involves training a machine learning model.

Please start by thinking carefully about the user's request, paying particular attention to the level of complexity involved.
Then, pick the most appropriate scenario by choosing from one of five options: basic, intermediate, advanced, vague, or other. Finally, generate a short description of the type of analysis to perform.
Your entire response should be in well-formatted JSON including your thoughts (a few sentences), chosen scenario (single token), and analysis type (short phrase).
There should be no additional text or explanations after the JSON output.

For example,
---
_Conversation History_
User: Our latest email campaign seems to be performing much better than usual. What do you think?

_Output_
```json
{{
  "thought": "The user wants to compare the email open rate metric with the average email open rate. Comparing two metrics two each other is advanced.",
  "scenario": "advanced",
  "type": "Significance Testing"
}}
```

_Conversation History_
User: You've got to be kidding me! We spent over $2000 already? How did that happen?
Agent: The budget was set to $120 each day and we've been running for 17 days. Google often doesn't obey the budget exactly.
User: How much did we actually spend each day?

_Output_
```json
{{
  "thought": "Given a column for ad spend, we can group by day to calculate the average spend. Calculating a single variable is basic.",
  "scenario": "basic",
  "type": "Average Spend per Day"
}}
```

_Conversation History_
User: We've been seeing a lot of engagement with our new display campaign. I'm curious if there's a specific segment of our customers driving that.

_Output_
```json
{{
  "thought": "User segmentation requires calculating a metric to measure impact, and then comparing it across different segments. Slicing a metric across multiple segmentation variables is advanced.",
  "scenario": "advanced",
  "type": "User Segmentation"
}}
```  

_Conversation History_
User: I've been looking at our premium customers who signed up in Q3, and I'm wondering how many of them are still actively using the product. Is there a way to check this?

_Output_
```json
{{
  "thought": "The user wants to calculate the retention rate for a specific segment of users. Calculating a single metric is intermediate.",
  "scenario": "intermediate",
  "type": "Retention Rate"
}}
```

_Conversation History_
User: We're spending quite a bit on customer acquisition lately. I want to make sure we're not burning through cash too quickly compared to what we're making back. How can we check if this is sustainable?

_Output_
```json
{{
  "thought": "The user wants to calculate the profitability of our customer acquisition efforts. Calculating a metric to measure profitability is intermediate.",
  "scenario": "advanced",
  "type": "LTV to CAC Ratio"
}}
```

_Conversation History_
User: Can you help me find some insights in the data?

_Output_
```json
{{
  "thought": "The user wants to perform a generic analysis of the data. This is vague.",
  "scenario": "vague",
  "type": "generic analysis"
}}
```

_Conversation History_
User: Which channels do we have listed in the campaigns table?
Agent: We're running ads across Google, Facebook, and LinkedIn, plus some influencer campaigns.
User: Cool, so our marketing budget review is coming up and I need to figure out which channels we should double down on. How should we approach this?

_Output_
```json
{{
  "thought": "The user wants to model the mix of marketing channels to figure out which ones are most valuable.  This involves calculating a success metric per channel, which is advanced.",
  "scenario": "advanced",
  "type": "Multi-touch Attribution"
}}
```

_Conversation History_
User: Our advertising costs keep going up, and I want to understand which type of ads are actually getting people to click through. Some channels seem to be performing better but I want to be sure.

_Output_
```json
{{
  "thought": "The user wants to calculate the click-thru rate for each channel. Calculating a single metric is intermediate.",
  "scenario": "intermediate",
  "type": "Click-thru Rate"
}}
```

_Conversation History_
User: I've noticed we're losing some bigger accounts lately and I'd like to get ahead of this. Is there a way we could predict which customers might leave us before they actually do?

_Output_
```json
{{
  "thought": "While I can analyze the factors correlated with customer churn, training a machine learning model for predictive purposes is out of scope.",
  "scenario": "other",
  "type": "Churn Prediction"
}}
```

_Conversation History_
User: We've been running this new onboarding flow against our control for about a month now. The numbers look pretty good but I want to be confident before we roll it out to everyone. Can you dig into it?

_Output_
```json
{{
  "thought": "The user wants to analyze the results of an A/B test, which means comparing each split based on multiple metrics, so this is advanced.",
  "scenario": "advanced",
  "type": "A/B Testing Results"
}}
```
---
Now it's your turn! Based on the conversation history, please decide on the scenario and provide a short description of the key analysis type.

_Conversation History_
{history}

_Output_
"""

proposal_confirmation_prompt = """Given the conversation history, we believe the user wants to perform {analysis_type}.
However, for any given analysis, certain pieces of information are required before we can proceed. For example,
  * Customer Segmentation - How should we segment the users? What is the measure of impact for each segment?
  * LTV to CAC Ratio - What time range should we use to draw the cohort? What is the discount rate? What types of costs go into calculating CAC?
  * A/B Testing Results - How do we identify which users are assigned to each split? What metrics are used to determine success?
  * Significance Testing - What is baseline performance? What is the KPI we are measuring against? How much variation is considered significant?
  * Multi-touch Attribution - What attribution model should we use? What is the conversion event? What is the lookback window?
  * Retention / Churn Analysis - How do we define an active user? What is the time period for measuring retention? What potential churn factors should we investigate?
  * Conversion Funnel Optimization - What are the key stages in the funnel? What is the target conversion rate for each stage?
  * Content Analytics - What metrics should we use to measure content performance? How do we categorize different types of content?
  * Lead Scoring / RFM Analysis - How do we define recency, frequency, and monetary value? What are the thresholds for each category?

Of course, there are other types of analysis and therefore other questions that may be relevant as well. Use your best judgement according to the conversation.
Please start by forming a hypothesis about the analysis type based on the conversation history to produce a short 2-3 token name.
Given the analysis type, consider whether any critical parameters are missing. If so, add additional clarification questions to gather this information.
Your entire response should be in well-formatted JSON including keys for thought (string), type (string), and questions (list), with no further explanations after the JSON output.

For example,
---
_Example Data_
Tables: stripe_subscriptions_raw; GAds_Spend_export; ai_usage_metrics_prod
Columns: subscription_id, customer_id, current_plan_name, mrr_cents, billing_cycle_anchor, trial_end_date, payment_failure_count, discount_code_applied, initial_plan_name, created_at_utc;
campaign_id, campaign_name, ad_group_id, date, cost_micros, clicks, impressions, utm_medium, utm_campaign, landing_page_path, device_type, conversion_tracking_id, bidding_strategy, quality_score, conversion_value_micros, final_url_suffix;
user_email, prompt_id, content_type, tokens_consumed, prompt_cost_usd, accepted_suggestion, generated_timestamp, workspace_id, model_version, prompt_template_name

_Conversation History_
User: What happens if we add up all the costs for the past month?
Agent: The total ad spend adds up to $27,509.
User: Oh wow, we're burning through cash so quickly. Please do the analysis to see if this is sustainable.

_Output_
```json
{{
  "thought": "The user wants to calculate the profitability of our customer acquisition efforts. An appropriate analysis is the LTV to CAC ratio.",
  "type": "LTV to CAC Ratio",
  "questions": [
    "We can use a cohort for a month from the previous year. How does that sound?",
    "Also, do you have any preferred discount rate?",
    "I'm thinking of using the Google Ads total spend for CAC. Are there any other costs I should consider?"
  ]
}}
```

_Example Data_
Tables: Google Display q4, Mixpanel User Events, Segment CDP
Columns: placement_id, creative_asset_url, ad_group_name, device_category, geo_dma_code, avg_viewability_pct, slot_position, ad_size_px, raw_impressions;
device_id, session_id, ip_address, utm_source, landing_page_path, browser_language, os_version, time_on_site_sec, page_scroll_depth, exit_page_url;
twilio_id, user_id, email_domain, signup_source, job_title_raw, industry_raw, last_active_ts, total_sessions, has_downloaded_whitepaper

_Conversation History_
User: We've been seeing a lot of engagement with our new display campaign. I'm curious if there's a specific segment of our customers driving that.
Agent: It seems you want to perform Customer Segmentation. Before we get started, can you tell me how you define engagement? Also, how do you want to segment the customers?
User: Engagement is defined by the number of impressions and conversions. We can segment by device type and ad group.

_Output_
```json
{{
  "thought": "The user has implicitly accepted Customer Segmentation as the analysis type. Something else we still need to know is how conversions are defined.",
  "type": "User Segmentation",
  "questions": [
    "How exactly do we define conversions?"
  ]
}}
```

_Example Data_
Tables: constant_contact_campaigns, amplitude_analytics, Conferences List (Updated Jan 2025)
Columns: campaign_id, campaign_name, template_variant, sender_email_alias, target_segment_id, subject_line, preview_text, utm_campaign_code, scheduled_datetime, actual_send_datetime;
amp_id, campaign_id, delivery_timestamp, recipient_email_hash, device_type, email_client, bounce_type, open_count, click_count, unsubscribe_flag;
conference_id, conference_name, organizer_entity, venue_name, city_location, state_province, expected_attendance, booth_number, booth_size_sqft, sponsorship_tier

_Conversation History_
User: What's the email open rate been like recently for the Knock Your Socks Off campaign?
Agent: The email open rate for the Knock Your Socks Off campaign is 26.57% in the past two weeks.
User: That's pretty awesome, much better than usual. Is that right?
Agent: It sounds like you want to check if the open rate is significantly higher than usual. How much higher than usual is considered 'better'? Also, how far back should we look to calculate the average?
User: Something like 5% higher seems substantial. Let's look back 3 months.

_Output_
```json
{{
  "thought": "The user has implicitly accepted Significance Testing as the analysis type. Something else we still need to know is whether the 5% is absolute or relative.",
  "type": "Significance Testing",
  "questions": [
    "Just to be clear, is that 5% improvement measured on an absolute basis or a relative basis?"
  ]
}}
```

_Example Data_
Tables: facebook_ads_manager_daily, shopify_orders_202401, klayvio_customer_timeline
Columns: ad_id, campaign_name, adset_name, placement_type, daily_spend_cents, video_completion_rate, cta_button_type, audience_name, creative_format, instagram_eligible;
order_id, cart_token, landing_site_path, referring_site, discount_code_used, shipping_method, billing_postal_code, product_collection, total_price_cents, utm_parameters;
shopper_id, event_name, event_timestamp, device_fingerprint, last_utm_source, first_utm_source, session_number, cart_value_cents, products_viewed, time_to_purchase_sec

_Conversation History_
User: Which channels do we have listed as referring sites?
Agent: By looking at the utm_source columns, it seems the most common ones are Google, Mindful Momma, Sustainably Chic, and Going Zero Waste.
User: So our annual MBR is coming up and I need to figure out which mix of channels to double down on. How should we approach this?

_Output_
```json
{{
  "thought": "The user wants to model the mix of marketing channels to figure out which ones are most valuable. An appropriate analysis is Multi-touch Attribution.",
  "type": "Multi-touch Attribution",
  "questions": [
    "What is the conversion event we are trying to measure?",
    "Which attribution model should we use?"
  ]
}}
```

_Example Data_
Tables: SalesforceAccountsSnapshot2024Q1, PolicyClaims, RiskEngineAssessment
Columns: sfdc_account, property_portfolio_value, annual_premium_usd, last_risk_assessment_date, account_owner_email, payment_status, client_segment_tier, renewal_date, primary_contact_title, last_interaction_date;
claim_id, account_id, claim_type, reported_date, settlement_amount_usd, days_to_resolution, adjuster_notes, property_address, deductible_applied, claim_status;
property_id, assessment_date, building_age_years, flood_zone_category, last_renovation_year, coverage_type_code, property_type, square_footage, construction_material, sprinkler_system_status;

_Conversation History_
User: I've noticed we're losing some big accounts lately and I'd like to get ahead of this. Can we find out the drivers that are causing these customers to leave?
Agent: No problem! It seems you want to perform Retention Analysis. Before we get started, can you tell me how you define an active user? Also, how do you want to measure retention?
User: I'm more focused on risk of churn, rather than retention. Basically, a user has churned if they have cancelled their policy.
Agent: Got it. Can you tell me what potential churn factors should we investigate? Also, how should we determine whether an account is big?
User: Just look at all accounts. Use the data in the risk engine assessment table to figure out the churn factors.

_Output_
```json
{{
  "thought": "The feedback is to focus on Churn Analysis rather than Retention Analysis. Given their answers, we should have enough information to put together a plan.",
  "type": "Churn Analysis",
  "questions": [ ]
}}
```

_Example Data_
Tables: split_io_experiments, posthog_events_raw, user_activation_metrics
Columns: experiment_key, visitor_id, treatment_group, first_exposure_time, client_sdk_version, is_sticky_bucketing, feature_gate_name, traffic_type;
distinct_id, event_name, event_timestamp, current_url, screen_size, session_duration_sec, step_number, error_type, time_on_step, rage_clicks_count;
user_id, signup_date, invited_users_count, projects_created, files_uploaded, completion_percentage, days_to_activation, initial_setup_source, team_size_selected, industry_vertical

_Conversation History_
User: We've been running this new onboarding flow for about a month now. Let's jump to the split.io data to take a look.
Agent: Sure, the split_io_experiments table contains 316 rows and 8 columns. What would you like to know?
User: The numbers look pretty good but I want to be confident before we roll it out to everyone. Can you dig into it?
Agent: It seems you want to analyze the results of an A/B split test regarding the onboarding flow. Before we get started, can you tell me how you determine each split? Also, what are the key metrics we should compare?
User: Let's compare the conversion rate and user activation for each split.

_Output_
```json
{{
  "thought": "The user has implicitly accepted A/B Testing Results as the analysis type. However, we still need to know how to identify the split-test users.",
  "type": "A/B Testing Results",
  "questions": [
    "And how do we identify which users are assigned to each split? Perhaps the treatment_group column?"
  ]
}}
```
---
Now we move onto the real case. After thinking carefully, please decide on the analysis type followed by a list of questions (if needed) to clarify the key points of uncertainty.

_Available Data_
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

insight_plan_prompt = """As seen in the conversation history, the user wants to perform {type}.
To fulfill this request, we will proceed by generating a plan to extract insights through data analysis.
For our purposes, we define the following hierarchy of concepts:
  * column - content that is directly grounded to the table, such that one is able to simply read the values off the spreadsheet
  * variable - a specific value or dimension produced by aggregating (sum, count, average, etc.) or filtering data in a column
  * metric - a formula that calculates a specific value by establishing a relationship between one or more variables
  * plan - a sequence of steps executed in a particular order to produce a desired output; each step is often associated with a distinct metric or variable

Your proposed plan should be presented as a numbered list of steps. Possible steps include:
  * peeking at the data to get a sense of the format and distribution of values, which informs subsequent steps (eg. is a join possible?)
  * perform partial transformations by extracting or mapping values into staging columns, great for storing intermediate results
  * join multiple tables together to form a single view or table to simplify future queries
  * forming variables by aggregating columns (sum, count, average, etc.), filtering for specific ranges, and/or grouping information across key dimensions
  * calculating a metric (eg. ARPU, CAC, CPM, CVR, DAU, MRR, ROAS, Retention Rate, Net Profit, etc.) based on available variables
  * basic control flow, namely branching or looping logic, based on the results of previous steps
  * visualizing or summarizing the data to present final results in a clear and concise manner

To be clear, the steps above do NOT imply the order or count of steps in a typical plan. In fact, an ideal plan:
  * determines the order based on the logical flow required by the analysis. The steps outlined above simply provide a pool of options to consider.
  * consists of only 3 to 5 steps. If you find yourself using more, there is good chance you should simplify the plan.
  * may even repeat step types. For example, if multiple metrics need to be calculated, then each one may require a separate calculation step.

Start by grounding your plan in the data that is actually available, and build a bridge towards the desired result using standard data transformation techniques.
Concretely, include the specific columns being referenced within parentheses as part of your thinking. Then decide how to use them to produce the desired metric or insight.
After each step, the output will be summarized to a few lines of text and saved to a scratch pad that can be accessed in subsequent steps.
Thus, your plan can include branching logic that depends on the results of previous steps if that is useful to fulfill the request.

Your entire response should be in well-formatted JSON including keys for thought (string) and plan (list), where each step is a precise action to be executed.
There should be no further text or explanations after the JSON output.

For example,
---
_Example Data_
Tables: stripe_subscriptions_raw; GAds_Spend_export; ai_usage_metrics_prod
Columns: subscription_id, customer_id, current_plan_name, mrr_cents, billing_cycle_anchor, trial_end_date, payment_failure_count, discount_code_applied, initial_plan_name, created_at_utc;
campaign_id, campaign_name, ad_group_id, date, cost_micros, clicks, impressions, utm_medium, utm_campaign, landing_page_path, device_type, conversion_tracking_id, bidding_strategy, quality_score, conversion_value_micros, final_url_suffix;
user_email, prompt_id, content_type, tokens_consumed, prompt_cost_usd, accepted_suggestion, generated_timestamp, workspace_id, model_version, prompt_template_name

_Conversation History_
User: Oh wow, we're burning through cash so quickly. Please do the analysis to see if this is sustainable.
Agent: No problem, I will analyze the LTV to CAC Ratio. Before we proceed, I want to know which cohort to we can use, perhaps the month of new users from the previous year. How does that sound? Also, do you have any preferred discount rate?
User: Sure, last year sounds good. I'm not sure about the discount rate, maybe 10%?
Agent: Ok, I will use a discount rate of 10%. I'm thinking of using the Google Ads total spend for CAC. Are there any other costs I should consider?
User: No, that should be fine.

_Output_
```json
{{
  "thought": "The key metrics are CAC and LTV. The key variables are marketing spend (cost_micros) and revenue (mrr_cents). To get the right answer, I should filter for users who signed up a year ago to create the cohort. I also need to project into the future with a 10% discount rate.",
  "plan": [
    "1. Calculate Customer Acquisition Costs (CAC) - filter for YoY users within a month and aggregate marketing spend per customer from GAds_Spend_export",
    "2. Calculate Customer Lifetime Value (LTV) - sum up all past revenue generated by the cohort, then apply a 10% discount rate to account for future value, extending out for three years",
    "3. Finalize LTV/CAC Ratio - compare customer LTV and total CAC to review whether our actions are sustainable, flag any concerning trends"
  ]
}}
```

_Example Data_
Tables: Google Display q4, Mixpanel User Events, Segment CDP
Columns: placement_id, creative_asset_url, ad_group_name, device_category, geo_dma_code, avg_viewability_pct, slot_position, ad_size_px, raw_impressions;
device_id, session_id, ip_address, utm_source, landing_page_path, browser_language, os_version, time_on_site_sec, page_scroll_depth, exit_page_url;
twilio_id, user_id, email_domain, signup_source, job_title_raw, industry_raw, last_active_ts, total_sessions, has_downloaded_whitepaper

_Conversation History_
User: We've been seeing a lot of engagement with our new display campaign. I'm curious if there's a specific segment of our customers driving that.
Agent: It seems you want to perform Customer Segmentation. Before we get started, can you tell me how you define engagement? Also, how do you want to segment the customers?
User: Engagement is defined by the number of impressions and conversions. I'm not sure how to break it down, please help me think of the right segments.
Agent: Great, one more question - how exactly do we define conversions?
User: It's when anyone downloads a whitepaper

_Output_
```json
{{
  "thought": "The key metric is engagement rate. The key variables are impressions (raw_impressions) and conversions (has_downloaded_whitepaper). I'll need to explore the data to determine the right segments.",
  "plan": [
    "1. Define User Segment - peek at device_id, ad_group_name, and geo_dma_code columns to consider if they may be useful segments",
    "2. Stage Engagement Variables - create tables that aggregate the number of impressions and conversions for each promising segmentation dimension",
    "3. Calculate Engagement Metrics - calculate view-through rate and whitepaper download rate for each segment",
    "4. Summarize Segment Performance - repeat steps 2 and 3 for each segmentation dimension to compare and contrast the results"
  ]
}}
```  

_Example Data_
Tables: facebook_ads_manager_daily, shopify_orders_202401, klayvio_customer_timeline
Columns: ad_id, campaign_name, adset_name, placement_type, daily_spend_cents, video_completion_rate, cta_button_type, audience_name, creative_format, instagram_eligible;
order_id, cart_token, landing_site_path, referring_site, discount_code_used, shipping_method, billing_postal_code, product_collection, total_price_cents, utm_parameters;
shopper_id, event_name, event_timestamp, device_fingerprint, last_utm_source, first_utm_source, session_number, cart_value_cents, products_viewed, time_to_purchase_sec

_Conversation History_
User: Which channels do we have listed as referring sites?
Agent: By looking at the utm_source columns, it seems the most common ones are Google, Mindful Momma, Sustainably Chic, and Going Zero Waste.
User: So our annual MBR is coming up and I need to figure out which mix of channels to double down on. How should we approach this?
Agent: It seems you want to perform Multi-touch Attribution. Before we get started, can you tell me what is the conversion event we are trying to measure? Also, which attribution model should we use?
User: The conversion event is when a customer makes a purchase. We should use both first-touch and last-touch attribution models.

_Output_
```json
{{
  "thought": "The key metric is attribution. The key variables are conversion (order_id) and channel (campaign_name, first_utm_source, last_utm_source). To get the right answer, I should normalize the channel names and apply both first-touch and last-touch attribution models.",
  "plan": [
    "1. Prepare Channel Data - peek at all source columns: campaign_name, adset_name, referring_site, utm_parameters, first_utm_source, and last_utm_source to understand how the channels are related and normalize their format if necessary",
    "2. Build User Journeys - combine the Facebook, Shopify, and Klayvio tables to create user journeys for each customer, keeping only the relevant columns",
    "3. Apply Attribution Models - calculate channel contribution to ROAS and purchases using first-touch and last-touch attribution models",
    "4. Compare Channel Performance - rank the performance across channels, accounting for assisted conversions; plot the results to present them in a clear and concise manner"
  ]
}}
```

_Example Data_
Tables: constant_contact_campaigns, amplitude_analytics, Conferences List (Updated Jan 2025)
Columns: campaign_id, campaign_name, template_variant, sender_email_alias, target_segment_id, subject_line, preview_text, utm_campaign_code, scheduled_datetime, actual_send_datetime;
amp_id, campaign_id, delivery_timestamp, recipient_email_hash, device_type, email_client, bounce_type, open_count, click_count, unsubscribe_flag;
conference_id, conference_name, organizer_entity, venue_name, city_location, state_province, expected_attendance, booth_number, booth_size_sqft, sponsorship_tier

_Conversation History_
User: That's pretty awesome, much better than usual. Is that right?
Agent: It sounds like you want to check if email open rate for Knock Your Socks Off campaign is significantly better than usual. How much higher than usual is considered 'better'? Also, how far back should we look to calculate the average?
User: Something like 5% higher seems substantial. We can look at click-thru rate and unsubscribe rate too.
Agent: Just to be clear, is that 5% improvement measured on an absolute basis or a relative basis?
User: I'm not sure, you can pick one.

_Output_
```json
{{
  "thought": "The key metrics are open rate, CTR, and unsubscribe rate. Critical columns open_count, click_count, and unsubscribe_flag. The user didn't specify a time period, so I will default to the last three months. I will define 'better' as a 5% absolute improvement.",
  "plan": [
    "1. Data Assessment - look for anomalies in delivery_timestamp to ensure we have the date range we need, check that unsubscribe_flag is not set to True for all records",
    "2. Establish Baseline Metrics - calculate open rate, click-through rate, and unsubscribe rate, focused on the last three months and grouped by campaign",
    "3. Target Cohort Performance - filter to just the performance of the Knock Your Socks Off campaign",
    "4. Compare Target to Baseline - test if the target's performance is at least 5% better than the baseline on an absolute basis on all three metrics"
  ]
}}
```

_Example Data_
Tables: SalesforceAccountsSnapshot2024Q1, PolicyClaims, RiskEngineAssessment
Columns: sfdc_account, property_portfolio_value, annual_premium_usd, last_risk_assessment_date, account_owner_email, payment_status, client_segment_tier, renewal_date, primary_contact_title, last_interaction_date;
claim_id, account_id, claim_type, reported_date, settlement_amount_usd, days_to_resolution, adjuster_notes, property_address, deductible_applied, claim_status;
property_id, assessment_date, building_age_years, flood_zone_category, last_renovation_year, coverage_type_code, property_type, square_footage, construction_material, sprinkler_system_status;

_Conversation History_
User: I've noticed we're losing some big accounts lately and I'd like to get ahead of this. Can we find out the drivers that are causing these customers to leave?
Agent: No problem! It seems you want to perform Retention Analysis. Before we get started, can you tell me how you define an active user? Also, how do you want to measure retention?
User: I'm more focused on risk of churn, rather than retention. Basically, a user has churned if they have cancelled their policy.
Agent: Got it. Can you tell me what potential churn factors should we investigate? Also, how should we determine whether an account is big?
User: Just look at all accounts. Use the data in the risk engine assessment table to figure out the churn factors.

_Output_
```json
{{
  "thought": "The key metric is churn rate. The key variables are policy status (renewal_date, payment_status) and risk factors (building_age_years, flood_zone_category, etc.). To get the right answer, I should filter for churned accounts and then analyze the risk factors.",
  "plan": [
    "1. Define Churn Accounts - identify churned accounts from the Salesforce table to by looking at the policy status (renewal_date, payment_status)",
    "2. Aggregate Risk Factors - combine claims history (claim_status, settlement_amount_usd, claim_type), risk assessments (building_age_years, flood_zone_category, property_type), and account characteristics into a single view",
    "3. Model Churn Drivers - analyze correlation between risk attributes and churn probability to help discover at-risk accounts in need of intervention"
  ]
}}
```

_Example Data_
Tables: split_io_experiments; posthog_events_raw; user_activation_metrics
Columns: experiment_key, visitor_id, treatment_group, first_exposure_time, client_sdk_version, is_sticky_bucketing, feature_gate_name, traffic_type;
distinct_id, event_name, event_timestamp, current_url, screen_size, session_duration_sec, step_number, error_type, time_on_step, rage_clicks_count;
user_id, signup_date, invited_users_count, projects_created, files_uploaded, completion_percentage, days_to_activation, initial_setup_source, team_size_selected, industry_vertical

_Conversation History_
User: The numbers look pretty good but I want to be confident before we roll it out to everyone. Can you dig into it?
Agent: It seems you want to analyze the results of an A/B split test regarding the onboarding flow. Before we get started, can you tell me how you determine each split? Also, what are the key metrics we should compare?
User: Let's compare the conversion rate and user activation for each split.
Agent: And how do we identify which users are assigned to each split?  Perhaps the treatment_group column?
User: Oh right. Actually, use the experiment key column.

_Output_
```json
{{
  "thought": "The key metrics are conversion rate and user activation. The key variables are conversions (signup_date) and user activation (days_to_activation, initial_setup_source). To get the right answer, I should filter for each split based on the experiment key.",
  "plan": [
    "1. Segment Test Splits - create an filtered view for each split based on the experiment key",
    "2. Calculate Success Metrics - measure conversion lift and user activation for each split in the onboarding flow, adding new columns for each",
    "3. Summarize Findings - compare the experimental splits to the control, and decide whether the new flow(s) should be rolled out"
  ]
}}
```
---
Now it's your turn! Based on the conversation history, please devise a carefully considered plan for {type}.

_Available Data_
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

automatic_plan_prompt = """Based on the conversation history, the user has made a generic request for insights about the data.
To fulfill this request, you should generate a hypothesis about the type of analysis most likely to be relevant according to the available tables and columns.
In doing so, keep in mind the following hierarchy of concepts:
  * column - content that is directly grounded to the table, such that one is able to simply read the values off the spreadsheet
  * variable - a specific value or dimension of the data calculated by applying an aggregation or operation to one or more columns
  * metric - a formula that calculates a specific value by establishing a relationship between one or more variables
  * plan - a sequence of steps executed in a particular order to conduct data analysis, where each step is associated with a distinct metric or variable

Your proposed hypothesis should carry out a specific plan of one or more steps. Typical steps include:
  * peeking at the data to get a sense of the available columns and their values, which informs the subsequent steps
  * filtering or removing data to focus on specific rows or columns that are relevant to the analysis
  * performing data transformations to create new rows or columns, great for storing intermediate results
  * staging the data to create new tables or views (ie. variables) that aggregate information across multiple columns
  * calculating a metric (eg. ARPU, CAC, CPM, CVR, DAU, MRR, ROAS, Retention Rate, Net Profit, etc.) based on available variables
  * basic control flow, namely branching or looping logic, based on the results of previous steps
  * visualizing or summarizing the data to present final results in a clear and concise manner

As guidance, typical analysis types include:
  * Customer Segmentation - group customers based on shared characteristics and behaviors, then compare the performance of each segment
  * LTV to CAC Ratio - calculate the lifetime value of a customer and compare it to the cost of acquiring that customer
  * A/B Testing Results - check the split-test results to determine if the variant is better than the control and by how much
  * Cohort Analysis - group customers based on shared characteristics and behaviors, then compare the performance of each cohort over time
  * Significance Testing - compare the performance of some target group to the baseline to determine if the difference is statistically significant
  * Churn Analysis - identify the factors that contribute to customer churn and develop strategies to reduce it, or equivalently to retain customers
  * Multi-touch Attribution - determine the contribution of each marketing channel to conversion, and develop an optimal marketing mix
  * Conversion Funnel Optimization - identify the stages in the conversion funnel where customers are dropping off and develop strategies to improve them
  * Content Analytics - measure the performance of different types of content and develop strategies to improve it
  
Start by grounding your hypothesis in the columns, and build a bridge towards the larger plan by coming up with variables, metrics, analysis type, and then finally a plan.
Before deciding on the type of analysis, take some time to think about what type of analysis would be most relevant given the metrics and variables.
If a complex plan is not possible, perhaps due to limited number of columns, then a simple plan that simply calculates a single metric is also acceptable.
Your entire response should be in well-formatted JSON including keys for variables (list), metrics (list), thought (string), hypothesis (short phrase), and plan (list).
There should be no further text or explanations after the JSON output.

For example,
#############
Suppose the available data includes:
Tables: stripe_subscriptions_raw; GAds_Spend_export; ai_usage_metrics_prod
Columns: subscription_id, customer_id, current_plan_name, mrr_cents, billing_cycle_anchor, trial_end_date, payment_failure_count, discount_code_applied, initial_plan_name, created_at_utc;
campaign_id, campaign_name, ad_group_id, date, cost_micros, clicks, impressions, utm_medium, utm_campaign, landing_page_path, device_type, conversion_tracking_id, bidding_strategy, quality_score, conversion_value_micros, final_url_suffix;
user_email, prompt_id, content_type, tokens_consumed, prompt_cost_usd, accepted_suggestion, generated_timestamp, workspace_id, model_version, prompt_template_name

_Output_
```json
{{
  "variables": [
    "Monthly/Annual Revenue per Customer (from mrr_cents)"
    "Customer Lifetime (from billing_cycle_anchor and created_at_utc)"
    "Marketing Spend per Customer (aggregating cost_micros)"
    "AI Usage Frequency per User (aggregating generated_timestamp)"
    "AI Cost per User (summing prompt_cost_usd)"
    "Acceptance Rate (aggregating accepted_suggestion)"
    "Deal Success Rate (from deal_stage_name)"
  ],
  "metrics": [
    "Customer Lifetime Value (LTV) = Monthly/Annual Revenue per Customer * Customer Lifetime"
    "Customer Acquisition Cost (CAC) = Marketing Spend per Customer * Acceptance Rate"
    "AI Usage Efficiency = AI Cost per User / AI Usage Frequency per User"
    "Net Profit Margin = (Monthly/Annual Revenue per Customer - AI Usage Cost per User) / Monthly/Annual Revenue per Customer"
  ],
  "thought": "The dataset covers customer subscriptions, marketing spend, and AI usage metrics. Given the top line metrics, an appropriate analysis is the LTV to CAC ratio.",
  "hypothesis": "LTV to CAC Ratio",
  "plan": [
    "1. Calculate Customer Acquisition Costs (CAC) - filter for YoY users within a month and aggregate marketing spend per customer from GAds_Spend_export",
    "2. Calculate Customer Lifetime Value (LTV) - sum up all past revenue generated by the cohort, then apply a 10% discount rate to account for future value, extending out for three years",
    "3. Finalize LTV/CAC Ratio - compare customer LTV and total CAC to review whether our actions are sustainable, flag any concerning trends"
  ]
}}
```

In our second example, suppose the available data includes:
Tables: Google Display q4, Mixpanel User Events, Segment CDP
Columns: placement_id, creative_asset_url, ad_group_name, device_category, geo_dma_code, avg_viewability_pct, slot_position, ad_size_px, raw_impressions;
device_id, session_id, ip_address, utm_source, landing_page_path, browser_language, os_version, time_on_site_sec, page_scroll_depth, exit_page_url;
twilio_id, user_id, email_domain, signup_source, job_title_raw, industry_raw, last_active_ts, total_sessions, has_downloaded_whitepaper

_Output_
```json
{{
  "variables": [
    "User Segment (from device_id and ad_group_name)",
    "Total Impressions (from raw_impressions)",
    "Ad Viewability by Placement (from avg_viewability_pct)",
    "Average Session Duration (from time_on_site_sec)",
    "User Engagement Depth (from page_scroll_depth)",
    "Content Engagement (from has_downloaded_whitepaper)",
    "Channel Performance (from utm_source)"
  ],
  "metrics": [
    "Engagement Quality Score = f(Time on Site, Scroll Depth, Whitepaper Downloads)"
    "Channel Efficiency = Cost per Conversion by Source"
    "Ad Position Impact = Conversion Rate by Slot Position"
  ],
  "thought": "This dataset covers display ads, user events, and customer data. We could dig into the segments that drive the most conversions and engagement.",
  "hypothesis": "Customer Segmentation",
  "plan": [
    "1. Define User Segment - peek at the device_id and ad_group_name columns to understand how to segment the users",
    "2. Stage Engagement Variables - create tables that aggregate the number of impressions and conversions for each segment",
    "3. Calculate Engagement Metrics - calculate scroll depth, time on site, and whitepaper download rate for each segment",
    "4. Summarize Segment Performance - identify top performing segments and present the results in a clear and concise manner",
  ]
}}
```

For our third example, suppose the available data is:
Tables: facebook_ads_manager_daily, shopify_orders_202401, klayvio_customer_timeline
Columns: ad_id, campaign_name, adset_name, placement_type, daily_spend_cents, video_completion_rate, cta_button_type, audience_name, creative_format, instagram_eligible;
order_id, cart_token, landing_site_path, referring_site, discount_code_used, shipping_method, billing_postal_code, product_collection, total_price_cents, utm_parameters;
shopper_id, event_name, event_timestamp, device_fingerprint, last_utm_source, first_utm_source, session_number, cart_value_cents, products_viewed, time_to_purchase_sec

_Output_
```json
{{
  "variables": [
    "Daily Ad Spend by Platform (from daily_spend_cents)",
    "Video Engagement Rate (from video_completion_rate)",
    "Average Order Value (from total_price_cents)",
    "Campaign Performance by CTA Type (from cta_button_type)",
    "Customer Journey Touchpoints (from event_name, referring_site, utm_parameters)"
    "Geographic Sales Distribution (from billing_postal_code)",
    "Cross-collection Purchase Rate (from product_collection)"
  ],
  "metrics": [
    "Return on Ad Spend (ROAS) = Revenue / Ad Spend",
    "CTA Effectiveness = Conversion Rate by Button Type",
    "Cart Abandonment Rate = Completed Orders / Cart Sessions",
    "Campaign Efficiency = Cost per Purchase by Campaign"
  ],
  "thought": "This dataset covers Facebook, Shopify, and Klayvio. Looking at the customer journey and other utm tracking, an appropriate analysis is Conversion Optimization.",
  "hypothesis": "Conversion Optimization",
  "plan": [
    "1. Prepare Channel Data - peek at all source columns: campaign_name, adset_name, referring_site, and utm_parameters to understand how the channels are related and normalize their format if necessary",
    "2. Build User Journeys - combine the Facebook, Shopify, and Klayvio tables to create conversion paths",
    "3. Calculate Conversion Metrics - calculate the AOV, ROAS, and purchase rate for each step of the journey
    "4. Compare Channel Performance - segment the performance across channels such as by CTA, geography, and product collection"
  ]
}}
```

Moving onto the fourth example, suppose the data includes:
Tables: constant_contact_campaigns, amplitude_analytics, Conferences List (Updated Jan 2025)
Columns: campaign_id, campaign_name, template_variant, sender_email_alias, target_segment_id, subject_line, preview_text, utm_campaign_code, scheduled_datetime, actual_send_datetime;
amp_id, campaign_id, delivery_timestamp, recipient_email_hash, device_type, email_client, bounce_type, open_count, click_count, unsubscribe_flag;
conference_id, conference_name, organizer_entity, venue_name, city_location, state_province, expected_attendance, booth_number, booth_size_sqft, sponsorship_tier

_Output_
```json
{{
  "variables": [
    "Email Campaign Engagement Rate (from open_count, click_count)",
    "Delivery Success Rate (from bounce_type)",
    "Client Platform Distribution (from email_client, device_type)",
    "Template Performance (from template_variant)",
    "Subject Line Effectiveness (comparing subject_line to open_count)",
    "Sponsorship Value (from sponsorship_tier, booth_size_sqft)",
    "Unsubscribe Rate (from unsubscribe_flag)"
  ],
  "metrics": [
    "Email Engagement Score = f(Opens, Clicks, Unsubscribes)",
    "Conference ROI Potential = Sponsorship Value * Expected Attendance",
    "Delivery Quality = (1 - Bounce Rate)",
    "Campaign Performance = Engagement Rate - Unsubscribe Rate"
  ],
  "thought": "This dataset covers email campaigns, user engagement, and conference data. We could test whether certain campaigns are significantly better than others.",
  "hypothesis": "Significance Testing",
  "plan": [
    "1. Data Assessment - look for anomalies within our data, such as large gaps in the data or columns containing only a single value",
    "2. Establish Baseline Metrics - calculate open rate, click-through rate, and unsubscribe rate, focused on the last three months and grouped by campaign",
    "3. Target Cohort Performance - filter to just the performance of the some specific campaign or cohort",
    "4. Compare Target to Baseline - test if the target's performance is at least better than the baseline by a certain margin"
  ]
}}
```

Our next example contains:
Tables: split_io_experiments, posthog_events_raw, user_activation_metrics
Columns: experiment_key, visitor_id, treatment_group, first_exposure_time, client_sdk_version, is_sticky_bucketing, feature_gate_name, traffic_type;
distinct_id, event_name, event_timestamp, current_url, screen_size, session_duration_sec, step_number, error_type, time_on_step, rage_clicks_count;
user_id, signup_date, invited_users_count, projects_created, files_uploaded, completion_percentage, days_to_activation, initial_setup_source, team_size_selected, industry_vertical

_Conversation History_
User: Can you help me find some insights in the data?
Agent: It sounds like you want me to search for insights in the data. I can do a better job when you provide more details about what you're looking for. Can you please be more specific?
User: Just show me what you can do

_Output_
```json
{{
  "variables": [
    "Experiment Exposure Rate (from treatment_group)",
    "Feature Adoption Rate (from feature_gate_name)",
    "User Frustration Level (from rage_clicks_count, error_type)",
    "Step Completion Time (from time_on_step)",
    "Project Creation Velocity (from projects_created)",
    "Setup Completion Rate (from completion_percentage)",
    "Activation Speed (from days_to_activation)"
  ],
  "metrics": [
    "Experiment Impact Score = Treatment vs Control Performance",
    "User Friction Index = f(Rage Clicks, Errors, Time on Step)",
    "Setup Success Rate = Completion % / Days to Activation",
    "Feature Impact = Activation Speed by Feature Exposure",
  ],
  "thought": "This dataset covers A/B testing, user events, and activation metrics. Clearly, we should dig into the results of the split tests.",
  "hypothesis": "A/B Testing Results",
  "plan": [
    "1. Segment Test Splits - create an filtered view for each split based on the experiment key",
    "2. Calculate Success Metrics - measure conversion lift and user activation for each split in the onboarding flow, adding new columns for each",
    "3. Perform Statistical Analysis - run significance tests on key metrics between control and treatment groups if possible",
    "4. Summarize Findings - compare the experimental splits to the control, and decide whether the new flow(s) should be rolled out"
    ]
}}
```

For our final example, suppose the data contains:
Tables: SalesforceAccountsSnapshot2024Q1, PolicyClaims, RiskEngineAssessment
Columns: sfdc_account, property_portfolio_value, annual_premium_usd, last_risk_assessment_date, account_owner_email, payment_status, client_segment_tier, renewal_date, primary_contact_title, last_interaction_date;
claim_id, account_id, claim_type, reported_date, settlement_amount_usd, days_to_resolution, adjuster_notes, property_address, deductible_applied, claim_status;
property_id, assessment_date, building_age_years, flood_zone_category, last_renovation_year, coverage_type_code, property_type, square_footage, construction_material, sprinkler_system_status;

_Output_
```json
{{
  "variables": [
    "Portfolio Risk Level (from property_portfolio_value, flood_zone_category)",
    "Claims Frequency Rate (aggregating claim_id by account)",
    "Property Risk Score (from building_age, flood_zone, construction_material)",
    "Premium to Portfolio Ratio (annual_premium_usd / property_portfolio_value)",
    "Claim Severity (from settlement_amount_usd)",
    "Property Modernization Index (from building_age_years, last_renovation_year)",
    "Geographic Risk Distribution (from property_address)"
  ],
  "metrics": [
    "Loss Ratio = Total Claims Paid / Total Premiums",
    "Risk-Adjusted Premium = Premium / Risk Score",
    "Claims Efficiency = Resolution Time / Settlement Amount",
    "Portfolio Risk Index = Aggregate Risk Scores / Portfolio Value",
  ],
  "thought": "This dataset covers customer accounts, claims history, and risk assessments. We could dig into the risk factors that drive churn.",
  "hypothesis": "Churn Analysis based on Risk Factors",
  "plan": [
    "1. Define Churn Events - identify churned accounts from salesforce_accounts_snapshot_2024q1 and policy status changes",
    "2. Aggregate Risk Factors - combine claims history, risk assessments, and account characteristics into a single view",
    "3. Model Churn Drivers - analyze correlation between customer attributes and churn probability, which helps discover at-risk accounts in need of intervention",
  ]
}}
```
#############
Now it's your turn! Based on the conversation history, please generate a hypothesis about the type of analysis most likely to be relevant according to the available tables and columns:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

revise_plan_prompt = """Based on the conversation history, we proposed performing {analysis_type} according to the following plan:
{previous_plan}

Given this plan, the user has provided some feedback which we should now incorporate to revise the plan.
In doing so, keep in mind the following hierarchy of concepts:
  * column - content that is directly grounded to the table, such that one is able to simply read the values off the spreadsheet
  * variable - a specific value or dimension of the data calculated by applying an aggregation function to a column or set of columns
  * metric - a formula that calculates a specific value based on a set of input variables, such as Return on Ad Spend or Conversion Rate
  * plan - a sequence of steps executed in a particular order to conduct data analysis, where each step is associated with a distinct metric or variable

Start by thinking carefully about the user's feedback, paying particular attention to what part of the original plan needs to be revised.
Then come up with a new plan to execute the analysis. As a reminder, typical steps include:
  * peeking at the data to get a sense of the available columns and their values, which informs the subsequent steps
  * filtering or removing data to focus on specific rows or columns that are relevant to the analysis
    - only include a filter step if the user has provided specific criteria to consider, otherwise we should assume the entire dataset is relevant
  * performing data transformations to create new rows or columns, great for storing intermediate results
  * staging the data to create new tables or views (ie. variables) that aggregate information across multiple columns
  * measuring a metric (eg. ARPU, CAC, CPM, CVR, DAU, MRR, ROAS, Retention Rate, Net Profit, etc.) based on available variables
  * basic control flow, namely branching or looping logic, based on the results of previous steps
  * computing statistics or forming comparisons based on the available variables, anything that involves a calculator or numpy/scipy
  * visualizing or summarizing the data to present final results in a clear and concise manner

Start by thinking carefully about the user's feedback, paying particular attention to what part of the original plan needs to be revised.
Your entire response should be in well-formatted JSON including keys for your thought (string) and revised plan (list), with no further text or explanations after the JSON output.

For example,
#############
Suppose the target analysis is 'LTV to CAC Ratio' and the following data is available:
Tables: stripe_subscriptions_raw; GAds_Spend_export; ai_usage_metrics_prod
Columns: subscription_id, customer_id, current_plan_name, mrr_cents, billing_cycle_anchor, trial_end_date, payment_failure_count, discount_code_applied, initial_plan_name, created_at_utc;
campaign_id, campaign_name, ad_group_id, date, cost_micros, clicks, impressions, utm_medium, utm_campaign, landing_page_path, device_type, conversion_tracking_id, bidding_strategy, quality_score, conversion_value_micros, final_url_suffix;
user_email, prompt_id, content_type, tokens_consumed, prompt_cost_usd, accepted_suggestion, generated_timestamp, workspace_id, model_version, prompt_template_name

_Conversation History_
User: Sure, last year sounds good. I'm not sure about the discount rate, maybe 10%?
Agent: Ok, I will use a discount rate of 10%. I'm thinking of using the Google Ads total spend for CAC. Are there any other costs I should consider?
User: No, that should be fine.
Agent: Based on your answers, I have come up with the following plan:
  1. Calculate Customer Acquisition Costs (CAC) - filter for YoY users within a month and aggregate marketing spend per customer from GAds_Spend_export
  2. Derive Revenue Metrics - aggregate the mrr_cents and ai_usage_metrics_prod to determine actual revenue per customer
  3. Calculate Customer Lifetime Value (LTV) - sum up all past revenue generated by the cohort, then apply a 10% discount rate to account for future value, extending out for three years
  4. Finalize LTV/CAC Ratio - compare customer LTV to total CAC to review whether our actions are sustainable, flagging any concerning trends
Is there anything you would like to change or add?
User: Actually, we don't need to look at the ai usage metrics, that seems out of scope.

_Output_
```json
{{
  "thought": "We can drop the step involving ai usage metrics from the plan. It actually seems redundant with the LTV calculations anyway.",
  "plan": [
    "1. Calculate Customer Acquisition Costs (CAC) - filter for YoY users within a month and aggregate marketing spend per customer from GAds_Spend_export",
    "2. Calculate Customer Lifetime Value (LTV) - sum up all past revenue generated by the cohort, then apply a 10% discount rate to account for future value, extending out for three years",
    "3. Finalize LTV/CAC Ratio - compare customer LTV to total CAC to review whether our actions are sustainable, flagging any concerning trends"
  ]
}}
```

In this next example, the target analysis is 'Multi-touch Attribution' and the following data is available:
Tables: facebook_ads_manager_daily, shopify_orders_202401, klayvio_customer_timeline
Columns: ad_id, campaign_name, adset_name, placement_type, daily_spend_cents, video_completion_rate, cta_button_type, audience_name, creative_format, instagram_eligible;
order_id, cart_token, landing_site_path, referring_site, discount_code_used, shipping_method, billing_postal_code, product_collection, total_price_cents, utm_parameters;
shopper_id, event_name, event_timestamp, device_fingerprint, last_utm_source, first_utm_source, session_number, cart_value_cents, products_viewed, time_to_purchase_sec

_Conversation History_
User: So our annual MBR is coming up and I need to figure out which mix of channels to double down on. How should we approach this?
Agent: It seems you want to perform Multi-touch Attribution. Before we get started, can you tell me what is the conversion event we are trying to measure? Also, which attribution model should we use?
User: The conversion event is when a customer makes a purchase. We should use both first-touch and last-touch attribution models.
Agent: Ok, based on your answers, I have come up with the following plan:
  1. Prepare Channel Data - peek at all source columns: campaign_name, adset_name, referring_site, utm_parameters, first_utm_source, and last_utm_source to understand how the channels are related and normalize their format if necessary
  2. Build User Journeys - combine the Facebook, Shopify, and Klayvio tables to create user journeys for each customer, keeping only the relevant columns
  3. Apply Attribution Models - calculate channel contribution to ROAS and purchases using first-touch and last-touch attribution models
  4. Compare Channel Performance - rank the performance across channels, accounting for assisted conversions; plot the results to present them in a clear and concise manner
How does this look to you?
User: Not really, but I just want to note that the Shopify data is the most important, so if there are any conflicts after joining with the other tables, we should prioritize the Shopify data.

_Output_
```json
{{
  "thought": "We should make a note to prioritize the Shopify data in case of conflicts.",
  "plan": [
    "1. Prepare Channel Data - peek at all source columns: campaign_name, adset_name, referring_site, utm_parameters, first_utm_source, and last_utm_source to understand how the channels are related and normalize their format if necessary",
    "2. Build User Journeys - combine all the tables to create user journeys for each customer, keeping only the relevant columns. In case of conflicts, prioritize the Shopify data over Facebook and Klayvio.",
    "3. Apply Attribution Models - calculate channel contribution to ROAS and purchases using first-touch and last-touch attribution models",
    "4. Compare Channel Performance - rank the performance across channels, accounting for assisted conversions; plot the results to present them in a clear and concise manner"
  ]
}}
```

For our third example, the target analysis is 'Customer Segmentation' and has the data:
Tables: Google Display q4, Mixpanel User Events, Segment CDP
Columns: placement_id, creative_asset_url, ad_group_name, device_category, geo_dma_code, avg_viewability_pct, slot_position, ad_size_px, raw_impressions;
device_id, session_id, ip_address, utm_source, landing_page_path, browser_language, os_version, time_on_site_sec, page_scroll_depth, exit_page_url;
twilio_id, user_id, email_domain, signup_source, job_title_raw, industry_raw, last_active_ts, total_sessions, has_downloaded_whitepaper

_Conversation History_
User: Engagement is defined by the number of impressions and conversions. We can segment by device type and ad group.
Agent: Great, one more question - how exactly do we define conversions?
User: It's when anyone downloads a whitepaper
Agent: Ok, based on your answers, I have come up with the following plan:
  1. Define User Segment - peek at the device_id and ad_group_name columns to understand how to segment the users
  2. Stage Engagement Variables - create tables that aggregate the number of page views, impressions, clicks, and conversions for each segment
  3. Calculate Engagement Metrics - calculate view-through rate and whitepaper download rate for each segment
  4. Summarize Segment Performance - identify top performing segments and present the results in a clear and concise manner
Is there anything you would like to modify?
User: We don't need to worry about page views or clicks as engagement variables.

_Output_
```json
{{
  "thought": "We can drop the page views and clicks from the second step of the plan.",
  "plan": [
    "1. Define User Segment - peek at the device_id and ad_group_name columns to understand how to segment the users",
    "2. Stage Engagement Variables - create tables that aggregate the number of impressions and conversions for each segment",
    "3. Calculate Engagement Metrics - calculate view-through rate and whitepaper download rate for each segment",
    "4. Summarize Segment Performance - identify top performing segments and present the results in a clear and concise manner"
  ]
}}
```

For our next example, the target analysis is 'Significance Testing' and has the data:
Tables: constant_contact_campaigns, amplitude_analytics, Conferences List (Updated Jan 2025)
Columns: campaign_id, campaign_name, template_variant, sender_email_alias, target_segment_id, subject_line, preview_text, utm_campaign_code, scheduled_datetime, actual_send_datetime;
amp_id, campaign_id, delivery_timestamp, recipient_email_hash, device_type, email_client, bounce_type, open_count, click_count, unsubscribe_flag;
conference_id, conference_name, organizer_entity, venue_name, city_location, state_province, expected_attendance, booth_number, booth_size_sqft, sponsorship_tier

_Conversation History_
User: We ran a new email campaign called 'Knock Your Socks Off' and I want to see how it performed compared to our usual campaigns. How should we approach this?
Agent: It seems you want to perform Significance Testing. Before we get started, can you tell me what are the key metrics we should compare? Also, how far back should we look?
User: We can focus on open rate, CTR, and unsubscribe rate. Let's say going back three months.
Agent: No problem. Based on your answers, I have come up with the following plan:
  1. Data Assessment - look for anomalies in delivery_timestamp to ensure we have the date range we need, check that unsubscribe_flag is not set to True for all records
  2. Establish Baseline Metrics - calculate open rate, click-through rate, and unsubscribe rate, focused on the last three months and grouped by campaign
  3. Target Cohort Performance - filter to just the performance of the Knock Your Socks Off campaign
  4. Compare Target to Baseline - test if the target's performance is at least 5% better than the baseline on an relative basis
What do you think? Do you approve?
User: I think we should use a significance test instead of just comparing the numbers.
  
_Output_
```json
{{
  "thought": "We should use a significance test instead of just comparing the numbers. Calculating a p-value is a good way to do this.",
  "plan": [
    "1. Data Assessment - look for anomalies in delivery_timestamp to ensure we have the date range we need, check that unsubscribe_flag is not set to True for all records",
    "2. Establish Baseline Metrics - calculate open rate, click-through rate, and unsubscribe rate, focused on the last three months and grouped by campaign",
    "3. Target Cohort Performance - filter to just the performance of the Knock Your Socks Off campaign",
    "4. Compare Target to Baseline - perform a significance test by determining the p-value for each of the three key metrics to see their variation from the baseline"
  ]
}}
```

Moving on, the target analysis this time is 'A/B Testing Results' and has the data:
Tables: split_io_experiments, posthog_events_raw, user_activation_metrics
Columns: experiment_key, visitor_id, treatment_group, first_exposure_time, client_sdk_version, is_sticky_bucketing, feature_gate_name, traffic_type;
distinct_id, event_name, event_timestamp, current_url, screen_size, session_duration_sec, step_number, error_type, time_on_step, rage_clicks_count;
user_id, signup_date, invited_users_count, projects_created, files_uploaded, completion_percentage, days_to_activation, initial_setup_source, team_size_selected, industry_vertical

_Conversation History_
User: Let's compare the conversion rate and user activation for each split.
Agent: And how do we identify which users are assigned to each split?  Perhaps the treatment_group column?
User: Oh right. Actually, use the experiment key column.
Agent: Great, based on your answers, I have come up with the following plan:
  1. Segment Test Splits - create an filtered view for each split based on the experiment key
  2. Calculate Success Metrics - measure conversion lift and user activation for each split in the onboarding flow, adding new columns for each
  3. Perform Statistical Analysis - run significance tests on key metrics between control and treatment groups if possible
  4. Summarize Findings - compare the experimental splits to the control, and decide whether the new flow(s) should be rolled out
Are there any changes you would like to make?
User: We don't need to run a statistical tests, that's too much work.

_Output_
```json
{{
  "thought": "We can drop the third step from the plan involving statistical tests.",
  "plan": [
    "1. Segment Test Splits - create an filtered view for each split based on the experiment key",
    "2. Calculate Success Metrics - measure conversion lift and user activation for each split in the onboarding flow, adding new columns for each",
    "3. Summarize Findings - compare the experimental splits to the control, and decide whether the new flow(s) should be rolled out"
  ]
}}
```

For our final example, the target analysis is 'Churn Analysis' and has the data:
Tables: SalesforceAccountsSnapshot2024Q1, PolicyClaims, RiskEngineAssessment
Columns: sfdc_account, property_portfolio_value, annual_premium_usd, last_risk_assessment_date, account_owner_email, payment_status, client_segment_tier, renewal_date, primary_contact_title, last_interaction_date;
claim_id, account_id, claim_type, reported_date, settlement_amount_usd, days_to_resolution, adjuster_notes, property_address, deductible_applied, claim_status;
property_id, assessment_date, building_age_years, flood_zone_category, last_renovation_year, coverage_type_code, property_type, square_footage, construction_material, sprinkler_system_status;

_Conversation History_
User: I'm more focused on risk of churn, rather than retention. Basically, a user has churned if they have cancelled their policy.
Agent: Got it. Can you tell me what potential churn factors should we investigate? Also, how should we determine whether an account is big?
User: Just look at all accounts. Use the data in the risk engine assessment table to figure out the churn factors.
Agent: Ok, based on your answers, I have come up with the following plan:
  1. Define Churn Accounts - identify churned accounts from the Salesforce table to by looking at the policy status (renewal_date, payment_status)
  2. Aggregate Risk Factors - combine claims history (claim_status, settlement_amount_usd, claim_type), risk assessments (building_age_years, flood_zone_category, property_type), and account characteristics into a single view
  3. Model Churn Drivers - analyze correlation between risk attributes and churn probability to help discover at-risk accounts in need of intervention
Do you approve of this plan? Anything you would change?
User: We need to make sure that the property address (specifically the zip code) is included in the account characteristics.

_Output_
```json
{{
  "thought": "The user wants to include the property address in the account characteristics. Specifically, we should extract the zip code from the address.",
  "plan": [
    "1. Define Churn Accounts - identify churned accounts from the Salesforce table to by looking at the policy status (renewal_date, payment_status)",
    "2. Extract Zip Code - extract the zip code from the property address column in the Salesforce table",
    "3. Aggregate Risk Factors - combine claims history (claim_status, settlement_amount_usd, claim_type), risk assessments (building_age_years, flood_zone_category, property_type), and account characteristics (zip code) into a single view",
    "4. Model Churn Drivers - analyze correlation between risk attributes and churn probability to help discover at-risk accounts in need of intervention"
  ]
}}
```
#############
Now it's your turn! Please think carefully about the user's feedback in the final utterance, and update the plan accordingly. For context, the available data includes:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

revise_hypothesis_prompt = """Based on the available data, we have attempted to perform {analysis_type} according to the following plan:
{previous_plan}

Given this plan, the user has provided some feedback which we should now incorporate to revise the plan.
In doing so, keep in mind the following hierarchy of concepts:
  * column - content that is directly grounded to the table, such that one is able to simply read the values off the spreadsheet
  * variable - a specific value or dimension of the data calculated by applying an aggregation or operation to one or more columns
  * metric - a formula that calculates a specific value by establishing a relationship between one or more variables
  * plan - a sequence of steps executed in a particular order to conduct data analysis, where each step is associated with a distinct metric or variable

Start by thinking carefully about the user's feedback, paying particular attention to what part of the original plan needs to be revised.
Then come up with a new hypothesis of the analysis type as well as a series of steps to execute the analysis. As a reminder, typical steps include:
  * peeking at the data to get a sense of the available columns and their values, which informs the subsequent steps
  * filtering or removing data to focus on specific rows or columns that are relevant to the analysis
  * performing data transformations to create new rows or columns, great for storing intermediate results
  * staging the data to create new tables or views (ie. variables) that aggregate information across multiple columns
  * calculating a metric (eg. ARPU, CAC, CPM, CVR, DAU, MRR, ROAS, Retention Rate, Net Profit, etc.) based on available variables
  * basic control flow, namely branching or looping logic, based on the results of previous steps
  * visualizing or summarizing the data to present final results in a clear and concise manner

Your entire response should be in well-formatted JSON including keys for thought (string), hypothesis (short phrase), and revised plan (list), with no further text or explanations after the JSON output.

For example,
#############
Suppose the available data includes:
Tables: stripe_subscriptions_raw; GAds_Spend_export; ai_usage_metrics_prod
Columns: subscription_id, customer_id, current_plan_name, mrr_cents, billing_cycle_anchor, trial_end_date, payment_failure_count, discount_code_applied, initial_plan_name, created_at_utc;
campaign_id, campaign_name, ad_group_id, date, cost_micros, clicks, impressions, utm_medium, utm_campaign, landing_page_path, device_type, conversion_tracking_id, bidding_strategy, quality_score, conversion_value_micros, final_url_suffix;
user_email, prompt_id, content_type, tokens_consumed, prompt_cost_usd, accepted_suggestion, generated_timestamp, workspace_id, model_version, prompt_template_name

_Conversation History_
User: Just show me what you can do
Agent: I propose to perform a Course Segmentation analysis. My plan is as follows:
  1. Acquisition Cost Analysis - Calculate CAC by channel, Measure campaign efficiency, Map acquisition costs to customer segments
  2. Revenue Analysis - Calculate customer LTV, Measure trial conversion impact, Analyze revenue stability
  3. AI Usage Impact - Measure AI costs per customer, Calculate usage profitability, Identify optimal usage patterns
  4. ROI Calculation - Compare LTV:CAC ratios, Calculate payback periods, Measure campaign ROI
  5. Optimization Strategy - Generate channel mix recommendations, Identify profitable customer segments, Create usage optimization guidelines
How does this sound?
User: I don't want to look at the success rate. Can you just look at the average feedback rating for each course?

_Output_
```json
{{
  "thought": "The user changed their mind and no longer wants to look at the success rate. Therefore, we should remove the last step from the plan.",
  "hypothesis": "LTV to CAC Ratio",
  "plan": [
    "1. Calculate Customer Acquisition Costs (CAC) - filter for YoY users within a month and aggregate marketing spend per customer from GAds_Spend_export",
    "2. Calculate Customer Lifetime Value (LTV) - sum up all past revenue generated by the cohort, then apply a 10% discount rate to account for future value, extending out for three years",
    "3. Finalize LTV/CAC Ratio - compare customer LTV and total CAC to review whether our actions are sustainable, flag any concerning trends"
  ]
}}
```

Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach
Columns: CourseID, CourseTitle, InstructorID, CourseDescription, StartDate, EndDate, Duration, CourseFormat, Category, EnrollmentCount in BB_courses;
EnrollmentID, CourseID, StudentID, EnrollmentDate, CompletionStatus, Feedback, CertificateLink, PaymentStatus, ReferralSource in BB_enrollments;
TestimonialID, StudentID, CourseID, TestimonialText, DateProvided, Rating, Featured, ApprovalStatus, PhotoLink in Testimonials;
OutreachID, CampaignName, TargetAudience, Platform, ResponseRate, Collaborators in CanvasOutreach
Tables: Google Display q4, Mixpanel User Events, Segment CDP
Columns: placement_id, creative_asset_url, ad_group_name, device_category, geo_dma_code, avg_viewability_pct, slot_position, ad_size_px, raw_impressions;
device_id, session_id, ip_address, utm_source, landing_page_path, browser_language, os_version, time_on_site_sec, page_scroll_depth, exit_page_url;
twilio_id, user_id, email_domain, signup_source, job_title_raw, industry_raw, last_active_ts, total_sessions, has_downloaded_whitepaper

_Conversation History_
  1. Channel Mapping - Map all touchpoints across Google Ads, UTM sources, Associate impressions with session data, Create unified user journey timelines
  2. Engagement Scoring - Calculate engagement metrics per session, Weight engagement based on depth indicators, Create composite engagement scores
  3. Attribution Modeling - Apply attribution models (first-touch, last-touch, linear, time-decay), Calculate channel, Contribution scores, Identify key conversion paths
  4. Industry Analysis - Segment attribution patterns by industry, Analyze industry-specific conversion paths, Identify high-value industry segments
  5. Optimization Framework - Generate channel mix recommendations, Create targeting recommendations by industry, Develop creative optimization suggestions

_Output_
```json
{{
  "thought": "The user changed their mind and no longer wants to look at the success rate. Therefore, we should remove the last step from the plan.",
  "hypothesis": "Course Segmentation",
  "plan": [
    "1. Define User Segment - peek at the device_id and ad_group_name columns to understand how to segment the users",
    "2. Stage Engagement Variables - create tables that aggregate the number of impressions and conversions for each segment",
    "3. Calculate Engagement Metrics - calculate view-through rate and whitepaper download rate for each segment",
    "4. Summarize Segment Performance - identify top performing segments and present the results in a clear and concise manner"
  ]
}}
```

For our third example, suppose the available data is:
Tables: facebook_ads_manager_daily, shopify_orders_202401, klayvio_customer_timeline
Columns: ad_id, campaign_name, adset_name, placement_type, daily_spend_cents, video_completion_rate, cta_button_type, audience_name, creative_format, instagram_eligible;
order_id, cart_token, landing_site_path, referring_site, discount_code_used, shipping_method, billing_postal_code, product_collection, total_price_cents, utm_parameters;
shopper_id, event_name, event_timestamp, device_fingerprint, last_utm_source, first_utm_source, session_number, cart_value_cents, products_viewed, time_to_purchase_sec

_Conversation History_
  1. Funnel Mapping - Map conversion paths from ad exposure to purchase, Calculate drop-off rates at each stage, Identify critical conversion points
  2. Creative Performance Analysis - Analyze conversion rates by creative format, Measure video completion impact on conversion, Assess CTA effectiveness by placement
  3. Cart Analysis - Calculate cart abandonment patterns, Analyze discount code impact, Measure product collection influence
  4. Audience Optimization - Analyze performance by audience segment, Map high-value customer journeys, Identify optimal targeting parameters
  5. Channel Efficiency - Calculate ROAS by channel and campaign, Measure cross-platform effectiveness, Generate spend optimization recommendations

_Output_
```json
{{
  "thought": "The user changed their mind and no longer wants to look at the success rate. Therefore, we should remove the last step from the plan.",
  "hypothesis": "Multi-touch Attribution",
  "plan": [
    "1. Prepare Channel Data - peek at all source columns: campaign_name, adset_name, referring_site, utm_parameters, first_utm_source, and last_utm_source to understand how the channels are related and normalize their format if necessary",
    "2. Build User Journeys - combine the Facebook, Shopify, and Klayvio tables to create user journeys for each customer, keeping only the relevant columns",
    "3. Apply Attribution Models - calculate channel contribution to ROAS and purchases using first-touch and last-touch attribution models",
    "4. Compare Channel Performance - rank the performance across channels, accounting for assisted conversions; plot the results to present them in a clear and concise manner"
  ]
}}
```

Suppose the available data includes:
Tables: constant_contact_campaigns, amplitude_analytics, Conferences List (Updated Jan 2025)
Columns: campaign_id, campaign_name, template_variant, sender_email_alias, target_segment_id, subject_line, preview_text, utm_campaign_code, scheduled_datetime, actual_send_datetime;
amp_id, campaign_id, delivery_timestamp, recipient_email_hash, device_type, email_client, bounce_type, open_count, click_count, unsubscribe_flag;
conference_id, conference_name, organizer_entity, venue_name, city_location, state_province, expected_attendance, booth_number, booth_size_sqft, sponsorship_tier

_Conversation History_
  1. Data Assessment - check the date range, look for any anomalies in delivery_timestamp or large gaps in the data to account of confounding factors
  2. Establish Baseline Metrics - calculate delivery rate, open rate, click-through rate, unsubscribe rate, focused on the approximate time period and grouped by campaign
  3. Target Cohort Performance - filter to just the performance of the recent email campaign
  4. Compare Target to Baseline - perform a basic t-test by determining the p-value for each of the key metrics

_Output_
```json
{{
  "thought": "The user changed their mind and no longer wants to look at the success rate. Therefore, we should remove the last step from the plan.",
  "hypothesis": "Significance Testing",
  "plan": [
    "1. Data Assessment - look for anomalies in delivery_timestamp to ensure we have the date range we need, check that unsubscribe_flag is not set to True for all records",
    "2. Establish Baseline Metrics - calculate open rate, click-through rate, and unsubscribe rate, focused on the last three months and grouped by campaign",
    "3. Target Cohort Performance - filter to just the performance of the Knock Your Socks Off campaign",
    "4. Compare Target to Baseline - test if the target's performance is at least 5% better than the baseline on an absolute basis on all three metrics"
  ]
}}
```

In our next example, suppose the data includes:
Tables: split_io_experiments, posthog_events_raw, user_activation_metrics
Columns: experiment_key, visitor_id, treatment_group, first_exposure_time, client_sdk_version, is_sticky_bucketing, feature_gate_name, traffic_type;
distinct_id, event_name, event_timestamp, current_url, screen_size, session_duration_sec, step_number, error_type, time_on_step, rage_clicks_count;
user_id, signup_date, invited_users_count, projects_created, files_uploaded, completion_percentage, days_to_activation, initial_setup_source, team_size_selected, industry_vertical

_Conversation History_
User: We've been running this new onboarding flow for about a month now. Let's jump to the split.io data to take a look.
Agent: Based on your answers, I have come up with the following plan:
  1. Experiment Mapping - Identify all active experiments, Map user exposure across features, Calculate baseline metrics per group
  2. User Journey Analysis - Track progression through setup steps, Measure friction points, Calculate drop-off rates
  3. Feature Impact Assessment - Compare activation rates across variants, Measure feature adoption impact, Calculate statistical significance
  4. Segment Performance - Analyze results by industry vertical, Map team size impact, Identify high-performing segments
  5. Optimization Recommendations - Generate feature recommendations, Identify friction reduction opportunities, Create segment-specific strategies
Agent: Do you approve of this plan? Anything you would change?
User: We don't need to run a statistical tests, that's too much work.

_Output_
```json
{{
  "thought": "The user changed their mind and no longer wants to look at the success rate. Therefore, we should remove the last step from the plan.",
  "hypothesis": "A/B Testing Results",
  "plan": [
    "1. Experiment Mapping - Identify all active experiments, Map user exposure across features, Calculate baseline metrics per group",
    "2. User Journey Analysis - Track progression through setup steps, Measure friction points, Calculate drop-off rates",
    "3. Feature Impact Assessment - Compare activation rates across variants, Measure feature adoption impact, Calculate statistical significance",
    "4. Segment Performance - Analyze results by industry vertical, Map team size impact, Identify high-performing segments",
    "5. Optimization Recommendations - Generate feature recommendations, Identify friction reduction opportunities, Create segment-specific strategies"
  ]
}}
```

For our final example, suppose the data includes:
Tables: SalesforceAccountsSnapshot2024Q1, PolicyClaims, RiskEngineAssessment
Columns: sfdc_account, property_portfolio_value, annual_premium_usd, last_risk_assessment_date, account_owner_email, payment_status, client_segment_tier, renewal_date, primary_contact_title, last_interaction_date;
claim_id, account_id, claim_type, reported_date, settlement_amount_usd, days_to_resolution, adjuster_notes, property_address, deductible_applied, claim_status;
property_id, assessment_date, building_age_years, flood_zone_category, last_renovation_year, coverage_type_code, property_type, square_footage, construction_material, sprinkler_system_status;

_Conversation History_
User: I've noticed we're losing some big accounts lately and I'd like to get ahead of this. Can we find out the drivers that are causing these customers to leave?
Agent: Based on your answers, I have come up with the following plan:
  1. Define Churn Events - identify churned accounts from salesforce_accounts_snapshot_2024q1 and policy status changes
  2. Aggregate Risk Factors - combine claims history, risk assessments, and account characteristics into a single view
  3. Model Churn Drivers - analyze correlation between customer attributes and churn probability, which helps discover at-risk accounts in need of intervention
Do you approve of this plan? Anything you would change?
User: We need to make sure that the property address (specifically the zip code) is included in the account characteristics.

_Output_
```json
{{
  "thought": "The user changed their mind and no longer wants to look at the success rate. Therefore, we should remove the last step from the plan.",
  "hypothesis": "Churn Analysis based on Risk Factors",
  "plan": [
    "1. Define Churn Events - identify churned accounts from salesforce_accounts_snapshot_2024q1 and policy status changes",
    "2. Aggregate Risk Factors - combine claims history, risk assessments, and account characteristics into a single view",
    "3. Model Churn Drivers - analyze correlation between customer attributes and churn probability, which helps discover at-risk accounts in need of intervention",
  ]
}}
```
#############
Now it's your turn! Based on the conversation history, please update the plan according to the user's feedback. For context, the available data includes:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

adjust_plan_prompt = """Based on the conversation history, we planned to perform {analysis_type} according to the following plan:
{previous_plan}

We have just completed {iteration} of the plan.
Given this progress, we want to take a moment to reflect on the results and adjust the plan if necessary.
Start by thinking carefully about the results obtained so far and whether any prior assumptions need to be revisited.

If an adjustment is needed, please generate the revised plan choosing only from the following list of allowed steps:
  * peek: look at the data to get a sense of the available columns and their values
  * insert: add a single new row or column by extracting or mapping values from an existing column, very useful for staging intermediate results
  * query: apply filtering, aggregation, or sorting to focus on specific subset of the data
  * pivot: create a staging table that groups the data across some dimension for future reference
    - whereas 'query' is meant for reducing scope and complexity, 'pivot' is preferred for drilling down into more detail
  * measure: calculate a business metric or KPI that is composed of variables created in previous steps. Common examples include:
    - Marketing Efficiency: Return on Ad Spend (ROAS), Click-Through Rate (CTR), Cost Per Click (CPC), Cart Abandonment Rate, Cost per Mille (CPM)
    - User Engagement: Bounce Rate, Daily Active Users (DAU), Monthly Active Users (MAU), Device Statistics, Net Promoter Score (NPS), Churn Rate, Retention Rate (Retain)
    - Conversion: Conversion Rate (CVR), Signup Rate, Purchase Frequency (Frequency),  Email Open Rate (Open), Customer Acquisition Cost (CAC)
    - Revenue: Average Order Value (AOV), Average Revenue Per User (ARPU), Customer Lifetime Value (LTV), Monthly Recurring Revenue (MRR), Net Profit (Profit)
  * segment: categorize a metric into multiple buckets based on some dimension
  * compute: perform calculations based on existing values such as correlation, averaging, regression, or other data science tasks using numpy or scipy
  * plot: create a visualization of the data, such as a bar chart or scatter plot, and summarize the results
  * exit: the spreadsheet must be processed or cleaned before we can proceed with the analysis, so we exit the analysis

Some acceptable reasons to adjust the plan include:
  * It is not possible to complete the plan due to inability to join the tables together. Need to early exit.
  * The user has added new requirements that were not part of the original plan.
  * Results from the previous steps show that we can simplify the plan by skipping certain steps since the results are already clear or redundant.

In contrast, some scenarios where no adjustment is needed (even though they may seem problematic):
  * Encountering null values or missing values are fine as long as they doesn't break the analysis
  * Outliers are fine. In fact, we *like* outliers because these often hint at potential areas for insights
  * Repeated values are not an issue since we can apply a 'Group By' operation with a future step (ie. query, pivot, segment, etc.)

For most cases, no adjustment is needed, so you can just set 'needs_adjustment' to false and output an empty list for the plan.
Otherwise set 'needs_adjustment' to true and outline the remaining steps that need to be implemented. Notably, we can skip steps that have already been completed.
An ideal plan should be as short as possible by avoiding redundant steps:
  * Obviously, a 'query' step should not be right next to another 'query' step.
  * Do NOT include a 'query' step right before or after a 'plot' step. 'query' is unnecessary because 'plot' already implies a summary.
  * A 'query' step should not follow a 'peek' step either. At the beginning of a plan, 'query' behaves as a 'peek' with additional grouping and ordering. Pick one or the other.
  * A 'measure' step should not preceed a 'segment' step. The 'segment' step already encompasses the metric calculation.

Your entire response should be in well-formatted JSON including keys for your thought (string), needs_adjustment (boolean), and revised plan (list).
Recall that each step in the plan is a dict with a name key (token), description (string), and optional keys for variables (list of strings) or metrics (list of dicts).
Each metric dict is a consists of an acronym (short key) and a full name (long key) that follows industry standards.
There should be no further text or explanations after the JSON output.

---
Suppose the original plan for 'LTV to CAC Ratio' was:
1. insert (complete) - create a cohort of one month that filters for users who signed up a year ago. Variables: total_ad_cost, recurring_revenue, conversion_value
2. measure (complete) - calculate the customer acquisition cost based on total ad cost. Metrics: CAC
3. measure - calculate lifetime value by summing up all past revenue, and then apply a 10% discount rate to account for future value, extending out for three years. Metrics: LTV
4. query - compare the customer LTV to CAC to review whether our actions are sustainable, flag any concerning trends

_Conversation History_
User: Great, this looks good.
Agent: Here is the cohort of users from last year, along with associated variables for ad cost, recurring revenue, and conversion value. Should I continue?
User: Yup, keep going.
Agent: You got it, I have calculated the customer acquisition cost based on total ad cost. See table for details. Should I continue?
User: Yea, sure

_Output_
```json
{{
  "thought": "The results so far seem fine, so no adjustment to the plan is needed.",
  "needs_adjustment": false,
  "plan": [ ]
}}
```

In this next example, suppose the original plan for 'Customer Segmentation' was:
1. peek (complete) - look at the device_id and ad_group_name columns to understand how to segment the users
2. pivot - create tables that aggregate the number of impressions and conversions for each segment. Variables: impressions, conversions
3. measure - calculate view-through rate and whitepaper download rate for each segment. Metrics: engagement_rate
4. query - identify top performing segments and present the results in a clear and concise manner

_Conversation History_
User: It's when anyone downloads a whitepaper
Agent: Ok, based on your answers, I have come up with the following plan:
  1. Define User Segment - peek at the device_id and ad_group_name columns to understand how to segment the users
  2. Stage Engagement Variables - create pivot tables that aggregate the number of impressions and conversions for each segment
  3. Calculate Engagement Metrics - calculate view-through rate and whitepaper download rate for each segment
  4. Summarize Segment Performance - identify top performing segments and present the results in a clear and concise manner
Is there anything you would like to modify?
User: Nope, it looks good to me.
Agent: After taking a look at some of the columns, it seems we don't have a clear way to join the tables together. Is there something I'm missing?
User: We can join these together based on date.

_Output_
```json
{{
  "thought": "After peeking at the data, there isn't a clear primary key column to join the tables together. We will have to perform data preparation first based on date related columns.",
  "needs_adjustment": true,
  "plan": [
    {{"name": "exit", "description": "There are no primary key columns to join the tables together, so we need to create a new joint table based on date before we can proceed."}}
  ]
}}
```

For our third example, the original plan for 'Conversion Funnel Optimization' was:
1. peek (complete) - consider campaign_name, adset_name, referring_site, utm_parameters, event_type, and user_action columns to understand which columns is appropriate for modeling the customer journey
2. query - filter the data to include only users who have at least one user action related to visiting the website within the last 30 days. Variables: website_visit
3. measure - calculate the Conversion Rate (CVR) from website visits to the number of users with adding a product to their cart. Metrics: AddToCart Action
4. measure - calculate the percentage of users who added their credit card information to those who added a product to their cart. Metrics: CreditCard Action
5. measure - calculate the percentage of users who completed the purchase to those who added their credit card information. Metrics: Checkout Action
6. plot - aggregate results to rank the performance of each metric across campaigns and show the results in a graph

_Conversation History_
User: The last action goes from adding a credit card information to actually checking out.
Agent: No problem, here is the updated plan:
  1. Identify User Actions - peek at all source columns: campaign_name, adset_name, referring_site, utm_parameters, event_type, and user_action to understand which columns are appropriate for modeling the customer journey
  2. Filter Relevant Users - query the data to include only users who have at least one user action related to visiting the website within the last month
  3. Build User Journeys - combine all the tables to create user journeys from (1) website visit to (2) add to cart to (3) credit card info to (4) checkout, which will be four steps of the funnel
  4. Compare Campaign Performance - analyze the performance for each campaign by looking at the drop-off rates at each stage; plot the results to present them in a clear and concise manner
What do you think now?
User: That looks good.
Agent: After peeking at the data, it seems the event_type already contains actions for viewed_product, add_to_cart, credit_card_success (or credit_card_fail), and checkout. So all the relevant actions are directly captured. Does that sound right?
User: Sure, that sounds right.

_Output_
```json
{{
  "thought": "The customer journey modeling is not as complex as expected. We can directly stage the relevant metrics in a single pivot table rather than building computing them individually.",
  "needs_adjustment": true,
  "plan": [
    {{
      "name": "pivot", "description": "create user journeys for Website Visit, Add to Cart, Credit Card Success, and Checkout, by counting instances of each user action in the event_type column",
      "variables": ["website_visits", "add_to_cart_actions", "credit_card_actions", "checkout_actions"]
    }},
    {{"name": "plot", "description": "aggregate results to rank the performance across campaigns and show the results in a graph"}}
  ]
}}
```

In this next example, the original plan 'Significance Testing' was:
1. peek (complete) - look for anomalies in delivery_timestamp to ensure we have the date range we need, check that unsubscribe_flag is not set to True for all records
2. pivot (complete) - calculate delivery rate, open rate, click-thru rate, and unsubscribe rate, focused on the last year and grouped by campaign. Variables: delivery_rate, open_rate, click_thru_rate, unsubscribe_rate
3. insert - add rows for the mean, standard deviation, and number of campaigns for each metric. Variables: mean, std_dev, num_campaigns
4. insert - add a row to the table that filters for just the performance of the Knock Your Socks Off campaign
5. compute - calculate the p-values for each of the four key metrics by comparing the target cohort to the statistics described above

_Conversation History_
User: Yea, this is fine.
Agent: I have looked in the data for anomalies or concerns in the data, but did not find any issues. Should I continue?
User: Yup go ahead.
Agent: I have calculated the baseline metrics for the last three months in the new 'email_metrics' table. Should I continue?
User: Ok, this is good, but we should exclude any campaigns that include the 'test' keyword in the name.

_Output_
```json
{{
  "thought": "We need to re-stage the table again, but this time exclude any campaigns that include the 'test' keyword in the name.",
  "needs_adjustment": true,
  "plan": [
    {{
      "name": "pivot", "description": "calculate delivery rate, open rate, click-thru rate, and unsubscribe rate, focused on the last year and grouped by campaign, while filtering out any campaigns with the 'test' keyword in the name",
      "variables": ["delivery_rate", "open_rate", "click_thru_rate", "unsubscribe_rate"]
    }},
    {{
      "name": "insert", "description": "add rows for the mean, standard deviation, and number of campaigns for each metric",
      "variables": ["mean", "std_dev", "num_campaigns"]
    }},
    {{"name": "insert", "description": "add a row to the table that filters for just the performance of the Knock Your Socks Off campaign"}},
    {{"name": "compute", "description": "calculate the p-values for each of the four key metrics by comparing the target cohort to the statistics described above"}}
  ]
}}
```

For our next example, the original plan for 'Churn Analysis' was:
1. pivot (complete) - identify churned accounts from the Salesforce table to by looking at the policy status (renewal_date, payment_status). Variables: policy_status
2. insert (complete) - combine claims history (claim_status, settlement_amount_usd, claim_type), risk assessments (building_age_years, flood_zone_category, property_type), and account characteristics into a single view. Variables: claims_history, risk_assessments, account_characteristics
3. compute (complete) - analyze correlation between risk attributes and churned accounts to determine probability of churn. Metrics: churn_probability
4. query - rank the policies by churn probability to help discover at-risk accounts in need of intervention

_Conversation History_
User: Yes, keep going.
Agent: I will not calculate the churn probability. Given that large number of metrics, are there particular risk factors we should focus on?
User: So like I mentioned earlier, zip code is important. I think claim type, flood zone, building age and property type are the others.
Agent: I have analyzed the correlation between risk attributes and churned accounts to determine probability of churn. See table for details. Should I continue?
User: Looks good to me.

_Output_
```json
{{
  "thought": "The recent results don't really challenge our assumptions, so we don't need to adjust anything. We can just finalize the results.",
  "needs_adjustment": false,
  "plan": [ ]
}}
```

In our last example, the original plan for 'A/B Testing Results' was:
1. measure (complete) - measure conversion lift for each split in the onboarding flow. Metrics: conversion lift
2. insert (complete) - store the results, making sure to have new columns for each control and treatment group
3. measure (complete)- measure user activation for each split in the onboarding flow. Metrics: user activation
4. insert - store the results, making sure to have new columns for each control and treatment group
5. compute - compare the experimental splits to the control, and decide whether the any of the new flows should be rolled out

_Conversation History_
User: yep, go ahead
Agent: Just to confirm, which column should we use to measure user activation?
User: If the time to activation is not empty, then that means the user was activated
Agent: I have calculated the user activation based on the days_to_activation for each of the three splits. How does this look? Should I continue?
User: Actually, I think we should also measure rage clicks as well. I want to make sure there's nothing weird going on there.

_Output_
```json
{{
  "thought": "The user wants to add the count of rage clicks as another success metric, so we should add an extra 'measure' step and 'insert' step, after saving the current results.",
  "needs_adjustment": true,
  "plan": [
    {{"name": "insert", "description": "store the results for user activation before starting on the next metric"}},
    {{
      "name": "measure", "description": "measure count of rage clicks for each split in the onboarding flow",
      "metrics": [{{"short": "Rage", "long": "rage clicks"}}]
    }},
    {{"name": "insert", "description": "store the results, making sure to have new columns for each control and treatment group"}},
    {{"name": "compute", "description": "compare the experimental splits to the control, and decide whether the any of the new flows should be rolled out"}}
    ]
}}
```
---
For our real case, we are performing {analysis_type} according to the original plan:
{previous_plan}

Our results so far are:
{past_results}

_Conversation History_
{history}

Based on all this information, please think carefully about whether the plan needs adjustment, and if so, devise a new one.

_Output_
"""

convert_to_flow_prompt = """As seen in the conversation history, the user is attempting to perform {analysis_type}.
While a proposed framework has been approved, it needs to be converted from natural language into a structured sequence of predefined DSL operations.
Your task is to develop an actionable plan where each step corresponds to a single valid operation.
The output steps do NOT need to map one-to-one with the elements in the original framework, and in fact will often be shorter since multiple input elements may be combined into a single operation.
Concretely, the approved set of valid operations are:
  * peek: look at the data to get a sense of the available columns and their values, particularly to grasp their format and distribution
  * insert: add a new row or column by extracting or mapping values from an existing column, very useful for staging intermediate results
  * query: materialize a temporary view by filtering, aggregating, or sorting to focus on a specific subset of the data
  * pivot: create a permanent table that not only aggregates values across multiple columns, but also groups them across some dimension, useful for staging variables
    - also used to join multiple tables together to focus on a specific subset of columns for future analysis
    - whereas 'query' is meant for reducing scope and complexity, 'pivot' is preferred for drilling down into more detail
  * measure: calculate a business metric or KPI that is composed of variables created in previous steps. Common examples include:
    - Marketing Efficiency: Return on Ad Spend (ROAS), Click-Through Rate (CTR), Cost Per Click (CPC), Cart Abandonment Rate, Cost per Mille (CPM)
    - User Engagement: Bounce Rate, Daily Active Users (DAU), Monthly Active Users (MAU), Device Statistics, Net Promoter Score (NPS), Churn Rate, Retention Rate (Retain)
    - Conversion: Conversion Rate (CVR), Signup Rate, Purchase Frequency (Frequency),  Email Open Rate (Open), Customer Acquisition Cost (CAC)
    - Revenue: Average Order Value (AOV), Average Revenue Per User (ARPU), Customer Lifetime Value (LTV), Monthly Recurring Revenue (MRR), Net Profit (Profit)
  * segment: breakdown a metric by splitting it across multiple categories. Effectively, this combines the 'insert' and 'measure' steps into a single operation
  * compute: perform calculations based on existing values such as correlation, averaging, regression, or other data science tasks using numpy or scipy
  * plot: summarize the results by creating a visualization of the data, such as a bar chart or scatter plot

Keep the plan as short as possible by avoiding redundant steps:
  * Do NOT include a 'query' step right before or after a 'plot' step. 'query' is unnecessary because 'plot' already implies a summary.
  * A 'query' step should not follow a 'peek' step either. At the beginning of a plan, 'query' behaves as a 'peek' with additional grouping and ordering. Pick one or the other.
  * A 'measure' step should not preceed a 'segment' step. The 'segment' step already encompasses the metric calculation.
  * Ideally, the output plan should contain no more than 3 to 5 steps.

Start by thinking carefully about the available tables and columns, noting the hierarchy of aggregating columns to form variables, and then forming relations between variables to build metrics.
Following such an order is crucial to ensure that we do not accidentally reference a variable or metric before it has been established.
Also, notice that the natural language plan may contain branching or looping. Since the DSL does not support control logic, these cases should be repeated as individual steps.

Your entire response should be in well-formatted JSON including keys for thought (string) and plan (list), with no further text or explanations after the JSON output.
Each step in the plan is a dict with a name key (token), description (string), and optional keys to hold variables (list of strings) or metrics (list of dicts) when applicable.
Each metric dict is a consists of an acronym (short key) and a full name (long key) that follows industry standards.

For example,
---
Suppose the target analysis is 'LTV to CAC Ratio' and the following data is available:
Tables: stripe_subscriptions_raw; GAds_Spend_export; ai_usage_metrics_prod
Columns: subscription_id, customer_id, current_plan_name, mrr_cents, billing_cycle_anchor, trial_end_date, payment_failure_count, discount_code_applied, initial_plan_name, created_at_utc;
campaign_id, campaign_name, ad_group_id, date, cost_micros, clicks, impressions, utm_medium, utm_campaign, landing_page_path, device_type, conversion_tracking_id, bidding_strategy, quality_score, conversion_value_micros, final_url_suffix;
user_email, prompt_id, content_type, tokens_consumed, prompt_cost_usd, accepted_suggestion, generated_timestamp, workspace_id, model_version, prompt_template_name

_Natural Language Plan_
1. Calculate Customer Acquisition Costs (CAC) - filter for YoY users within a month and aggregate marketing spend per customer from GAds_Spend_export
2. Derive Revenue Metrics - aggregate the mrr_cents and ai_usage_metrics_prod to determine actual revenue per customer
3. Calculate Customer Lifetime Value (LTV) - sum up all past revenue generated by the cohort, then apply a 10% discount rate to account for future value, extending out for three years
4. Finalize LTV/CAC Ratio - compare customer LTV to total CAC to review whether our actions are sustainable, flagging any concerning trends

_Output_
```json
{{
  "thought": "Before calculating metrics, I should query the data to filter for the key variables and time period. I can also drop the revenue metric calculation since this is redundant with the LTV calculation.",
  "plan": [
    {{
      "name": "query", "description": "create a cohort of one month that filters for users who signed up a year ago",
      "variables": ["total_ad_cost", "recurring_revenue", "conversion_value"]
    }},
    {{
      "name": "measure", "description": "calculate the customer acquisition cost based on total ad cost",
      "metrics": [{{ "short": "CAC", "long": "Customer Acquisition Cost"}}]
    }},
    {{
      "name": "measure", "description": "calculate lifetime value by summing up all past revenue, and then apply a 10% discount rate to account for future value, extending out for three years",
      "metrics": [{{ "short": "LTV", "long": "Customer Lifetime Value"}}]
    }},
    {{"name": "compute", "description": "compare the customer LTV to CAC to review whether our actions are sustainable, flag any concerning trends"}}   
  ]
}}
```

In this next example, the target analysis is 'Multi-touch Attribution' and the following data is available:
Tables: facebook_ads_manager_daily; shopify_orders_202401; klayvio_customer_timeline
Columns: ad_id, campaign_name, adset_name, placement_type, daily_spend_cents, video_completion_rate, cta_button_type, audience_name, creative_format, instagram_eligible;
order_id, cart_token, landing_site_path, referring_site, discount_code_used, shipping_method, billing_postal_code, product_collection, total_price_cents, utm_parameters;
shopper_id, event_name, event_timestamp, device_fingerprint, last_utm_source, first_utm_source, session_number, cart_value_cents, products_viewed, time_to_purchase_sec

_Natural Language Plan_
1. Prepare Channel Data - peek at all source columns: campaign_name, adset_name, referring_site, utm_parameters, first_utm_source, and last_utm_source to understand how the channels are related and normalize their format if necessary
2. Build User Journeys - combine the Facebook, Shopify, and Klayvio tables to create user journeys for each customer, keeping only the relevant columns
3. Determine First-touch Attribution - calculate how much each channels contributed to ROAS and purchases using first-touch attribution
4. Alternate Attribution Models - figure out channel contribution using last-touch and linear attribution models as well
5. Compare Channel Performance - rank the performance across channels, accounting for assisted conversions; plot the results to present them in a clear and concise manner

_Output_
```json
{{
  "thought": "I should first figure out a way to join all the tables together to form a single view of the customer journey. Then, I need to loop through the three attribution models, since each one calculates a metric broken down by channel, the 'segment' operation is a great fit. Ranking and plotting results is the final step.",
  "plan": [
    {{"name": "peek", "description": "consider campaign_name, adset_name, referring_site, utm_parameters, first_utm_source, and last_utm_source columns to understand how the channels are related and normalize their format if necessary"}},
    {{
      "name": "pivot", "description": "combine the Facebook, Shopify, and Klayvio tables to create user journeys for each customer, keeping only the relevant columns for storing variables",
      "variables": ["purchases", "ROAS"]
    }},
    {{
      "name": "segment", "description": "calculate attribution methods to ROAS and purchases, segmented by channel",
      "metrics": [
        {{ "short": "first", "long": "First-touch Attribution"}},
        {{ "short": "last", "long": "Last-touch Attribution"}},
        {{ "short": "linear", "long": "Linear Attribution"}}
      ]
    }},
    {{"name": "plot", "description": "aggregate results to rank the performance across channels and show the results in a graph"}}
  ]
}}
```

The target analysis for the third example is 'Customer Segmentation' and has the data:
Tables: Google Display q4; Mixpanel User Events; Segment CDP
Columns: placement_id, creative_asset_url, ad_group_name, device_category, geo_dma_code, avg_viewability_pct, slot_position, ad_size_px, raw_impressions;
device_id, session_id, ip_address, utm_source, landing_page_path, browser_language, os_version, time_on_site_sec, page_scroll_depth, exit_page_url;
twilio_id, user_id, email_domain, signup_source, job_title_raw, industry_raw, last_active_ts, total_sessions, has_downloaded_whitepaper

_Natural Language Plan_
1. Define User Segment - peek at the device_id and ad_group_name columns to understand how to segment the users
2. Stage Engagement Variables - create pivot tables that aggregate the number of clicks, impressions and downloads for each segment
3. Calculate Engagement Metrics - calculate view-through rate and whitepaper download rate for each segment
4. Summarize Segment Performance - identify top performing segments and present the results in a clear and concise manner

_Output_
```json
{{
  "thought": "After figuring out how to segment the users, I can then decide how to create staging tables to aggregate the key variables. Then, I can compute the engagement metrics and finally present the results.",
  "plan": [
    {{"name": "peek", "description": "peek at the device_id and ad_group_name columns to understand how to segment the users"}},
    {{
      "name": "segment", "description": "calculate view-through rate based on clicks and impressions for each ad group segment",
      "metrics": [ {{ "short": "VTR", "long": "View-through Rate"}} ]
    }},
    {{
      "name": "segment", "description": "calculate download rate for whitepapers based on downloads and clicks broken down by segment",
      "metrics": [ {{ "short": "DR", "long": "Download Rate"}} ]
    }},
    {{"name": "plot", "description": "identify top performing segments and present the results in a chart comparing VTR and DR across segments"}}
  ]
}}
```

Our next example covers 'Significance Testing' and includes the following data:
Tables: constant_contact_campaigns; amplitude_analytics; Conferences List (Updated Jan 2025)
Columns: campaign_id, campaign_name, template_variant, sender_email_alias, target_segment_id, subject_line, preview_text, utm_campaign_code, scheduled_datetime, actual_send_datetime;
amp_id, campaign_id, delivery_timestamp, recipient_email_hash, device_type, email_client, bounce_type, open_count, click_count, unsubscribe_flag;
conference_id, conference_name, organizer_entity, venue_name, city_location, state_province, expected_attendance, booth_number, booth_size_sqft, sponsorship_tier

_Natural Language Plan_
1. Data Assessment - look for anomalies in delivery_timestamp to ensure we have the date range we need, check that unsubscribe_flag is not set to True for all records
2. Establish Baseline Metrics - calculate delivery rate, open rate, click-thru rate, and unsubscribe rate, focused on the last year and grouped by campaign
3. Target Cohort Performance - filter to just the performance of the Knock Your Socks Off campaign
4. Compare Target to Baseline - perform a basic significance test by determining the p-value for each of the four key metrics

_Output_
```json
{{
  "thought": "I should first prepare the data to focus in on the last year and the variables of interest. Since we have so many metrics to compare, I will perform row-wise transformations to compute intermediate values at scale rather than calculating each metric separately.",
  "plan": [
    {{"name": "peek", "description": "look for anomalies in delivery_timestamp to ensure we have the date range we need, check that unsubscribe_flag is not set to True for all records"}},
    {{
      "name": "pivot", "description": "calculate delivery rate, open rate, click-thru rate, and unsubscribe rate, focused on the last year and grouped by campaign",
      "variables": ["delivery_rate", "open_rate", "click_thru_rate", "unsubscribe_rate"]
    }},
    {{
      "name": "insert", "description": "add rows for the mean, standard deviation, and number of campaigns for each metric to serve as a baseline",
      "variables": ["mean", "std_dev", "num_campaigns"]
    }},
    {{"name": "insert", "description": "add an additional row that filters for just the performance of the Knock Your Socks Off campaign"}},
    {{
      "name": "query", "description": "create a view that calculates a p-value for each of the four key metrics by comparing the target cohort to the baseline statistics",
      "metrics": [ {{ "short": "PVAL", "long": "P-value Significance"}} ]
    }}
  ]
}}
```

The fifth example deals with 'A/B Testing Results' and has the following data:
Tables: split_io_experiments; posthog_events_raw; user_activation_metrics
Columns: experiment_key, visitor_id, treatment_group, first_exposure_time, client_sdk_version, is_sticky_bucketing, feature_gate_name, traffic_type;
distinct_id, event_name, event_timestamp, current_url, screen_size, session_duration_sec, step_number, error_type, time_on_step, rage_clicks_count;
user_id, signup_date, invited_users_count, projects_created, files_uploaded, completion_percentage, days_to_activation, initial_setup_source, team_size_selected, industry_vertical

_Natural Language Plan_
1. Segment Test Splits - create an filtered view for each split based on the experiment key
2. Calculate Success Metrics - measure conversion lift and user activation for each split in the onboarding flow, adding new columns for each
3. Summarize Findings - compare the experimental splits to the control, and decide whether the new flow(s) should be rolled out

_Output_
```json
{{
  "thought": "For each metric, I will measure its value and save its results. Then, I can compare the experimental splits to the control and decide whether the new flows should be rolled out.",
  "plan": [
    {{
      "name": "measure", "description": "measure conversion lift for each split in the onboarding flow",
      "metrics": [ {{ "short": "Lift", "long": "conversion lift"}} ]
    }},
    {{"name": "insert", "description": "store the results, making sure to have new columns for each control and treatment group"}},
    {{
      "name": "measure", "description": "measure user activation for each split in the onboarding flow",
      "metrics": [ {{ "short": "UA", "long": "user activation"}} ]
    }},
    {{"name": "insert", "description": "store the results, making sure to have new columns for each control and treatment group"}},
    {{"name": "query", "description": "compare the experimental splits to the control, and decide whether the any of the new flows should be rolled out"}}
  ]
}}
```

We now move onto the target analysis for the final example, which is 'Churn Analysis' and has the data:
Tables: Salesforce; Claims History; Risk Assessments; Account Characteristics
Columns: sfdc_account, property_portfolio_value, annual_premium_usd, last_risk_assessment_date, account_owner_email, payment_status, client_segment_tier, renewal_date, primary_contact_title, last_interaction_date;
claim_id, account_id, claim_type, reported_date, settlement_amount_usd, days_to_resolution, adjuster_notes, property_address, deductible_applied, claim_status;
building_age_years, flood_zone_category, property_type, policy_status, policy_expiration_date, policy_renewal_date, policy_premium_usd, policy_deductible_usd, policy_coverage_limit_usd;

_Natural Language Plan_
1. Define Churn Accounts - identify churned accounts from the Salesforce table to by looking at the policy status (renewal_date, payment_status)
2. Aggregate Risk Factors - combine claims history (claim_status, settlement_amount_usd, claim_type), risk assessments (building_age_years, flood_zone_category, property_type), and account characteristics into a single view
3. Model Churn Drivers - analyze correlation between risk attributes and churn probability to help discover at-risk accounts in need of intervention

_Output_
```json
{{
  "thought": "Before calculating metrics, I should pivot the data to filter for the key churn variables. I also need to summarize the risk factors into a single view. Then, computing the correlation should be straightforward.",
  "plan": [
    {{
      "name": "insert", "description": "identify churned accounts from the Salesforce table to by looking at the policy status (renewal_date, payment_status)",
      "variables": ["policy_status"]
    }},
    {{
      "name": "pivot", "description": "combine claims history (claim_status, settlement_amount_usd, claim_type), risk assessments (building_age_years, flood_zone_category, property_type), and account characteristics grouped by churn status",
      "variables": ["claims_history", "risk_assessments", "account_characteristics"]
    }},
    {{
      "name": "compute", "description": "analyze correlation between risk attributes and churned accounts to determine probability of churn",
      "metrics": [ {{ "short": "churn", "long": "Churn Probability"}} ]
    }},
    {{"name": "query", "description": "rank the policies by churn probability to help discover at-risk accounts in need of intervention"}}
  ]
}}
```
#############
Now it's your turn! Please convert the natural language plan into a structured sequence of steps chosen from the list of allowed operations. For context, the available data includes:
{valid_tab_col}

_Natural Language Plan_
{plan_steps}

_Output_
"""

summarize_results_prompt = """We are currently in the middle of {analysis}, and have just completed a step within the analysis.
Given the plan details and a preview of the data, your task is to summarize the results such that we can:
  * decide whether the plan needs adjustment to continue the analysis
  * save important takeways from the analysis that can be shared with the user
  * inform future steps in the plan that may depend on the current results
  * eventually complete the analysis to answer the user's original question

Your summary should be a few points at most, where each point is just one or two sentences long, so please focus on the most relevant details each time.
Additionally, we are only concerned with the results of the current step, so takeaways from previous steps are not relevant and should be omitted.
Finally, note that you are the recipient of the summary (not the user), so please convey the information in the manner that is easiest for you to understand in the future.
Your entire response should be in well-formatted JSON including a key for thought (string) and summary (list of strings), with no further text or explanations after the JSON output.

For example,
#############
To start, suppose we are analyzing LTV to CAC Ratio according to the following plan:
1. pivot (complete) - create a cohort of one month that filters for users who signed up a year ago
2. measure (complete) - calculate the customer acquisition cost based on total ad cost
3. measure - calculate lifetime value by summing up all past revenue, and then applying a 10% discount rate to account for future value, extending out for three years
4. query - compare the customer LTV to CAC to review whether our actions are sustainable, flag any concerning trends

Completing the second step results in the table:
| TotalSpend | VendorFees | TotalCost |
|------------|------------|-----------|
| $16346.16  | $713.22    | $17059.38 |

_Output_
```json
{{
  "thought": "After calculating CAC, we should save the results. The table is quite small, so we can just store all the information.",
  "summary": [
    "The total cost for acquiring a customer is composed of TotalSpend ($16346.16) and VendorFees ($713.22), which adds up to $17059.38.",
    "Ad spend makes up the majority of the cost by far."
  ]
}}
```

In our next example, suppose we are performing Multi-touch Attribution according to the following plan:
1. peek (complete) - consider campaign_name, adset_name, referring_site, utm_parameters, first_utm_source, and last_utm_source columns to understand how the channels are related and normalize their format if necessary
2. pivot - combine the Facebook, Shopify, and Klayvio tables to create user journeys for each customer, keeping only the relevant columns
3. measure - calculate channel contribution to ROAS and purchases using first-touch attribution
4. measure - calculate channel contribution to ROAS and purchases using last-touch attribution
5. measure - calculate channel contribution to ROAS and purchases using linear attribution
6. plot - aggregate results to rank the performance across channels and show the results in a graph

Completing the first step results in the table:
| CampaignName         | AdsetName             | referring_site     | utm_parameters                                              | first_utm_source | last_utm_source |
|----------------------|-----------------------|--------------------|-------------------------------------------------------------|------------------|-----------------|
| FB_Spring_Sale_2024  | Lookalike_2%_USA      | facebook.com       | utm_source=facebook&utm_medium=cpc&utm_campaign=spring_sale | facebook         | Instagram       |
| Google_Brand_Search  | exact_match_keywords  | www.google.com     | utm_source=google&utm_medium=cpc&utm_campaign=brand_terms   | organic          | Google          |
| ig_UGC_influencer_Q1 | Female_25_34_Interest | l.instagram.com    | utm_source=ig&utm_campaign=ugc_q1&utm_content=story         | INSTAGRAM        | Facebook        |
| Email_Abandoned_Cart | 30_Day_Active         | email.klaviyo.com  | utm_source=klaviyo&utm_medium=email&utm_campaign=cart_recovery | organic       | email           |
| PMax_BlackFriday2023 | smart_shopping        | shopping.google.com| gclid=Cj0KCQiA_P6dBhD1ARIs                                  | google           | pmax            |
| FB_Valentine_Promo   | Engagement_Retarget   | facebook.com       | utm_source=facebook&utm_medium=social&utm_campaign=vday24   | facebook         | Facebook        |
| Email_WinBack_90d    | Churned_Customers     | click.klaviyo.com  | utm_source=Klaviyo&utm_medium=email&utm_campaign=winback    | email            | email           |
| ig_Stories_Carousel  | Interest_Beauty_US    | instagram.com      | utm_source=instagram&utm_medium=stories&utm_campaign=carousel_q1 | organic     | Instagram       |

_Output_
```json
{{
  "thought": "The goal of peeking at the columns is to see how we might be able to join the tables together, so we should focus on their relationships.",
  "summary": [
    "referring_site, utm_parameters, first_utm_source, and last_utm_source have overlapping keywords, but are not exactly the same so they will need to be normalized.",
    "The utm sources seem to have different capitalization, so we should lowercase all of them before trying to join them.",
    "The source can potentially be connected to CampaignName, but only on a loose basis, rather than a strict one-to-one mapping.",
    "The AdsetName is largely disconnected from the other columns, so it is not useful for joining."
  ]
}}
```

Moving on, suppose we are performing Customer Segmentation according to the following plan:
1. peek (complete) - peek at the device_id and ad_group_name columns to understand how to segment the users
2. pivot (complete) - create tables that aggregate the number of impressions and conversions for each segment
3. measure (complete) - calculate view-through rate and whitepaper download rate for each segment
4. query - identify top performing segments and present the results in a clear and concise manner

Completing the third step yields the table:
| Device         | AdGroup              | ViewThroughRate | DownloadRate |
|----------------|----------------------|-----------------|--------------|
| Mobile_iOS     | Professional_Finance | 0.0423          | 0.0186       |
| Mobile_Android | Professional_Finance | 0.0387          | 0.0142       |
| Desktop_Mac    | Professional_Finance | 0.0612          | 0.0324       |
| Desktop_Windows| Professional_Finance | 0.0534          | 0.0267       |
| Mobile_iOS     | Student_Resources    | 0.0756          | 0.0434       |
| Mobile_Android | Student_Resources    | 0.0698          | 0.0389       |
| Desktop_Mac    | Student_Resources    | 0.0578          | 0.0298       |
| Desktop_Windows| Student_Resources    | 0.0498          | 0.0245       |

_Output_
```json
{{
  "thought": "We just finished calculating the engagement metrics, so we should summarize the results for each metric.",
  "summary": [
    "The highest view-through rate is for student content on mobile iOS devices.",
    "The highest download rate is for student content on mobile iOS devices.",
    "Consistent slightly better performance on iOS vs Android.",
    "The trend in View-through rates are very highly correlated with download rates."
  ]
}}
```

If we finished the analysis in the fourth step, the table might look like this after grouping:
| segment              | avg_view_through | avg_download |
|----------------------|------------------|--------------|
| student ad group     | 0.0633           | 0.0342       |
| professional ad group| 0.0489           | 0.0230       |
| desktop              | 0.0556           | 0.0284       |
| mobile               | 0.0566           | 0.0288       |

_Output_
```json
{{
  "thought": "Since we are done with the Customer Segmentation, we should pick out the most important takeaways based on each segment.",
  "summary": [
    "Student-focused content generally outperforms professional content in terms of both view-through and download rates.",
    "Desktop and mobile devices have very similar view-through and download rates, likely indicating that device has little impact on engagement."
  ]
}}
```

For our fourth example, suppose we are performing Significance Testing according to the following plan:
1. peek (complete) - look for anomalies in delivery_timestamp to ensure we have the date range we need, check that unsubscribe_flag is not set to True for all records
2. pivot (complete) - calculate open rate, click-through rate, and unsubscribe rate, focused on the last three months and grouped by campaign
3. insert (complete) - add rows for the mean, standard deviation, and number of campaigns for each metric
4. insert (complete) - add a row to the table that filters for just the performance of the Knock Your Socks Off campaign
5. compute (complete) - calculate a p-value for each of the three key metrics by comparing the target cohort to the statistics described above

Completing the fifth step results in the table:
| Metric                       | OpenRate | ClickThroughRate | UnsubscribeRate |
|------------------------------|----------|------------------|-----------------| 
| mean (all campaigns)         | 0.198    | 0.027            | 0.003           |
| std_dev (all campaigns)      | 0.042    | 0.008            | 0.001           |
| num_campaigns (all campaigns)| 10       | 10               | 10              |
| Knock Your Socks avg.        | 0.276    | 0.041            | 0.002           |
| p-value                      | 0.023    | 0.018            | 0.142           |

_Output_
```json
{{
  "thought": "Since we are done with Significance Testing, we should pick out the most important takeaways based on the p-values.",
  "summary": [
    "Since 2.3% < 5%, the Knock Your Socks Off campaign has a statistically significant higher open rate than the average campaign.",
    "Since 1.8% < 5%, the Knock Your Socks Off campaign has a statistically significant higher CTR than the average campaign.",
    "Since 14.2% > 5%, the Knock Your Socks Off campaign performs similarly to the average campaign in terms of unsubscribe rate."
  ]
}}
```

In our final example, suppose we are performing Churn Analysis according to the following plan:
1. pivot (complete) - identify churned accounts from the Salesforce table to by looking at the policy status (renewal_date, payment_status)
2. insert (complete) - combine claims history (claim_status, settlement_amount_usd, claim_type), risk assessments (building_age_years, flood_zone_category, property_type), and account characteristics into a single view
3. compute (complete) - analyze correlation between risk attributes and churned accounts to determine probability of churn
4. query - rank the policies by churn probability to help discover at-risk accounts in need of intervention

Completing the third step results in the table:
| Feature                     | Correlation_w_Churn | Probability | Sample_Mean | Churn_Group_Mean |
|-----------------------------|---------------------|-------------|-------------|------------------|
| days_since_last_interaction | 0.342               | 0.008       | 45.6        | 89.3             |
| claims_last_12m_count       | 0.287               | 0.012       | 0.8         | 1.4              |
| avg_claim_resolution_days   | 0.276               | 0.015       | 18.4        | 25.7             |
| premium_to_portfolio_ratio  | 0.264               | 0.018       | 0.012       | 0.018            |
| building_age_years          | 0.245               | 0.022       | 23.5        | 31.2             |
| high_risk_flood_zone_flag   | 0.238               | 0.024       | 0.15        | 0.28             |
| total_settlement_amount_ytd | 0.226               | 0.031       | 12450.00    | 24680.00         |
| payment_late_count_6m       | 0.198               | 0.046       | 0.3         | 0.8              |
| property_value_to_coverage  | -0.187              | 0.042       | 0.82        | 0.71             |
| client_segment_score        | -0.312              | 0.009       | 7.8         | 6.2              |

_Output_
```json
{{
  "thought": "We just finished modeling the churn drivers, so we should summarize the risk factors that are most correlated with churn.",
  "summary": [
    "The most important risk factors for churn are days since last interaction, claims last 12m count, and average days until claim resolution.",
    "The client segment score practically has a negative correlation with churn, indicating that higher scores are associated with lower churn.",
    "Late payment counts by six months suggests a very high probability that the customer will churn."
  ]
}}
```
#############
Now, given the stated plan and the resulting table, please summarize the results in a few concise bullet points. 
For our real scenario, we are {analysis} according to the following plan:
{current_plan}

Completing the {iteration} step results in the table:
{table_md}

_Output_
"""

interesting_enough_prompt = """We have just completed calculating {analysis_type} according to the following plan:
{previous_plan}

Given the conversation history and a summary of the analysis, decide whether the results are interesting enough to present to the user.
Concretely, the results are interesting if they meet any of the following criteria:
  * significant - encountering a value that is above or below the average by 10% or more is quite interesting
  * surprising - unexpected qualitative observations are also interesting, including cases where we thought something would happen but it did not
  * actionable - the best insights are those that justify moving forward with a decision (eg. send an email) or stopping an activity (eg. stop a poorly performing campaign)

Your entire response should be in well-formatted JSON including a key for thought (string) and is_interesting (boolean), with no further text or explanations after the JSON output.

For example,
#############
_Conversation History_
User: I want to see the total number of clicks for each campaign.
Agent: The total clicks are 1,228 for the 'Independence Day' campaign. See the table for more.
User: What about just for this week?
Agent: The total clicks for this week are 234 for the 'Independence Day' campaign. See the table for more.

_Summary of Analysis_
* The total clicks for the 'Independence Day' campaign are 1,228.
* The total clicks for this week are 234 for the 'Independence Day' campaign.

_Output_
```json
{{
  "thought": "The results are not interesting enough to present to the user.",
  "is_interesting": false
}}
```

_Conversation History_
User: I want to see the total number of clicks for each campaign.
Agent: The total clicks are 1,228 for the 'Independence Day' campaign. See the table for more.
User: What about just for this week?

_Summary of Analysis_
* The total clicks for the 'Independence Day' campaign are 1,228.
* The total clicks for this week are 234 for the 'Independence Day' campaign.
* The total clicks for this week are 234 for the 'Independence Day' campaign, which is 95% lower than the total clicks for the campaign.

_Output_
```json
{{
  "thought": "The results are surprising and actionable, so we should present them to the user.",
  "is_interesting": true
}}
```
#############
Now it's your turn! Please decide whether the results are interesting enough or if we should generate a new hypothesis to explore.

_Conversation History_
{history}

_Summary of Analysis_
{summaries}

_Output_
"""

focus_metric_prompt = """Based on the conversation, we want to calculate the {metric} metric.
However, we need to focus in on which columns from the {table} table are most relevant to the metric.
Please review the conversation history and the valid columns to select the subset of columns that are most useful for calculating the metric.
Remember to only choose from the list of valid columns without changing any spelling or formatting.
Your entire response should simply be a comma-separated list of column names, with no further text or explanations after the list.

For example,
#############
Suppose the valid columns are:
ContactID, UniqueClicks, ClickThruPct, LastUpdatedTS, IsScheduled, EmailAddress, BounceSoftCount, BounceHardCount, TemplateIDFK, ModifiedByUsr, CreatedDatetime, IsArchivedFlag, DeliverabilityScore, EmailPreviewTxt, ListIDsCSV in constant_contact_res

_Conversation History_
User: I want to see the total number of clicks for each campaign.
Agent: The total clicks are 1,228 for the 'Independence Day' campaign. See the table for more.
User: What about just for this week?

Columns: UniqueClicks, ClickThruPct, LastUpdatedTS, CreatedDatetime
#############
As another example, suppose the columns are:
promo_sk, program_name, partner_org_id, discount_pct, eligible_vehicle_types, requires_medical_docs, docs_required_txt, total_num_claims, valid_from_dt, valid_to_dt in discount_programs_active

_Conversation History_
User: so for example, we can see if those folks usually pay a lot, pay a little, or where they usually come from. Whatever you want to come up.
Agent: OK, I will check for any patterns among ticket issuers starting with payment amounts and pick-up locations. Should we limit our analysis to repeat ticket issuers or some other minimum threshold of tickets?
User: Yea, sure why not. Only people who have filed tickets more than once.
Agent: Got it. My next question is, what is a reasonable baseline amount that you expect for a payment? When would you consider someone to be paying a lot or a little?
User: I guess 100 to 200 is normal, anything above $500 is over

Columns: payment_amt, pickup_addr, issue_category_main, total_num_claims
#############
For our final example, suppose the available columns are:
campaign_id, campaign_code, promo_name, segment_code, channel_type, budget_allocated, budget_spent, response_pct, asset_url, is_active, created_dt, modified_dt in mkt_campaigns_v2

_Conversation History_
User: Actually, total ad spend includes discount amount in addition to budget spent.
Agent: It seems that the promo_name from mkt_campaigns_v2 does not line up with the promo_sk from promo_discount_rules, any ideas on how to join the two tables together?
User: Same as before, if it's running at the same time, they are the same promotion.
Agent: What time period should I use for calculating the return on ad spend?
User: I think anything from the last quarter should be fine.

Columns: promo_name, budget_spent, budget_allocated, response_pct, created_dt, modified_dt
#############
In our real case, the valid columns are:
{columns} in {table}

_Conversation History_
{history}

"""