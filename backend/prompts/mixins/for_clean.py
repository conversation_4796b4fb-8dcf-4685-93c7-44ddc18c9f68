update_prompt = """Given the conversation history and supporting details, follow the thought process to generate Pandas code for updating the data.
Supporting details includes information on the columns of the table being changed with a likely datatype written in parentheses.
If an entire table is being changed, then the location will be written as 'all' instead, designating all columns in the table.
Examples of changes include renaming content, modifying values, or filling rows with new content based on other columns.

You can access dataframes as 'db' followed by a table name: `db.table_name`
Concretely, the dataframes you have are {df_tables}.

When possible to do so easily, perform the operation in place rather than assigning to a dataframe.
If multiple requests are presented in the conversation, only pay attention to instructions found in the most recent user turn.
Please only output executable Python, so any explanations must be written as comments. If a request requires multiple steps, write each step on a new line.

For example,
---
_Conversation History_
User: Can you capitalize all the columns in activity_log?
Agent: Sure, I can do that for you. How does this look?
User: That looks good, please do the same for the purchases table.

_Supporting Details_
Location: all columns in purchases
Thought: We have a purchases table which has columns that can be capitalized using title()
Explanation: Note, we should *NOT* make any changes to activity_log table since the final user turn is only concerned with purchases.

_Output_
```python
db.purchases.columns = [col.title() for col in db.purchases.columns]
```

_Conversation History_
User: Create a formula that is True if the price is greater than $1000 and False otherwise.

_Supporting Details_
Location: price (currency) column in orders
Thought: I can check if the price is greater than $1000 by using a comparison operator.  I will then create a new column with a short descriptive name, such as 'expensive'.

_Output_
```python
db.orders['expensive'] = db.orders['price'] > 1000
```

_Conversation History_
User: Simplify the table name from jpm_conference_lead_downloads_0108202 to just jpm_leads

_Supporting Details_
Location: all columns in jpm_conference_lead_downloads_0108202
Thought: We are changing the name of the table rather than just changing the column names.

_Output_
```python
db.jpm_leads = db.jpm_conference_lead_downloads_0108202
del db.jpm_conference_lead_downloads_0108202
```

_Conversation History_
User: Got it, please change them into first_name and last_name

_Supporting Details_
Location: first (name), last (name) columns in users
Thought: The user is updating columns from the previous turn, so I should carry over the tables and columns.

_Output_
```python
db.users.rename(columns={{'first': 'first_name', 'last': 'last_name'}}, inplace=True)
```

_Conversation History_
User: Can you drop the year from the column?

_Supporting Details_
Location: sales_by_state_in_2022 (currency) column in orders
Thought: The user is referring to the previous request, so the column likely refers to sales_by_state_in_2022. I can perform this operation by renaming the column in place.

_Output_
```python
db.orders.rename(columns={{'sales_by_state_in_2022': 'sales_by_state'}}, inplace=True)
```

_Conversation History_
User: What is the price of the most expensive shoe sold?
Agent: The most expensive shoe was New Balance - Men's Arrow with a price of $6112
User: That doesn't look right! We should reduce it by 100

_Supporting Details_
Location: product_id (id), product_name (general), product_type (category), retail_value (currency) in ProductAnalytics
Thought: The price of New Balance - Men's Arrow should be divided by 100.

_Output_
```python
db.productanalytics.loc[(db.productanalytics['product_type'] == 'shoe') & (db.productanalytics['product_name'] == 'New Balance - Men\'s Arrow'), 'retail_value'] /= 100
```

_Conversation History_
User: Let's break that down into three new columns for month, day and year

_Supporting Details_
Location: date_due (date) column in orders
Thought: I can break the date_due column into three new columns by searching for the appropriate separator and then using the split method.
    I will also need to convert the values into integers so we can query them.

_Output_
```python
for separator in ['/', '-', '.', ' ']:
  if db.orders['date_due'].str.contains(separator).any():
    db.orders[['month', 'day', 'year']] = db.orders['date_due'].str.split(separator, expand=True)
    break
db.orders[['month', 'day', 'year']] = db.orders[['month', 'day', 'year']].astype(int)
```

_Conversation History_
User: Can you change the date of all arrivals in November back by one day?

_Supporting Details_
Location: shipment_id (id), arrival_time (timestamp) in inventory
Thought: The user is updating a row from the previous query, which I can do by first finding the shipment in November and then subtracting one day from the arrival_time.

_Output_
```python
db.inventory.loc[db.inventory['arrival_time'].dt.month == 11, 'arrival_time'] -= pd.DateOffset(days=1)
```

_Conversation History_
User: Are there any other problems in the data?
Agent: It seems that Stopify is also a typo.
User: Yes, that's a good find.

_Supporting Details_
Locations: OnlineVendors (general), ActiveVendors (general) in Partnerships
Thought: I should update both OnlineVendors and ActiveVendors columns by replacing all instances of 'Stopify' with 'Shopify'

_Output_
```python
db.Partnerships['OnlineVendors'].replace(to_replace='Stopify', value='Shopify', inplace=True)
db.Partnerships['ActiveVendors'].replace(to_replace='Stopify', value='Shopify', inplace=True)
```

_Conversation History_
User: Can you drop all cities that are blank or nans?

_Supporting Details_
Location: cities (city) column in customers
Thought: I can remove blank cities by searching for an empty string. Then, I will remove cities with nans using the dropna method.

_Output_
```python
db.customers = db.customers[db.customers['city'] != '']
db.customers = db.customers.dropna(subset=['city'])
```

_Conversation History_
User: The accounts payable deadline has changed and is no longer June.
Agent: What should the new deadline be?
User: Can you update this to September instead?

_Supporting Details_
Location: AccountsPayable (boolean), DueDate (date) column in orderagreements
Thought: I should update the formula in AccountsPayable column to use DueDate > September 1st, instead of June 1st.

_Output_
```python
db.orderagreements['AccountsPayable'] = db.orderagreements['DueDate'] > pd.Timestamp('2023-09-01')
```
---
Now it's your turn! Please generate well-formatted Python code for updating the data.

_Conversation History_
{history}

_Supporting Details_
Location: {location}
Thought: {thought}

_Output_
"""

clean_issues_prompt = """Given the conversation history and `subset_df` dataframe, generate pandas code for updating the data to comply with the user's request.
{df_description}
For simplicity, the `subset_df` displayed only focuses on relevant subset of data contained within the full `issue_df` dataframe.
Therefore, if any code requires access to an entire row or column, please reference the `issue_df` dataframe.
If you see 'N/A' in the table, you can assume it refers to a pandas NaN value rather than a string.

Start by thinking carefully about what code to generate, and then output well-formatted Python code. If a request requires multiple steps, write each step on a new line.

For example,
#############
Agent: The numbers with outliers include 20,00, 30,45, and 23,50.
User: Got it, they should be $20.00, $30.45 and $23.50.
|    |  price   |
|---:|:---------|
|  0 |  20,00   |
|  1 |  30,45   |
|  2 |  23,50   |

Thought: I can replace the comma with a period and convert the column to a float data type.
```python
subset_df['price'] = subset_df['price'].str.replace(',', '.').astype(float)
```

#############
Agent: The rows with issues are None and None. Please see the table for more.
User: "Oh, they should actually be 09-12-23 13:00:00."
|    | shipping_date       |
|---:|:--------------------|
|  0 | N/A                 |
|  1 | N/A                 |

Thought: All the rows in `subset_df` are null, so I can replace them all at once. I will convert the entire column to a datetime data type, which requires referencing `issue_df`.
```python
subset_df['shipping_date'] = '09-12-23 13:00:00'
issue_df['shipping_date'] = pd.to_datetime(issue_df['shipping_date'])
```

#############
Agent: The text with anomalies are N/A, N/A, and Priya\ufffds Indian Delight.
User: Yea, please re-write into Priya's Indian Delight.
|    | restaurants                 |
|---:|:----------------------------|
|  0 | Priya\ufffds Indian Delight |

Thought: I can replace the unknown character with an apostrophe.
```python
subset_df['restaurants'].replace("\ufffd", "'", inplace=True)
```

#############
Agent: What would you like to do with the remaining row(s)?
User: You can drop the 'now 30% off' part from the Bomber product name.
|    | product_name                                         |
|---:|:-----------------------------------------------------|
|  0 |  Vegan Suede Bomber with Faux Leather - now 30% off  |

Thought: I will remove the ' - now 30% off' from the product name.
```python
subset_df['product_name'] = subset_df['product_name'].str.replace(' - now 30% off', '')
```

#############
Agent: There were three rows with outliers having values of 5232 and 5235, and two rows with missing values.
User: Oh, those should be 232 and 235.
|    | lead_score  |
|---:|:------------|
|  0 |  5232       |
|  1 |  5235       |
|  2 |  5232       |

Thought: I will subtract 5000 from the lead score to correct the outliers.
```python
subset_df['lead_score'] = subset_df['lead_score'].astype(int) - 5000
```

#############
Agent: The dates with issues are 18:00:00, yesterday and yesterday
User: Change all the rows into 18:01:01
|    |  time      |
|---:|:-----------|
|  0 |  18:00:00  |
|  1 |  yesterday |
|  2 |  yesterday |

Thought: I can directly assign the new time value to `subset_df`.
```python
subset_df['time'] = '18:01:01'
```

#############
Agent: There were four rows with empty values in the pageVisit column.
User: Let's fill them in with the average of the other visits.
|    | pageVisit   |
|---:|:------------|
|  0 |  N/A        |
|  1 |  N/A        |
|  2 |  N/A        |

Thought: Since I want to fill the missing values with the global average, I will calculate the average visits using `issue_df` first and then fill `subset_df` afterwards.
```python
average_visits = issue_df['pageVisit'].mean()
subset_df['pageVisit'].fillna(average_visits, inplace=True)
```

#############
Agent: The date issues include , , and 12:0. See the table for more details.
User: Bleh, this is sort of messy. Let's change 12:0 to 12:00:00 and delete the blank rows.
|    |  OrderTime  |
|---:|:------------|
|  0 |             |
|  1 |             |
|  2 |  12:0       |

Thought: I will first replace the times, and then separately remove the blank rows.
```python
subset_df['OrderTime'] = subset_df['OrderTime'].replace('12:0', '12:00:00')
subset_df = subset_df[subset_df['OrderTime'] != '']
```

#############
Remember, we are only concerned with updating the data based on the final user turn. All prior user and agent turns only serve to provide context.
Now, please generate a thought and then write the pandas code to clean the data, with no additional text after the code block.

{convo_history}
{issues}
"""


"""You are given some utterances and a status describing problems concerning mixed data types or unsupported data within a single column.
You will then be shown a rows of values in the format "row_id) data_type - value" for up to seven rows.
Examples of data types include boolean, status, year, month, address, currency, percent, email, name, or category.
Next, you will be given the user's response about how to deal with these problems, such as updating, ignoring or removing them.
Using this information, please decide which datatype is being referenced, or 'all' if the user is referencing all problems at once.
Finally, output the associated row_id. If the answer is unclear, please output 'unsure' instead.

Given the user request and supporting details, follow the thought process to generate Pandas code for updating the data.
Supporting details includes information on the part of the table being changed, a description of the change, and the method for deriving the new values.
Changes include renaming content, modifying values, or filling rows with new content based on other columns.
The dataframes you have are {df_tables}.
Please only output executable Python without any explanations. If a request requires multiple steps, write each step on a new line.
When possible to do so easily, perform the operation in place rather than assigning to a dataframe."""

row_styles_prompt = """Given the most recent utterances and the available columns, your goal is to choose the most likely methods for resolving merge conflicts when removing duplicates.
Concretely, suppose a user wants to remove duplicates based on email address. Two rows might have the exact same email, but the other columns may contain conflicting values, so we need to decide which row's values take precedence.
The column(s) chosen for determining duplicates cannot be used for conflict resolution (since by definition they contain equivalent values). They will start with at symbol '@' to help you identify them.
Start by briefly thinking out loud about the top methods you would use, and then rank up to 5 methods in order of most likely to least likely.
Each method should consist of a method name and column name tuple, such as ['time', 'DateTimeJoined'] or ['contains', '.ai']. We technically use a list of lists since JSON does not support tuples.

There are 12 possible methods to choose from:
  - *order*: keep the value based on the order of appearance, a good default option when no other method seems appropriate
  - *time*: keep a value based on the time it occurred, whether it is earlier or later according to a timestamp or date
  - *contains*: keep a value based on whether it contains a substring, a good method for filtering out specific values
  - *binary*: use a third, external column that contains a binary value to decide which row to keep
  - *add*: keep the sum of the two values, a good method for calculating totals
  - *subtract*: keep the difference between the two values, a good method for calculating changes
  - *mean*: keep the average of the values in the rows, a good method when both values are equally important
  - *size*: keep the value that is smaller or larger, a good method for choosing the minimum or maximum
  - *length*: keep the value based on the length of the text, for example to keep the longer or shorter text
  - *concat*: combine the text together directly, with no space in between, a good method for combining text
  - *space*: join the text with a space, for example to create a full name
  - *alpha*: keep the text which comes earlier or later in the alphabet, for example to sort a list

Your entire response should be in well-formatted JSON including rationale (string) and methods (list of tuples); with no further explanations after the JSON output.

For example,
---
User: Can you take a look to see if there are any duplicates in the CRM data?
Agent: Certainly, I have found the FirstName and LastName columns to determine duplicates. When there are conflicts, how do you want to determine which row to keep?
User: Keep all the rows? What do you mean?
Columns: ContactID, @FirstName, @LastName, DateTimeJoined, EmailAddress, OpportunityID, Stage, DealSize, LastContactDate, NextStep, DecisionMaker, City, State, ZipCode

_Output_
```json
{{
  "rationale": "Users who joined earlier or had been contacted most recently are more likely to be active. Larger deal size is also a good indicator of a strong lead.",
  "methods": [["time", "DateTimeJoined"], ["time", "LastContactDate"], ["size", "DealSize"], ["length", "NextStep"], ["order", "ContactID"]]
}}
```

User: I want to merge different testimonials together into one.
Agent: How would you like to determine duplicates when merging the rows?
User: merge rows based on the TestimonialID
Columns: @TestimonialID, StudentID, CourseID, TestimonialText, DateProvided, Rating, Featured, ApprovalStatus, PhotoLink, OutreachID, CampaignName, TargetAudience, Platform, ResponseRate

_Output_
```json
{{
  "rationale": "Longer testimonials are more likely to be more important. A higher rating or a featured testimonial is more likely to be unique. The date provided is also important.",
  "methods": [["length", "TestimonialText"], ["size", "Rating"], ["binary", "Featured"], ["time", "DateProvided"]]
}}
```

User: well, there are lots of repeated entries in the customer data.
Agent: Sure, I can use the CustName and ShippingAddress columns to determine duplicates. When there are conflicts, how do you want to determine which row to keep?
User: I want to keep the most active customers.
Columns: CustomerID, @CustName, FavCuisineType, @ShippingAddress, ContactNumber, IsActive, Twitter, Instagram, Yelp

_Output_
```json
{{
  "rationale": "IsActive is the most important indicator of activity. The number of reviews on Yelp and Instagram followers are also good indicators. Longer values for favorite cuisine type or contact number are also good.",
  "methods": [["binary", "IsActive"], ["size", "Yelp"], ["size", "Instagram"], ["length", "FavCuisineType"], ["length", "ContactNumber"]]
}}
```

User: I want to remove duplicate orders based on the OrderID and CustomerID columns.
Agent: Ok, I can use the OrderID and CustomerID columns to determine duplicates. When there are conflicts, how do you want to determine which row to keep?
User: Why are there conflicts?
Columns: @OrderID, @CustomerID, RestaurantID, OrderDate, TotalAmount, DeliveryAddress, OrderStatus, EstDeliveryTime, SpecialInstructions

_Output_
```json
{{
  "rationale": "The order status is the most important indicator of the current state of the order. The total amount is also a good indicator of the value of the order. The delivery address is also important.",
  "methods": [["size", "TotalAmount"], ["length", "DeliveryAddress"], ["time", "OrderDate"]]
}}
```

User: sure, ok
Agent: When there are conflicts, how do you want to determine which row to keep?
User: I don't know, you tell me
Columns: CustomerID, @FirstName, @LastName, @Email, PhoneNumber, DateRegistered, PurchaseHistory, LoyaltyPoints, Address, PreferredBrand

_Output_
```json
{{
  "rationale": "The date registered is the most important indicator of activity. The purchase history and loyalty points are also good indicators.",
  "methods": [["time", "DateRegistered"], ["size", "PurchaseHistory"], ["size", "LoyaltyPoints"], ["order", "CustomerID"]]
}}
```

User: why do some many campaigns have the same name?
Agent: I can remove duplicates based on CampaignName, would you like me to do that?
User: Yes, please
Columns: CampaignID, @CampaignName, StartDate, EndDate, TargetAudience, Channel, Budget, ResponseRate, CreativeAsset in Campaigns

_Output_
```json
{{
  "rationale": "The start date and end date are likely useful for determining which campaign is active. The budget and response rate are also good indicators.",
  "methods": [["time", "StartDate"], ["time", "EndDate"], ["size", "Budget"], ["size", "ResponseRate"], ["order", "CampaignID"]]
}}
```

User: yes, they are repeated
Agent: I will consider rows with identical emails and organizations as duplicates. When there are conflicts, how do you want to determine which row to keep?
User: Just keep the most recent ones.
Columns: uuid, activity_type, duration, data_source, outcome, @email, @organization, lead_source, contact_date, status, notes

_Output_
```json
{{
  "rationale": "The most recent contact date is the most important indicator of activity. The lead source and status can also be useful.",
  "methods": [["time", "contact_date"], ["length", "lead_source"], ["binary", "status"], ["binary", "outcome"]]
}}
```

User: ok, how does that work
Agent: I will consider rows with identical full_name and email_address as duplicates. When there are conflicts, how do you want to determine which row to keep?
User: What does that mean?
Columns: full_name, email_address, phone_number, date_joined, membership_type, membership_fee, expiration_date, packages_bought, emergency_contact

_Output_
```json
{{
  "rationale": "The date joined is the most important indicator of activity. The phone_number and expiration_date can also be useful. Some values with numbers can likely be averaged.",
  "methods": [["time", "date_joined"], ["length", "phone_number"], ["time", "expiration_date"], ["mean", "membership_fee"], ["mean", "packages_bought"]]
}}
```
---
Now it's your turn to decide! Remember that rationales should be very concise, with a length of only two to three sentences.
The methods to choose from are ['order', 'time', 'contains', 'binary', 'add', 'subtract', 'mean', 'size', 'length', 'concat', 'space', 'alpha'].
Your output should only include the JSON, with no further explanations.

_Conversation History_
{history}
Columns: {column_info}


_Output_
"""

validate_routing_prompt = """Given the conversation history we are fairly certain the user is trying to clean the data within the {column} column.
However, we still need to decide what task the user is trying to accomplish. Options include:
  * validate: ensuring every row in the column belongs to some predefined set of values (such as accounts being active/inactive) or fall within some predefined range
    - this option should only be chosen when the user is trying to perform validation, and the target values are well-defined
  * typo: correcting spelling errors in the data, such as changing 'coversion' to 'conversion'. When in doubt, choose this option.
  * format: standardizing the data within the column to conform to a specific format, such as MM/DD/YYYY or 24-hour time
  * update: changing existing cells, such as trimming whitespace, or find and replace operations.
    - this option should only be chosen when the user is trying to clean the data, but it is unclear how to do so.
  * ambiguous: the user is trying to clean the data, but it is unclear how to do so. For example, the user may be trying to perform validation, but the target values are not well-defined.
    - this option should only be chosen when the user is trying to clean the data, but it is unclear how to do so.

Please start by considering what exactly the user is trying to accomplish, then decide on the appropriate method.
Your entire response should be in well-formatted JSON including your thought (string) and the chosen method (token), with no further explanations after the JSON output.

For example,
---
_Conversation History_
User: Which campaign had the highest response rate in the last quarter?
Agent: The response rate is the highest for the 'HalloWeekend' campaign.
User: Can we fix all the values for the different campaign names?

_Output_
```json
{{
  "thought": "Fixing is a bit ambiguous, but the user is certainly trying to clean data. I will default to the typo option.",
  "task": "typo"
}}
```

_Conversation History_
User: So we have emails as the main contact method for all customers?
Agent: There is also a column for phone numbers.
User: Can we quickly check that all the phone numbers are valid?

_Output_
```json
{{
  "thought": "Even though the user has mentioned validation, phone numbers are not part of a predefined set of values. I will consider this a formatting task.",
  "task": "format"
}}
```

_Conversation History_
User: Cool, we can move onto cleaning the addresses now.
Agent: OK, could you please clarify what you mean by cleaning?
User: We should make sure all the cities are accurate, so like NY should actually be New York.

_Output_
```json
{{
  "thought": "The target value of New York is part of a predefined set of US states, so we can consider this data validation.",
  "task": "validate"
}}
```

_Conversation History_
User: We need to fix the issues in the 'actions taken' column.
Agent: Apologies, I didn't quite get that. What issue are you trying to resolve?
User: So for example, I'm pretty sure concatanate is not correct. 

_Output_
```json
{{
  "thought": "The target value of 'concatenate' seems to be part of an open set, so we will consider this as a typo
  "task": "typo"
}}
```

_Conversation History_
User: Sure, let's do that.
Agent: OK, what style do you want the dates to be in?
User: Can we make sure everything is written as MM/DD/YYYY?

_Output_
```json
{{
  "thought": "The target of MM/DD/YYYY is a specific format rather than a predefined value, so we will consider this as a formatting task.",
  "task": "format"
}}
```
---
Now it's your turn! Please decide on the method of cleaning based on the conversation history, choosing from the following options: [validate, update, format, ambiguous]
For additional context, here is a preview of the data:
{data_preview}

_Conversation History_
{history}

_Output_
"""

datatype_routing_prompt = """Given the conversation history we are fairly certain the user is trying to clean the data within the {column} column.
However, we still need to decide what task the user is trying to accomplish. Options include:
  - validate: ensuring every row in the column belongs to some predefined set of values (such as accounts being active/inactive) or fall within some predefined range
  - typo: correcting spelling errors in the data, such as changing 'coversion' to 'conversion'. When in doubt, choose this option.
  - format: standardizing the data within the column to conform to a specific format, such as MM/DD/YYYY or 24-hour time

If none of these options seem appropriate, you can choose 'other'.
Your response should only be in well-formatted JSON including your thinking and the chosen option, without any additional text or explanations.

For example,
#############
_Conversation History_
User: Which campaign had the highest response rate in the last quarter?
Agent: The response rate is the highest for the 'HalloWeekend' campaign.
User: Can we fix all the values for the different campaign names?

_Output_
```json
{{
  "thought": "Fixing is a bit ambiguous, but the user is certainly trying to clean data. I will default to the typo option.",
  "task": "typo"
}}
```
############
_Conversation History_
{history}

_Output_
"""

format_routing_prompt = """Given the conversation history, decide how the user is trying to standardize the data within a column.
For our purposes, the valid methods are:
  * format: changing the data within the column to conform to a specific format, such as MM/DD/YYYY, HH:MM:SS, or (XXX) XXX-XXXX
    - applying a format implies having a format string that represents the target display style. If no such string can be identified, then we're likely dealing with a different task
    - reformatting only works with datetimes and strings at the moment. Other datatypes such as numbers, booleans or locations also mean a different method is needed
  * validate: ensuring every row in the column belongs to some predefined set of values or fall within some predefined range
    - predefined sets are represented by a finite list of values. For example, membership status can be ['active', 'inactive', 'pending'], or valid registration location is one of the 50 US states
    - a predefined range is represented by a minimum and maximum value. For example, the discount rate goes from 0% and 100%, or the age of the customer must be between 18 and 65
  * update: cleaning existing values within the column by applying specific operations, such as trimming whitespace or find-and-replace keywords
    - if we change all instances of 'FL' to 'Florida' and 'CA' to 'California', then we are performing an update since these are two distinct operations
    - this is not a formatting task because there is no generally applicable format string that represents 'fully expanded state names'
    - furthermore, this is not a validation task because we are not considering all the values in the column, but instead just focusing on fixing a few specific instances
  * ambiguous: the user's request requires clarification because it might not be related to changing column contents at all
    - this is a catch-all option for any request that does not clearly fall into one of the above categories
    - a request may also be ambiguous if it involves multiple columns or multiple steps, in which case it represents a complex cleaning task

Please start by considering what exactly the user is trying to accomplish, then decide on the appropriate method.
Your entire response should be in well-formatted JSON including your thought (string) and the chosen method (token), with no further explanations after the JSON output.

For example,
---
_Conversation History_
User: I'm seeing some weird NPS scores in the customer satisfaction table.
Agent: What kind of issues are you seeing with the scores?
User: I need you to format the NPS score to be between 0 and 10?

_Output_
```json
{{
  "thought": "Even though the user is asking to 'format', the request is to constrain the NPS score to a specific range, so we are actually performing validation.",
  "method": "validate"
}}
```

_Conversation History_
User: I want to ensure good email deliverability for our VIP customers outreach.
Agent: That's a good idea. How do you want to define VIP customers?
User: Can you check to make sure that the email addresses for all the big spenders are correct?

_Output_
```json
{{
  "thought": "The user asking to perform validation that is not feasible since we don't have enough information to identify which customers are 'big spenders'",
  "method": "ambiguous"
}}
```

_Conversation History_
User: The CTR data is too messy for our performance dashboard.
Agent: I searched for outliers and found 10 rows with broken values. Should I remove them?
User: No, I just need to fix the formatting so that it has just 1 decimal place rather than of 3

_Output_
```json
{{
  "thought": "The user wants to update the precision of the CTR to be 1 decimal rather than 3, which can be done by simply rounding the values",
  "method": "update"
}}
```

_Conversation History_
User: Do we have any tracking information for when users first signed up?
Agent: Yes, we have a 'signup_timestamp' column as well as a 'first_login_time' column.
User: Can you fix the timestamps to be in the format of 'YYYY-MM-DD HH:MM:SS' without the T separator in the middle?

_Output_
```json
{{
  "thought": "While it is unclear which column the user is referring to, the request is clearly about standardizing the data to conform to a specific format.",
  "method": "format"
}}
```

_Conversation History_
User: I'm analyzing our recent promo, but I think there might be some months written incorrectly.
Agent: No problem, should I remove those rows? Any specific examples you are thinking of?
User: Well everything should be a valid month, but I see values like 'last month' or 'last quarter'

_Output_
```json
{{
  "thought": "The user wants to ensure all rows only include valid months, which is a validation task.",
  "method": "validate"
}}
```

_Conversation History_
User: How many users are there from each state?
Agent: We have 933 users from Pennsylvania, 487 from New York, and 130 from New Jersey. See table for more.
User: Change the format of the addresses so that the cities are properly capitalized rather than all lowercase or uppercase.

_Output_
```json
{{
  "thought": "The user is asking to 'format' an address, but capitalization of cities can be done simply by updating the values. Furthermore, locations are not a datatype supported by our format module.",
  "method": "update"
}}
```

_Conversation History_
User: Yea, go for it.
Agent: Great, I have just merged the two tables together covering leads from the trade shows and partner referrals.
User: I'm pretty sure I saw the same person multiple times now. Can we clean that up?

_Output_
```json
{{
  "thought": "The task is not about data validation, but rather about de-duplicating the repeated user entries.",
  "method": "ambiguous"
}}
```
---
Now it's your turn! Please decide on the method of cleaning based on the conversation history, choosing from the following options: [validate, update, format, ambiguous]
For additional context, here is a preview of the data:
{data_preview}

_Conversation History_
{history}

_Output_
"""

pattern_routing_prompt = """Given the conversation history, decide how the user is trying to fill in the missing values in a column.
For our purposes, the valid methods are:
  * pattern: the user is filling a column using a cumulative pattern starting from some base value
    - a pattern is when the value of a given row depends on the rows above it, such as a trailing sum or running total
    - another common use case is when the user wants to copy down the first non-empty value to fill any blank cells beneath it
    - this is also referred to as flash filling, where each cell is dependent on the previous cell above it
  * update: the user is changing existing values based on some formula
    - notably, the formula does not depend on the row above it, but rather on values within the row itself
    - the formula may also depend on external factors, such as a constant value defined by the user
  * impute: the user is filling in null or missing values within the column
    - once again, the formula should not depend on the row above it, but rather on values within the row itself or the surrounding area
    - a typical example is when the user wants to fill in the blanks by taking the median of the existing values in the column
    - this is considered imputation rather than a pattern because the new value depends on the rows above *and below* it
  * ambiguous: the user's request requires clarification, or the request might not actually be related to filling column contents at all
    - this is a catch-all option for any request that does not clearly fall into one of the above categories
    
Note that we are only concerned with the method of filling in the column, and not with its name or datatype.
Your response should only be in well-formatted JSON including your thought (string) and the chosen method (token), without any additional text or explanations.

For example,
---
_Conversation History_
User: I'm looking down the DeliveryDate column and I'm seeing a pattern of errors.
Agent: OK, we can look into that. What kind of pattern are you seeing?
User: Can you change every date to be one day later than what it currently is?

_Output_
```json
{{
  "thought": "Despite mentioned the word 'pattern', the user is updating date values based on the row itself, rather than the row above it.",
  "method": "update"
}}
```

_Conversation History_
User: let's go with the outliers
Agent: I can remove the outliers, or update them to a new value. What would you like to do?
User: Let's update the outliers to be the average of the two surrounding values.

_Output_
```json
{{
  "thought": "The request is ambiguous because it does not specify where the surrounding values are located.",
  "method": "ambiguous"
}}
```

_Conversation History_
User: What is the average number of clicks per day on the 'Personal Assessment' survey link?
Agent: The average number of clicks is 151.3. However, I noticed many blank rows which may affect the results, would you like to do something about it?
User: Can you copy down the first non-null value to fill those in?

_Output_
```json
{{
  "thought": "Copying down a non-null value to fill any blank cells beneath it is a clear example of a cumulative pattern.",
  "method": "pattern"
}}
```

_Conversation History_
User: We lost tracking data for a few days in March after the system upgrade, do you see it?
Agent: Yes, I see the gap in daily active users data from March 2nd to March 8th.
User: Can you smooth that out for the report by interpolating the missing values in between the two dates?

_Output_
```json
{{
  "thought": "The user is filling in the missing values by interpolating from the rows above and below it, which is a form of imputation.",
  "method": "impute"
}}
```

_Conversation History_
User: You see how we have lots of blanks in the final price column?
Agent: Yes, I can fill those in with the average price. Is that what you want?
User: The taxes and discounts should be applied to every item in inventory.

_Output_
```json
{{
  "thought": "Calculating the final price depends on the taxes and discounts in its own row, rather than an accumulated price.",
  "method": "update"
}}
```

_Conversation History_
User: Let's make a new column for costs as well that sums up all the cumulative totals.
Agent: I see two columns related to costs: cost_per_click and cost_of_goods. Which one should I use?
User: I want it to hold the sum of all the costs up to that point.

_Output_
```json
{{
  "thought": "Even though the exact column to update is unclear, the user is clearly looking to fill it using a cumulative pattern.",
  "method": "pattern"
}}
```

_Conversation History_
User: I need to clean up the data in the campaign column.
Agent: What kind of cleaning are you looking to do?
User: Can we go down the list and remove any duplicate campaigns?

_Output_
```json
{{
  "thought": "The task is about removing duplicates rather than filling a column with a pattern.",
  "method": "ambiguous"
}}
```
---
Now it's your turn to decide! Remember, the methods to choose from are 'pattern', 'update', 'impute', or 'ambiguous'.
For additional context, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

datetime_alignment_prompt = """Given the rows within a column, our task is to generate a Python function that checks if the values adhere to a specified datetime format.
Plausible formats include:
  * Date
    - %m/%d/%Y: 01/31/2022
    - %d/%m/%Y: 31/01/2022, changing the order
    - %-m/%-d/%Y: 1/31/2022, leading zeros are optional
    - %Y-%m-%d: 2022-01-31, changing the delimiter
    - %m/%d/%y: 01/31/22, using two-digit years
    - %m-%d: 01-31, only the month and day
    - %B %d, %Y: January 31, 2022, using the month name
    - %b. %d: Jan. 31, using the abbreviated month name
    - %B %o: January 31st, using the ordinal with a custom `%o` specifier
    - %d %B %y: 31 January 22, unusual but possible choice
  * Time
    - %H:%M:%S: 14:59:59
    - %H:%M: 14:30, omitting seconds
    - %I:%M: 02:30, using 12-hour format
    - %I:%M %p: 02:30 PM, including AM/PM
    - %I:%M %p-: 02:30 P, abbreviated AM/PM with a custom `%p-` specifier
    - %-I:%M %p: 2:30 PM, leading zero is optional
  * Timestamp
    - %Y-%m-%d %H:%M:%S: 2019-10-20 15:48:22
    - %Y-%m-%dT%H:%M:%SZ: 2019-10-20T15:48:22Z, with UTC indicator
    - %Y-%m-%dT%H:%M:%S.%f%z: 2019-10-20T15:48:22.000000+0500, with timezone offset
    - %Y-%m-%d %H:%M:%S.%f: 2019-10-20 15:48:22.600000, including microseconds
    - %m/%d/%Y %H:%M:%S: 10/20/2019 15:48:22, changing the delimiter
    - %m/%d/%Y %I:%M %p: 10/20/2019 3:48 PM, including AM/PM
    - %Y-%m-%dT%H:%M:%S: 2019-10-20T15:48:22, local time zone
  * Week
    - %A: Monday, Thurday; full weekday name
    - %a: Mon, Thu; abbreviated weekday name
    - %a.: Mon., Thu.; abbreviated with period
    - %U: 01, 52; week as a number starting on Sunday
    - %A, %B %o: Monday, April 1st; full date with ordinal suffix
  * Quarter
    - %q: Q1, Q2; quarter as a number
    - %Q: First Quarter, Second Quarter; quarter as a full name
    - %o: 1st, 2nd; quarter as an ordinal
    - %q %Y: Q1 2022, Q2 2022; quarter as a number with year

Start by developing a plan of action to validate each row, and then generate the Python function to perform the validation.
Each plan can follow the general template:
  1. If applicable, verify the length of the row
  2. If applicable, check for specific characters that should be present or absent
  3. Split the string into parts and count the number of parts
  4. Validate each part individually based on the expected format

The function is always named `is_format_aligned`, and returns a boolean value indicating whether the string is aligned to the target format.
Note that the environment has already imported:
  * `re`: for regular expressions
  * `dt`: for datetime operations
  * `pd`: for Pandas
  * `np`: for NumPy
  * `common_tlds`: a list of all top-level domains we care about, such as ['com', 'org', 'net', ...]

You do not need to import any other Python libraries because the function should be short and simple.
Your response should contain the plan and the function code, with no further text or explanations after the code block.

For example,
---
_Target_
Suppose the target format is: '%m/%d/%Y'

_Output_
Plan:
  1. Verify the length is exactly 10 characters
  2a. Split the input by '/' and ensure there are three parts
  2b. Assign each part to month, day, and year
  3. Check that month is a valid number from 01 to 12
  4. Check that day is a valid number from 01 to 31
  5. Check that year is a valid 4-digit number

Function:
```python
def is_format_aligned(date_str):
  if len(date_str) != 10:
    return False

  parts = date_str.split('/')
  if len(parts) != 3:
    return False

  month, day, year = map(int, parts)

  if not (1 <= month <= 12 and 1 <= day <= 31 and 1000 <= year <= 9999):
    return False
  return True
```

_Target_
Suppose the target format is: '%a.'

_Output_
Plan:
  1. Verify the length is exactly 4 characters
  2. Check that the string ends with a period, then remove it
  3. Check that the part is a valid day of the week abbreviation

Function:
```python
def is_format_aligned(week_str):
  if len(week_str) != 4:
    return False

  if week_str.endswith('.'):
    week = week_str.rstrip('.')
  else:
    return False

  if week not in ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']:
    return False

  return True
```

_Target_
Suppose the target format is: '%I:%M %p'

_Output_
Plan:
  1a. Split the input by space and ensure there are two parts
  1b. Assign to time and period
  2a. Split the time part by colon and ensure there are two parts
  2b. Assign to hours and minutes
  3. Check that hours is a valid number from 1 to 12, with no leading zeros
  4. Check that minutes is a valid number from 00 to 59
  5. Check that period is 'AM' or 'PM' after converting to uppercase

Function:
```python
def is_format_aligned(time_str):
  try:
    time, period = time_str.split()
  except ValueError:
    return False

  try:
    hours, minutes = map(int, time.split(':'))
  except ValueError:
    return False

  if not (1 <= hours <= 12 and 0 <= minutes <= 59):
    return False

  if period.upper() not in ['AM', 'PM']:
    return False

  return True
```

_Target_
Suppose the target format is: '%Y-%m-%dT%H:%M:%SZ'

_Output_
Plan:
  1. Verify the length is exactly 20 characters
  2. Verify that the string ends with 'Z', and then remove it
  3a. Split the string by 'T' and ensure there are two parts
  3b. Assign to date and time
  4a. Split the date by '-' and ensure there are three parts
  4b. Assign to year, month, and day
  5a. Split the time by ':' and ensure there are three parts
  5b. Assign to hours, minutes, and seconds
  6. Check that year is a valid 4-digit number
  7. Check that month is a valid number from 01 to 12
  8. Check that day is a valid number from 01 to 31
  9. Check that hours is a valid number from 00 to 23
  10. Check that minutes is a valid number from 00 to 59
  11. Check that seconds is a valid number from 00 to 59

Function:
```python
def is_format_aligned(timestamp_str):
  if len(timestamp_str) != 20:
    return False

  if timestamp_str.endswith('Z'):
    timestamp_str = timestamp_str[:-1]
  else:
    return False

  try:
    date, time = timestamp_str.split('T')
  except ValueError:
    return False

  try:
    year, month, day = map(int, date.split('-'))
    hours, minutes, seconds = map(int, time.split(':'))
  except ValueError:
    return False
  
  if not (1000 <= year <= 9999 and 1 <= month <= 12 and 1 <= day <= 31 and 0 <= hours <= 23 and 0 <= minutes <= 59 and 0 <= seconds <= 59):
    return False

  return True
```

_Target_
Suppose the target format is: '%B %d, %Y'

_Output_
Plan:
  1a. Split the input by space and ensure there are three parts
  1b. Assign each part to month, day, and year
  2. Verify that the day ends with a comma, and then remove it
  3. Check that month is a valid month name from January to December
  4. Check that day is a valid number from 1 to 31
  5. Check that year is a valid 4-digit number

Function:
```python
def is_format_aligned(date_str):
  parts = date_str.split()
  if len(parts) != 3:
    return False

  month, day, year = parts
  
  if day.endswith(','):
    day = day.rstrip(',')
  else:
    return False

  if month not in ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']:
    return False

  if not (1 <= int(day) <= 31 and 1000 <= int(year) <= 9999):
    return False

  return True
```
---
Now it's your turn to design a plan and function. The function should be directly executable without any additional text after the code block.

_Target_
The target format is: '{target_format}'

_Output_
"""

textual_alignment_prompt = """Given the rows within a column, our task is to generate a Python function that checks if the values adhere to a specified text format.
Plausible formats include:
  * Phone
    - '(XXX) XXX-XXXX': (*************
    - 'XXX-XXX-XXXX': ************, using hyphens
    - 'XXX.XXX.XXXX': ************, using periods
    - 'XXX XXX XXXX': ************, using spaces
    - '+X (XXX) XXX-XXXX': +1 (*************, including country code
    - 'X(XXX)XXX-XXXX': 1(650)783-4412, no space after the country code
  * Email
    - '<EMAIL>': <EMAIL>
    - 'username@domain': donotreply@soleda, without the top-level domain
    - 'username': donotreply, only the username
  * URL
    - 'protocol://subdomain.domain.tld/page': https://www.example.com/login
    - 'protocol://subdomain.domain.tld/page?query': https://www.example.com/login?user=1, allows for query parameters
    - 'subdomain.domain.tld/page': www.example.com/login, without the protocol
    - 'subdomain.domain.tld': www.example.com/login, stripped of the page and protocol
    - 'protocol://subdomain.domain.tld': http://www.example.com, with the protocol
    - 'domain.tld': example.com, without the subdomain
    - 'subdomain.domain': www.example, possible but usual option of excluding the top-level domain
  * Name
    - 'first last': Scott Lee
    - 'last first': Lee, Scott, with the last name at the beginning
    - 'last, first': Lee, Scott, with a comma
    - 'first middle last': Scott Hyung Lee, with a middle name
    - 'last first middle': Lee, Scott Hyung, with a middle name at the end
    - 'honorific first last': Dr. Scott Lee, with a title
    - 'first last suffix': Scott Lee Jr., with a suffix
  * Currency
    - '$XX.XX': $12.99, with a dollar sign and two decimal places
    - '€XX.XX': €12.99, with an euro sign and two decimal places
    - 'XX.XX': 12.99, without any currency symbol
    - 'XX,XX': 12,99, with a comma instead of a period
    - '$XX,XXX': $12,789, rounded to the nearest dollar ignoring the cents

Start by developing a plan of action to validate each row, and then generate the Python function to perform the validation.
Each plan can follow the general template:
  1. If applicable, verify the length of the row
  2. If applicable, check for specific characters that should be present or absent
  3. Split the string into parts and count the number of parts
  4. Validate each part individually based on the expected format

The function is always named `is_format_aligned`, and returns a boolean value indicating whether the string is aligned to the target format.
Note that the environment has already imported:
  * `re`: for regular expressions
  * `dt`: for datetime operations
  * `pd`: for Pandas
  * `np`: for NumPy
  * `common_tlds`: a list of all top-level domains we care about, such as ['com', 'org', 'net', ...]

You do not need to import any other Python libraries because the function should be short and simple.
Your response should contain the Plan and the Function code, with no further text or explanations after the code block.
Be sure to follow the structure by including the phrases "Plan:" and "Function:" in your response.

For example,
---
## Email Example
Suppose the target format is: '<EMAIL>'

_Output_
Plan:
  1a. Split by '@' and ensure there are two parts
  1b. Assign to username and domain
  2a. Split domain by '.' and ensure there are two parts
  2b. Assign to domain and tld
  3. Check that username and domain are alphanumeric
  4. Check that tld is a common top-level domain

Function:
```python
def is_format_aligned(email_str):
  try:
    username, domain = email_str.split('@')
    domain, tld = domain.split('.')
  except ValueError:
    return False

  if not (username.isalnum() and domain.isalnum() and tld in common_tlds):
    return False

  return True
```

## URL Example
Suppose the target format is: 'protocol://subdomain.domain.tld/page?query'
This simply means we want a generically valid URL. The query string is optional.

_Output_
Plan:
  1a. Split by '://' and ensure there are two parts
  1b. Assign to protocol and rest
  2a. Split rest by '/', but allow for no '/' to be present
  2b. The first part is the domain
  2c. The rest (if any) is the path and query
  3a. Split domain by '.' and ensure there are at least two parts
  3b. The last two parts are the domain and TLD
  3c. Any parts before that (if present) are subdomains
  4. If there's a path and query:
  4a. Split by '?', but allow for no '?' to be present
  4b. The part before '?' (if present) is the path
  4c. The part after '?' (if present) is the query
  5. Check that protocol is 'http' or 'https'
  6. Check that domain parts are valid (alphanumeric, hyphens allowed but not at start/end)
  7. Check that TLD is a common top-level domain
  8. If path is present, check that it doesn't contain spaces and is valid
  9. If query is present, check that it's a valid query string

Function:
```python
def is_format_aligned(url_str):
  protocol, rest = url_str.split('://', 1)
  
  if protocol not in ['http', 'https']:
      return False
  
  parts = rest.split('/', 1)
  domain = parts[0]
  path_query = parts[1] if len(parts) > 1 else ''
  
  domain_parts = domain.split('.')
  if len(domain_parts) < 2:
      return False
  
  tld = domain_parts[-1]
  domain = domain_parts[-2]
  subdomains = domain_parts[:-2]
  
  for part in [domain] + subdomains:
    if not re.match(r'^[a-zA-Z0-9](?:[a-zA-Z0-9-]{{0,61}}[a-zA-Z0-9])?$', part):
      return False

  if tld not in common_tlds:
      return False
  
  if path_query:
    if '?' in path_query:
      path, query = path_query.split('?', 1)
      if not re.match(r'^[a-zA-Z0-9-._~!$&\'()*+,;=:@/?%]*$', query):
        return False
    else:
      path = path_query
    
    if path and not re.match(r'^[a-zA-Z0-9-._~!$&\'()*+,;=:@/%]*$', path):
      return False
  
  return True
```

## Phone Example
Suppose the target format is: 'X(XXX)XXX-XXXX'

_Output_
Plan:
  1. Verify the length is exactly 14 characters
  2. Replace '(' and ')' with '-'
  3a. Split by '-' and ensure there are four parts
  3b. Assign to country, area, exchange, and subscriber
  4. Check that country is '1'
  5. Check that area, exchange, and subscriber are valid numbers

Function:
```python
def is_format_aligned(phone_str):
  if len(phone_str) != 14:
    return False

  phone_str = phone_str.replace('(', '-').replace(')', '-')
  parts = phone_str.split('-')
  if len(parts) != 4:
    return False

  country, area, exchange, subscriber = parts

  if country != '1':
    return False

  if not (area.isdigit() and exchange.isdigit() and subscriber.isdigit()):
    return False

  return True
```

## Name Example
Suppose the target format is: 'last, first'

_Output_
Plan:
  1a. Split by ',' and ensure there are two parts
  1b. Assign to last and first
  2. Strip leading and trailing spaces
  3. Check that last is alphabetical, hyphen, or apostrophe, but no spaces
  4. Check that first is alphabetical, hyphen, apostrophe, or space

Function:
```python
def is_format_aligned(name_str):
  try:
    last, first = name_str.split(',')
  except ValueError:
    return False

  first = first.strip()

  last_pattern = re.compile(r'^[a-zA-Z\-\']+$')
  first_pattern = re.compile(r'^[a-zA-Z\-\' ]+$')

  if not (last_pattern.match(last) and first_pattern.match(first)):
    return False

  return True
```

## URL Example
Suppose the target format is: 'subdomain.domain.tld/page'

_Output_
Plan:
  1. Ensure '://' and '?' are not present
  2a. Split rest by '/', but allow for no '/' to be present
  2b. The first part is the domain
  2c. The rest (if any) is the path
  3a. Split domain by '.' and ensure there are at least two parts
  3b. The last two parts are the domain and TLD
  3c. Any parts before that (if present) are subdomains
  4. Ensure subdomain is alphanumeric only
  5. Ensure domain and subdomain are valid
  6. Ensure TLD is a common top-level domain
  7. If path is present, check that is doesn't contain spaces and is valid

Function:
```python
def is_format_aligned(url_str):
  if '://' in url_str or '?' in url_str:
    return False

  parts = url_str.split('/', 1)
  domain = parts[0]
  path = parts[1] if len(parts) > 1 else ''

  domain_parts = domain.split('.')
  if len(domain_parts) < 2:
    return False

  tld = domain_parts[-1]
  domain = domain_parts[-2]
  subdomains = domain_parts[:-2]

  for subdomain in subdomains:
    if not subdomain.isalnum():
      return False
    
  domain_regex = r'^[a-zA-Z0-9](?:[a-zA-Z0-9-]{{0,61}}[a-zA-Z0-9])?$'
  if not re.match(domain_regex, domain):
    return False
  for subdomain in subdomains:
    if not re.match(domain_regex, subdomain):
      return False

  if tld not in common_tlds:
    return False

  if path:
    if ' ' in path:
      return False
    path_regex = r'^[a-zA-Z0-9-._~!$&\'()*+,;=:@/%]*$'
    if not re.match(path_regex, path):
      return False

  return True
```
---
## Current Scenario
Now it's your turn to design a plan and function. The function should be directly executable without any additional text after the code block.
The target format is: '{target_format}'

_Output_
"""

first_batch_sentinel_msg = """We are currently on the first round, so you should NOT use the sentinel string '<UNRESOLVABLE>' at all!
The next round will provide a better sense of the remaining issues, so it's better to wait before marking any values as unresolvable."""

second_batch_sentinel_msg = """If a value has no possible resolution, you can manually set its value to the sentinel string '<UNRESOLVABLE>' to remove it from further consideration.
Do NOT try to identify unresolvable values in bulk (ie. using a mask); you must mark them one at a time through manual inspection.
Be *very* conservative in using the sentinel because the next round might reveal more information that allows you to resolve the value."""

reformat_textual_prompt = """In an effort to standardize data, we often encounter conflicting formats within a column.
Given a sample of the rows within a column, your task is to generate a Python function that reformats the values into the specified format.
Plausible formats include:
  * Phone
    - '(XXX) XXX-XXXX': (*************
    - 'XXX-XXX-XXXX': ************, using hyphens
    - 'XXX.XXX.XXXX': ************, using periods
    - 'XXX XXX XXXX': ************, using spaces
    - '+X (XXX) XXX-XXXX': +1 (*************, including country code
    - 'X(XXX)XXX-XXXX': 1(650)783-4412, no space after the country code
  * Email
    - '<EMAIL>': <EMAIL>
    - 'username@domain': donotreply@soleda, without the top-level domain
    - 'username': donotreply, only the username
  * URL
    - 'protocol://subdomain.domain.tld/page': https://www.example.com/login
    - 'protocol://subdomain.domain.tld/page?query': https://www.example.com/login?user=1, allows for query parameters
    - 'subdomain.domain.tld/page': www.example.com/login, without the protocol
    - 'subdomain.domain.tld': www.example.com/login, stripped of the page and protocol
    - 'protocol://subdomain.domain.tld': http://www.example.com, with the protocol
    - 'domain.tld': example.com, without the subdomain
    - 'subdomain.domain': www.example, possible but usual option of excluding the top-level domain
  * Name
    - 'first last': Scott Lee
    - 'last first': Lee, Scott, with the last name at the beginning
    - 'last, first': Lee, Scott, with a comma
    - 'first middle last': Scott Hyung Lee, with a middle name
    - 'last first middle': Lee, Scott Hyung, with a middle name at the end
    - 'honorific first last': Dr. Scott Lee, with a title
    - 'first last suffix': Scott Lee Jr., with a suffix
  * Currency
    - '$XX.XX': $12.99, with a dollar sign and two decimal places
    - '€XX.XX': €12.99, with an euro sign and two decimal places
    - 'XX.XX': 12.99, without any currency symbol
    - 'XX,XX': 12,99, with a comma instead of a period
    - '$XX,XXX': $12,789, rounded to the nearest dollar ignoring the cents

Start by developing a plan of action to reformat problematic rows, and then generate the Python code to do so.
Specifically, please follow this 3-step process:
  1. Thoughts: Identify the most common formatting error among the sampled rows. Focus on making incremental progress, rather than trying to resolve everything at once.
  2. Fix: Determine how you can fix a handful of errors to align with the target format. You should fix at most three errors in each round.
  3. Function: Write a function that reformats an entire column by correcting the most prevalent errors into the desired format.

Your function is always named `reformat_col`, and should take a pandas Series as input and return an updated version as the output. Process the column directly rather than making a copy.
Your function should be relatively concise (< 32 lines) since it does not need to resolve all issues at once. We will iteratively go through multiple rounds.
{unresolvable_msg}

Note that the environment has already imported:
  * `re`: for regular expressions
  * `pd`: for Pandas
  * `np`: for NumPy
  * `common_tlds`: a list of all top-level domains we care about, such as ['com', 'org', 'net', ...]

You do not need to import any other Python libraries because the function should be short and simple.
Your view of the data is limited to the sampled rows provided. Do not make any assumptions about the overall distribution of the data.
Your final response should contain thoughts on the error (Thoughts), a plan for fixing the error (Fix), and the function code (Function), with no further explanations after the code block.

For example,
---
## Phone Example
Suppose the target format is: 'XXX-XXX-XXXX'
Samples of correctly formatted rows: ['************', '************', '************', '************', '************']
Samples of conflicting rows:
'************ '
'************ '
'916 -745-2190 '
'415-672'
'**************'
'************     '
'**************'
'************ '
'************'
'**************'
'************ '
'628 - 359 - 7146'
'************ '
' ************'
'**************'
'************ '

_Output_
Thoughts: The most common error is the presence of extra spaces, including trailing spaces surrounding the phone number.
Fix: I can remove extra spaces using the strip() function followed by replacing internal spaces with empty strings.
Function:
```python
def reformat_col(phone_series):
  # Remove all whitespace
  cleaned = phone_series.str.strip().str.replace(r'\s+', '', regex=True)
  # Remove leading country code if present
  cleaned = cleaned.str.replace(r'^1-?', '', regex=True)
  # Add hyphens for 10-digit numbers
  formatted = cleaned.str.replace(r'^(\d{{3}})(\d{{3}})(\d{{4}}), r'\1-\2-\3', regex=True)
  # Mark as unresolvable since we don't even have seven digits
  formatted = formatted.str.replace('415-672', '<UNRESOLVABLE>', regex=False)
  return formatted
```

## URL Example
Suppose the target format is: 'subdomain.domain.tld'
Samples of correctly formatted rows: ['www.caranddriver.com', 'www.oregonstate.gov', 'store.fancydresses.com', 'mail.columbia.edu', 'www.soleda.ai']
Samples of conflicting rows:
'https://www.sugarrushcandy.com'
'www.toyboxtreasures.net/cart'
'https://www.gummybearland.org'
' www.lollipopplayland.com'
'http://www.teddybeartoys.store'
'www.chocolatewonderland. co'
'http://www.bubblegumbonanza.biz'
'http://www.toytrain-express.com'
'http://www.candycrushstore.net'
'www.sweet tooth heaven.org'
'https://www.puzzlesandpeppermints.com'
'www.build_a blockstoys.net'
' www.marshmallowmadness.store'
'www.dollhousedelights.biz '
'http://www.jellybeanjunction.co'
'https://www.licoriceland.org'

_Output_
Thoughts: The most common error is the presence of the protocol 'http://' or 'https://' at the beginning of the URL.
Fix: I can remove the protocol from the URL and unnecessary spaces. We can deal with the remaining issues in a later round.
Function:
```python
def reformat_col(url_series):
  # Remove whitespace
  cleaned = url_series.str.strip()
  # Remove protocol (http:// or https://)
  cleaned = cleaned.str.replace(r'^https?://', '', regex=True)
  # Remove spaces and underscores in domain names
  cleaned = cleaned.str.replace(r'[\s_]+', '', regex=True)
  # Fix spacing in TLD (like '. co' -> '.co')
  cleaned = cleaned.str.replace(r'\.\s+', '.', regex=True)
  return cleaned
```

## Email Example
Suppose the target format is: '<EMAIL>'
Samples of correctly formatted rows: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
Samples of conflicting rows:
'erichard186@ yahoo.com'
'<EMAIL>'
'brockjohnsonbiomechanicslab.app'
'officenorth @ company.net'
'lindab112collisionrepair.com'
'<EMAIL>'
'<EMAIL>'
'mygreatnation1997collisionrepair.com'
'<EMAIL>'
'mackaypushgmail.com'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
'<EMAIL>'
' <EMAIL>'
' <EMAIL>'
'<EMAIL>'

_Output_
Thoughts: The most common error is OCR type errors for the top-level domain. Some emails are also missing the '@' symbol.
Fix: I can replace 'conn' with 'com'  and 'neT' with 'net'. I must be careful when inserting the '@' symbol since naively placing it before a period will lead to incorrect domains.
Function:
```python
def reformat_col(email_series):
  # Remove whitespace
  cleaned = email_series.str.strip()
  # Remove spaces around @ symbol
  cleaned = cleaned.str.replace(r'\s*@\s*', '@', regex=True)
  # Fix common TLD typos
  cleaned = cleaned.str.replace(r'\.conn$', '.com', regex=True)
  cleaned = cleaned.str.replace(r'\.neT$', '.net', regex=True)
  # Fill in missing @ symbol given the domain
  cleaned = cleaned.str.replace(r'(\w+)gmail\.com', r'\<EMAIL>', regex=True)
  cleaned = cleaned.str.replace(r'(\w+)collisionrepair\.com', r'\<EMAIL>', regex=True)
  cleaned = cleaned.str.replace(r'(\w+)biomechanicslab\.app', r'\<EMAIL>', regex=True)
  return cleaned
```

## Phone Example
Suppose the target format is: 'X(XXX)XXX-XXXX'
Samples of correctly formatted rows: ['1(650)783-4412', '1(212)472-6531', '1(650)483-1045', '1(650)498-3698', '1(650)558-7214']
Samples of conflicting rows:
'(415)289-7623'
'(628)931-5084'
'(916)745-2190'
'**************'
'(916)584-3021'
'**************'
'**************'
'1(628)403-8176 '
'6285140987'
'(916)238-7465'
'(916)721-4058 '
'(628)359-7146'
'**************'
' 1(628)194-5732'
'(916)952-8047'
'(916)480-3625'

_Output_
Thoughts: The most common error is the missing country code '1' at the beginning of the phone number.
Fix: I can add the country code to the phone number when it is missing.
Function:
```python
def reformat_col(phone_series):
  # Remove whitespace
  cleaned = phone_series.str.strip()
  # Remove all separators to get raw digits
  digits_only = cleaned.str.replace(r'[\s\-\(\)]+', '', regex=True)
  # Add country code if missing (10 digit numbers)
  needs_country = digits_only.str.len() == 10
  digits_only.loc[needs_country] = '1' + digits_only.loc[needs_country]
  # Format as X(XXX)XXX-XXXX
  formatted = digits_only.str.replace(r'^(\d{{1}})(\d{{3}})(\d{{3}})(\d{{4}})$', r'\1(\2)\3-\4', regex=True)
  return formatted
```

## Name Example
Suppose the target format is: 'first last suffix'
Samples of correctly formatted rows: ['Scott Lee Jr.', 'Alice Kensington', 'John Harrington', 'Emily Brown', 'David White II']
Samples of conflicting rows:
'Peter Garea Senior'
' Ayesha Dillon'
'Test Testerson '
'Tutt Mrasek JR'
'Gerard Fanet SR'
'Guan Hsing Hua'
'Joseph Moore the second'
'Luke Campbell Senior'
'Christopher Nakamura the third'
'Logan Lee Price'
'Alexander Ward (III)'
'Anthony Wilson Junior'
'Helen Parker '
'Thomas Harris JR'
'Thomas Campbell Senior'
'RichardDavis'

_Output_
Thoughts: The most common error is the inconsistent use of suffixes, such as Junior rather than Jr.
Fix: I can replace the suffixes with the standardized format.
Function:
```python
def reformat_col(name_series):
  # Remove leading/trailing whitespace
  cleaned = name_series.str.strip()
  # Fix spacing issues (like 'RichardDavis' -> 'Richard Davis')
  cleaned = cleaned.str.replace(r'([a-z])([A-Z])', r'\1 \2', regex=True)
  # Standardize suffixes
  cleaned = cleaned.str.replace(r'\bSenior\b', 'Sr.', regex=True)
  cleaned = cleaned.str.replace(r'\bJunior\b', 'Jr.', regex=True)
  cleaned = cleaned.str.replace(r'\bSR\b', 'Sr.', regex=True)
  cleaned = cleaned.str.replace(r'\bJR\b', 'Jr.', regex=True)
  cleaned = cleaned.str.replace(r'\bthe second\b', 'II', regex=True)
  cleaned = cleaned.str.replace(r'\bthe third\b', 'III', regex=True)
  cleaned = cleaned.str.replace(r'\(III\)', 'III', regex=True)
  # Mark obviously fake names as unresolvable
  cleaned = cleaned.str.replace('Test Testerson', '<UNRESOLVABLE>', regex=False)
  return cleaned
```
---
## Counter Example
This is an example of what *not* to do when writing the function.
Suppose the target format is: '<EMAIL>'
Samples of conflicting rows:
'sanchezdevin117machinemetrics.tech'
'josephmorse61@'
'lonniertransactwise.com'
'gregorytrack@'
'mckenziewsendwise.io'
'javierrauditstream.com'
'alonzogomezfraudguard.net'
'mackluke4@'

_Output_
Thoughts: The most common error is the missing @ symbol. The second most common error is the missing domain and TLD.
Fix: The code below shows mistakes that should be avoided.
Function:
```python
def reformat_col(email_series):
  # Make a copy to avoid modifying the original
  cleaned = email_series.copy()                   # DO NOT MAKE A COPY!
  # Add missing @ symbol
  cleaned = cleaned.str.replace(r'([a-zA-Z0-9]+)([a-zA-Z0-9\-]+\.[a-zA-Z]+)$', r'\1@\2', regex=True)   # INCORRECT!
  # Add missing domain and TLD
  missing_domain_mask = ~cleaned.str.endswith('@')
  email_series.loc[missing_domain_mask] = email_series.loc[missing_domain_mask] + 'company.com'   # INCORRECT!
```

The problem with the first fix is that it makes the false assumption the input column only contains the conflicting email address.
So previously correct emails such as '<EMAIL>' will now become broken as 'maxhalloway@<EMAIL>'.
In reality, any changes to the series will affect *all* rows in the column, so changes must be done with care.

Furthermore, the regex incorrectly assumes that the domain can be just one character long.
Thus, 'mckenziewsendwise.io' becomes '<EMAIL>' which is technically valid, but not what we want.
Instead, the more likely correct email is '<EMAIL>', so the correct line would be
`cleaned = cleaned.str.replace(r'(\w+)sendwise\.io', r'\<EMAIL>', regex=True)`

The problem with the second fix is that it makes ungrounded assumptions about the missing domain and TLD.
If something is unknown, then it should be individually marked as unresolvable rather than making any assumptions.
---
## Current Scenario
Now it's your turn to design a plan and function. Remember, the function should not make any copies of the target column, and should be relatively concise.
Unresolvable values must *not* be identified with in bulk with regex, and instead should be handled on a case-by-case basis.
For additional context, the recent conversation history is:
{history}

The target format is: '{target}'
Samples of correctly formatted rows: {correct}
Samples of conflicting rows:
{conflicts}

_Output_
"""

reformat_datetime_prompt = """In an effort to standardize data, we often encounter conflicting formats within a column.
Given a sample of the rows within a column, your task is to generate a Python function that reformats the values into the proper Pandas dtypes.
For our purposes, the standardized format for:
  * 'date' is '%Y-%m-%d', which is stored as a datetime64[ns] dtype
  * 'month' is an integer from 1 to 12, representing January to December, which is stored as an int64 dtype
  * 'quarter' is a decimal that optionally includes a year information, which is stored as a float64 dtype
    - For example, 2024.0 represents the first quarter of 2024, while 2022.75 represents the fourth quarter of 2022
    - If the year is not provided, then it should be set to 0 as a placeholder. Thus, 0.25 represents the second quarter with no year information
  * 'time' is '%H:%M:%S', which is stored as a datetime64[ns] dtype
  * 'timestamp' is '%Y-%m-%dT%H:%M:%SZ', which is stored as a datetime64[ns] dtype
  * 'week' is an integer from 1 to 7, representing Sunday to Saturday, which is stored as an int64 dtype

Start by developing a plan of action to reformat problematic rows, and then generate the Python code to do so.
Specifically, please follow this 3-step process:
  1. Thoughts: Identify the most common formatting error among the sampled rows. Focus on making incremental progress, rather than trying to resolve everything at once.
  2. Fix: Determine how to convert the conflicting rows into the target datatype. Be sure not to accidentally affect other rows in the process.
  3. Function: Write a function called `reformat_col` that reformats the problematic rows within the series.

Your function will take a pandas Series as input, representing the subset of the column that contains conflicting rows.
Process the column directly rather than making a copy. The output should contain exactly the same number of rows as the input.
Your function should be relatively concise (< 32 lines) since it does not need to resolve all issues at once. We will iteratively go through multiple rounds.
{unresolvable_msg}

Note that the environment has already imported:
  * `re`: for regular expressions
  * `dt`: for datetime operations
  * `pd`: for Pandas
  * `np`: for NumPy

You do not need to import any other Python libraries because the function should be short and simple.
When writing your function, keep in mind that there may be additional rows that are not shown in the sampled data.
Thus, utilize masks carefully to only match the rows you intended in order to limit the changes.
Your final response should contain thoughts on the error (Thoughts), a plan for fixing the error (Fix), and the function code (Function), with no further explanations after the code block.

For example,
---
Suppose the target datatype is: 'date'
Samples of correctly formatted rows: ['2022-03-25', '2022-03-28', '2022-03-31', '2022-04-03', '2022-03-17']
Samples of conflicting rows:
'2022-03-14 00:00:00'
'2022-04-02 00:00:01'
'2022-03-29 00:00:00'
'2022-04-17 00:00:00'
'2022-03-08 00:00:00'
'2022-04-25 12:00:00'
' 2022-03-21 00:00:00'
'2022-04-09 00:00:00'
'2022-03-03 00:00:00'
' 2022-04-30 00:00:00'
' 2022-03-18 00:00:00'
'2022-04-13 01:00:00'
'2022-03-26 00:00:00'
' 2022-04-06 00:00:00'
'2022-03-11 03:00:00'
'2022-04-22 00:00:00'

_Output_
Thoughts: The most common error is including times with the date. Some dates also have a leading space.
Fix: We can remove the leading space, and then convert to a date using the proper format string.
Function:
```python
def reformat_col(date_series):
  # Remove whitespace
  cleaned = date_series.str.lstrip()
  # Do our best to limit the changes to only the conflicting rows
  has_time_match = cleaned.str.match(r'^\d{{4}}-\d{{2}}-\d{{2}} (\d{{2}}):(\d{{2}}):(\d{{2}})')
  cleaned.loc[has_time_match] = pd.to_datetime(cleaned.loc[has_time_match], format='%Y-%m-%d %H:%M:%S')
  return cleaned  
```

Suppose the target datatype is: 'time'
Samples of correctly formatted rows: ['14:49:00', '09:30:00', '18:36:00', '11:22:00', '23:20:00']
Samples of conflicting rows:
'02:49 PM'
'05:09 AM'
'18:36:22'
'15:48:21'
'midnight'
'10:45 PM'
'08:36 AM'
'23:20:01'
'02:30:00'
'03:48 PM'
'11:03 PM'
'afternoon'
'10:22 AM'
'02:30 PM'
'06:45 AM'
'08:36 PM'

_Output_
Thoughts: The most common error is including the AM/PM indicator. There are also some times that include seconds. I can deal with the textual errors later.
Fix: I can convert the AM/PM times to 24-hour format, and then recognize the times with seconds using '%H:%M:%S' format.
Function:
```python
def reformat_col(time_series):
  # Fix conflicting rows containing AM/PM format
  am_pm_match = time_series.str.match(r'^\d{{1,2}}:\d{{2}} [AP]M$')
  time_series.loc[am_pm_match] = pd.to_datetime(time_series.loc[am_pm_match], format='%I:%M %p')
  # Fix conflicting rows containing seconds
  seconds_match = time_series.str.match(r'^\d{{1,2}}:\d{{2}}:\d{{2}}$')
  time_series.loc[seconds_match] = pd.to_datetime(time_series.loc[seconds_match], format='%H:%M:%S')
  return time_series.dt.strftime('%H:%M:%S')
```

Suppose the target datatype is: 'week'
Samples of correctly formatted rows: [5, 4, 1, 5, 2, 3]
Samples of conflicting rows:
'Wendsday'
'Thurs'
'Fri day'
'Thurs'
'Fri day'
'Thurs'
'Tue sday'
'Thurs day'

_Output_
Thoughts: There are some spelling errors and also spacing issues.
Fix: I can fix the spacing issues first, and then use a mapping to convert the names to integers.
Function:
```python
def reformat_col(week_series):
  # Remove whitespace
  cleaned = week_series.str.replace(r'\s+', '', regex=True)
  # Mapping for spelling errors
  revision_map = {{'Wendsday': 'Wednesday', 'Thurs': 'Thursday'}}
  for typo, correct in revision_map.items():
    cleaned = cleaned.str.replace(typo, correct, regex=True)

  # List for converting names to integers
  day_to_int = {{'Monday': 1, 'Tuesday': 2, 'Wednesday': 3, 'Thursday': 4, 'Friday': 5, 'Saturday': 6, 'Sunday': 7}}
  cleaned = cleaned.map(day_to_int)
  return cleaned
```

Suppose the target datatype is: 'quarter'
Samples of correctly formatted rows: [2019.25, 2019.75, 2019.0, 2019.25, 2019.5, 2019.25]
Samples of conflicting rows:
'Q1'
'Q2'
'Q3-2019 '
'Q3'
'Q4'
'Q1'
'Q2-2019 '
'Q1'
'Q2'
'15'
'Q3'
'Q4'
'Q3 '
'Q1-2019 '
'Q2'

_Output_
Thoughts: The most common error is that the dates are missing a year. The other error seems to be trailing spaces.
Fix: I would normally default to no year, but all the other dates have a year, so it is safe to assume the missing year is 2019. Spaces can be removed with the strip() function.
Function:
```python
def reformat_col(quarter_series):
  # Remove whitespace
  cleaned = quarter_series.str.strip()
  # Add missing year (2019) to QX format
  needs_year = cleaned.str.match(r'^Q\d$')
  cleaned.loc[needs_year] = cleaned.loc[needs_year] + '-2019'
  # Convert to decimal format
  def quarter_to_decimal(q_str):
    match = re.match(r'^Q([1-4])[-\s]?(\d{{4}})$', q_str)    # very strict regex to prevent false positives
    if match:
      quarter, year = int(match.group(1)), int(match.group(2))
      return year + (quarter - 1) * 0.25
    return q_str
  cleaned = cleaned.apply(quarter_to_decimal)
  return cleaned
```

Suppose the target datatype is: 'timestamp'
Samples of correctly formatted rows: ['2024-07-12T09:34:27Z', '2024-08-03T14:52:09Z', '2024-06-25T18:07:53Z', '2024-07-29T03:41:16Z', '2024-08-17T22:29:45Z']
Samples of conflicting rows:
'2024-08-31T17:29:03Z '
' 2024-09-08T19:42:18Z'
'2024-07-09T06:12:48Z '
'2024-08-05T12:40:19Z '
'Labor Day'
'2024-06-30T07:59:02Z '
'2024-08-14 04:03:46'
'2024-08-26T13:10:57'
'2024-08-09T16:23:51Z '
'2024-06-28T02:37:55Z '
' 2024-09-18T21:54:36Z'
' 2024-09-15T20:36:04Z'
'2024-09-01T11:15:38Z '
' 2024-07-05T01:27:39Z'
'2024-07-21T05:48:33Z '
'2024-07-18 23:17:05'

_Output_
Thoughts: The most common error is the presence of leading or trailing spaces surrounding the timestamp.
Fix: I can eliminate extra spaces using the strip() function. Some dates might have to be manually corrected.
Function:
```python
def reformat_col(timestamp_series):
  # Remove whitespace
  cleaned = timestamp_series.str.strip()
  # Convert space-separated format to T format
  cleaned = cleaned.str.replace(r'^(\d{{4}}-\d{{2}}-\d{{2}}) (\d{{2}}:\d{{2}}:\d{{2}})$', r'\1T\2Z', regex=True)
  # Add missing Z suffix
  needs_z = cleaned.str.match(r'^\d{{4}}-\d{{2}}-\d{{2}}T\d{{2}}:\d{{2}}:\d{{2}}$')
  cleaned.loc[needs_z] = cleaned.loc[needs_z] + 'Z'
  # Labor Day in 2024 falls on September 2nd
  cleaned = cleaned.str.replace('Labor Day', '2024-09-02T00:00:00Z', regex=False)
  return cleaned
```
---
## Current Scenario
Now it's your turn to design a plan and function. The function should not make any copies of the target column, and should be relatively concise.
Unresolvable values must *not* be identified with in bulk with regex, and instead should be handled on a case-by-case basis.

The target datatype is: '{datatype}'
Samples of correctly formatted rows: {correct}
Samples of conflicting rows:
{conflicts}

_Output_
"""

conflict_resolution_prompt = """In an attempt to {goal}, we have encountered conflicts we are unable to resolve, so we need to ask the user for feedback.
Given the conversation history and specific task, you should come up with a concise and informative clarification question to ask the user.
Your final output should be in well-structured JSON with keys for thought (string) and question (string), and no additional information or explanations afterwards.

For example,
---
_Conversation_
User: See how we have a bunch of misaligned dates in the ArrivalDate column?
Agent: Yes, I noticed that. How would you like to clean them up?
User: Could you make sure they are all in MM/DD/YYYY shape?

_Task_
Reformat the dates into the 'MM/DD/YYYY' format
'tomorrow'
'tomorrow'
'next week'
'04/23.2022'
'Tomorrow'

_Output_
```json
{{
  "thought": "If I am given the present date, I might be able to resolve the conflicts around relative dates.",
  "question": "There are some entries like 'tomorrow' or 'Tomorrow' which are not specific dates. If you tell me the current date, I can possibly convert them. Would you like to proceed with this approach?"
}}
```

_Conversation_
User: There is a lot of messy phone number data that needs to be standardized.
Agent: What format would you like the phone numbers to be in?
User: Please use the (XXX)XXX-XXXX format

_Task_
Reformat the phone numbers into the '(XXX)XXX-XXXX' format
'289-7623'
'931-5084'
'745-2190'
'672-9438'
'584-3021'

_Output_
```json
{{
  "thought": "The phone number are missing the area code, which is hard to infer",
  "question": "Concretely, there are a number of entries like '289-7623' or '672-9438' which lack the area code. Could you provide me a default area code to use for these cases?"
}}
```

_Conversation_
User: Can we format the data in DeliveryTime into a standard time?
Agent: Would you like to format them as 'HH:mm' or 'hh:mm aa'?
User: I mean the day of the week, not a time format.

_Task_
Reformat the weeks into the 'Week' format
'Wednesday or Friday'
'Thursay'
'Wednesay, Friday'
'Saturday or Sunday'
'Tuesday, Thursday'

_Output_
```json
{{
  "thought": "One common issue is the presence of multiple days in a single entry.",
  "question": "More precisely, what should we do with entries like 'Wednesay or Friday' which have two parts?"
}}
```

_Conversation_
User: I want all the URLs to be in a consistent format with 'protocol://subdomain.domain.tld'
Agent: Which column are you referring to?
User: referral_links column

_Task_
Reformat the urls into the 'protocol://subdomain.domain.tld' format
'www.sugarrushcandy.com'
'www.gummybearland.org'
'www.toyboxtreasures.net/cart'
'www.lollipopplayland.com'
'www.teddybeartoys.store'

_Output_
```json
{{
  "thought": "The URLs are missing the protocol, which is hard to infer",
  "question": "Specifically, there are entries like 'www.gummybearland.org' or 'www.lollipopplayland.com' which lack the protocol. Should I assume 'http://' or 'https://' for these?"
}}
```
---
Now it's your turn! Based on the conversation history, task description, and samples of conflicts, please generate a thought followed by a useful clarification question.

_Conversation_
{history}

_Task_
{task}
{conflicts}

_Output_
"""

validate_prompt = """The user wants to validate that all data in the {target_column} column belongs some predefined set of values.
In addition to the conversation history, you will be provided with a list of all unique values found in the column.
Your task is to group these values into the most likely valid terms, with remaining related terms following each valid term.
If certain values don't seem to fit into any group and can be seen as noise, then map them to a '<none>' category.

Please start by examining the unique values and then think out loud of about how you would group them.
Then, generate well-formatted JSON output for the groups where each key is a valid term and the corresponding value is a list of related terms.
Only terms found from the unique values can be considered a valid term key. Do not create new categories that are not present in the unique values.
Your final response should only contain valid JSON with parents keys of thought (string) and groups (dict), with no further explanations.

For example,
#############
_Conversation History_
User: We need to clean up the different plan types.
Agent: Sure, I can validate the data, remove duplicates, or standardize the format. What would you like?
User: We should make sure that they're either Free, Basic, or Pro.

_Unique Values_
['Free user', 'basic', 'standard', 'pro', 'Pro user', 'Free', 'Basic', 'free', 'free tier', 'Pro']

_Output_
```json
{{
  "thought": "User has told us the plan groupings: Free, Basic, and Pro. So we simply need to map the remaining values to these groups.",
  "groups": {{
    "Free": ["Free user", "free", "free tier"],
    "Basic": ["basic", "standard"],
    "Pro": ["pro", "Pro user"]
  }}
}}
```

_Conversation History_
User: We need to take care of the different user locations so that they are consistent.
Agent: Sure, how would you like to clean up the addresses?
User: Let's focus on the different zip codes for now.

_Unique Values_
['94040', '94045', '91150', '94046', '94043', '940', '94 043', '94044', '91123', 'zip', '94047', '9112 8', '94041']

_Output_
```json
{{
  "thought": "We should group by valid zip codes. There might be some OCR errors or missing digits to deal with.",
  "groups": {{
    "94040": [],
    "94045": [],
    "91150": [],
    "94046": [],
    "94043": ["94 043"],
    "94044": [],
    "94047": [],
    "91123": ["9112 8"],
    "94041": [],
    "<none>": ["940", "zip"]
  }}
}}
```

_Conversation History_
User: Let's take a look at where we run promotions.
Agent: Sure, what would you like to know?
User: We only ship to cities in California at the moment, so we need to validate the city names.

_Unique Values_
['Los Angeles', 'San Jose', 'San Diego', 'Sacramento', 'Fresno', 'SJ', 'San Bernardino', 'Bay Area', 'SF', 'Bakersfield', 'Anaheim', 'LOs Angeles',
  'LA', 'san bernardino', 'san jose', 'San Francisco', 'anaheim', 'Riverside', 'riverside', 'San Fran', 'Stockton', 'SanDiego', 'sacramento']

_Output_
```json
{{
  "thought": "We should group by city name. The most likely desired format is title case text.",
  "groups": {{
    "Los Angeles": ["LA", "LOs Angeles"],
    "San Jose": ["SJ", "san jose"],
    "San Diego": ["SanDiego"],
    "San Bernardino": ["san bernardino"],
    "San Francisco": ["San Fran", "SF", "Bay Area"],
    "Anaheim": ["anaheim"],
    "Riverside": ["riverside"],
    "Bakersfield": [],
    "Fresno": [],
    "Stockton": [],
    "Sacramento": ["sacramento"]
  }}
}}
```

_Conversation History_
User: There should only be two valid options.
Agent: I'm sorry, could you clarify which options you're referring to?
User: Can we make sure the payment status is either paid or unpaid.

_Unique Values_
['paid', 'PAID', 'unpaid']

_Output_
```json
{{
  "thought": "The user has specified two valid options: paid and unpaid. We can easily group the remaining values accordingly.",
  "groups": {{
    "paid": ["PAID"],
    "unpaid": []
  }}
}}
```
#############
_Conversation History_
User: What are the different pages in the checkout process?
Agent: According to PageActivity, some options include Enter Credit Card, Submit Payment, and Review Order. See table for more details.
User: Yea, so Payment Submission should be under Submit Payment actually, we gotta group these together

_Unique Values_
['Enter Credit Card', 'Submit Payment', 'Add to Cart', 'Review Order', 'Payment Submission', 'enter payment', 'ReView Order', 'Submit payment', 'Review Cart', 'Order Confirmation', 'Add-to-Cart']

_Output_
```json
{{
  "thought": "We know 'Submit Payment' is one valid term. We select other pages as the most likely to belong to a checkout process.",
  "groups": {{
    "Add to Cart": ["Add-to-Cart"],
    "Enter Credit Card": ["enter payment"],
    "Review Order": ["ReView Order", "Review Cart"],
    "Submit Payment": ["Submit payment", "Payment Submission"],
    "Order Confirmation": []
  }}
}}
```
#############
Now it's your turn! Based on the conversation history and unique values, please group the values and decide on the most likely valid terms for the {target_column} column.

_Conversation History_
{history}

_Unique Values_
{uniques}

_Output_
"""

validation_grouping_prompt = """Given the set of valid terms within a column, we are trying to map all remaining invalid terms to its most likely valid one.
The valid terms are values the user has confirmed are correct, while the invalid terms represent typos, variations, or other noise.
Start by examining all the unique values and consider how to map the invalid terms to the valid ones.
Then, generate well-formatted JSON output where each valid term serves as a key in the 'groups' dictionary.

Do not add or remove any new keys from the valid terms list, only map the invalid terms to the existing valid ones.
If there isn't a clear match for a term, then map it to a '<none>' category.
Your final response should only contain valid JSON including a thought (string) and groups (dict), with no further explanations after the output.

For example,
---
_Unique Values_
Valid: ['Free', 'Basic', 'Pro']
Invalid: ['Free user', 'basic', 'standard', 'pro', 'Pro user', 'free', 'free tier']

_Output_
```json
{{
  "thought": "Standard likely belongs to basic. All the others include a valid term, making them easy to match.",
  "groups": {{
    "Free": ["Free user", "free", "free tier"],
    "Basic": ["basic", "standard"],
    "Pro": ["pro", "Pro user"]
  }}
}}
```

_Unique Values_
Valid: ['94040', '94045', '91150', '94046', '94043', '94044', '94047', '94041', '91123']
Invalid: ['940', '94 043', 'zip', '9112 8']

_Output_
```json
{{
  "thought": "Valid zip codes likely have five digits. Some entries have OCR errors or missing digits.",
  "groups": {{
    "94040": [],
    "94045": [],
    "91150": [],
    "94046": [],
    "94043": ["94 043"],
    "94044": [],
    "94047": [],
    "91123": ["9112 8"],
    "94041": [],
    "<none>": ["940", "zip"]
  }}
}}
```

_Unique Values_
Valid: ['Los Angeles', 'San Jose', 'San Diego', 'San Bernardino', 'San Francisco', 'Anaheim', 'Riverside', 'Bakersfield', 'Fresno', 'Stockton', 'Sacramento']
Invalid: ['SJ', 'Bay Area', 'SF', 'LOs Angeles', 'LA', 'san bernardino', 'san jose', 'anaheim', 'riverside', 'San Fran', 'SanDiego', 'sacramento']

_Output_
```json
{{
  "thought": "The pattern is to group by city name with proper capitalization.",
  "groups": {{
    "Los Angeles": ["LA", "LOs Angeles"],
    "San Jose": ["SJ", "san jose"],
    "San Diego": ["SanDiego"],
    "San Bernardino": ["san bernardino"],
    "San Francisco": ["San Fran", "SF", "Bay Area"],
    "Anaheim": ["anaheim"],
    "Riverside": ["riverside"],
    "Bakersfield": [],
    "Fresno": [],
    "Stockton": [],
    "Sacramento": ["sacramento"]
  }}
}}
```

_Unique Values_
Valid: ['SJC', 'SAN', 'LAX', 'SMF', 'SFO', 'FAT', 'BUR', 'LGB', 'OAK']
Invalid: ['Los Angeles', 'Sacramento', 'SJ', 'smf', 'Bay Area', 'LA', 'sjc', 'San Francisco', 'SAN Diego', 'lax', 'san']

_Output_
```json
{{
  "thought": "The pattern is to group by airport code in uppercase.",
  "groups": {{
    "SJC": ["SJ", "sjc"],
    "SAN": ["SAN Diego", "san"],
    "LAX": ["Los Angeles", "LA", "lax"],
    "SMF": ["Sacramento", "smf"],
    "SFO": ["Bay Area", "San Francisco"],
    "FAT": [],
    "BUR": [],
    "LGB": [],
    "OAK": []
  }}
}}
```

_Unique Values_
Valid: ['Add to Cart', 'Enter Credit Card', 'Review Order', 'Submit Payment', 'Order Confirmation']
Invalid: ['Payment Submission', 'enter payment', 'ReView Order', 'Submit payment', 'Review Cart', 'Add-to-Cart']

_Output_
```json
{{
  "thought": "The pattern is to group the terms by the pages of a checkout process.",
  "groups": {{
    "Add to Cart": ["Add-to-Cart"],
    "Enter Credit Card": ["enter payment"],
    "Review Order": ["ReView Order", "Review Cart"],
    "Submit Payment": ["Submit payment", "Payment Submission"],
    "Order Confirmation": []
  }}
}}
```
---
Now it's your turn! Based on the conversation history and unique values, please group the values and decide on the most likely valid terms for the {target_column} column.
For reference, here is the recent conversation history and unique values:
{history}

_Unique Values_
Valid: {valid_terms}
Invalid: {invalid_terms}

_Output_
"""

backup_validation_prompt = """We are performing data validation so that all the values within the '{target_column}' column only include the correct terms.
Given the conversation history, your task is to decide which terms are the correct ones. The remaining invalid terms are typos, minor variations, or otherwise noise.
To aid with your decision, you will be provided with a list of all unique values found in the column.
Please start by examining the unique values and then think out loud of about the properties of valid terms.
Then, generate well-formatted JSON output for the groups where each key is a valid term and the corresponding value is a list of related terms.
Your final response should only contain valid JSON including keys for thought (string) and terms (list), with no further explanations after the output.

For example,
#############
_Conversation History_
User: We need to clean up the different plan types.
Agent: Sure, I can validate the data, remove duplicates, or standardize the format. What would you like?
User: We should make sure that they're either Free, Basic, or Pro.

_Unique Values_
['Free user', 'basic', 'standard', 'pro', 'Pro user', 'Free', 'Basic', 'free', 'free tier', 'Pro']

_Output_
```json
{{
  "thought": "User has told us the valid terms: Free, Basic, and Pro.",
  "terms": ["Free", "Basic", "Pro" ]
}}
```
#############
_Conversation History_
User: We need to take care of the different user locations so that they are consistent.
Agent: Sure, how would you like to clean up the addresses?
User: Let's focus on the different zip codes for now.

_Unique Values_
['94040', '94045', '91150', '94046', '94043', '940', '94 043', '94044', '91123', 'zip', '94047', '9112 8', '94041']

_Output_
```json
{{
  "thought": "Valid zip codes likely have five digits. Some entries have OCR errors or missing digits.",
  "terms": ["94040", "94045", "91150", "94046", "94043", "94044", "94047", "94041", "91123"]
}}
```
#############
_Conversation History_
User: Let's take a look at where we run promotions.
Agent: Sure, what would you like to know?
User: We only ship to locations in California at the moment, so we need to validate the cities.

_Unique Values_
['Los Angeles', 'San Jose', 'San Diego', 'Sacramento', 'Fresno', 'SJ', San Bernardino', 'Bay Area', 'SF', 'Bakersfield', 'Anaheim', 'LOs Angeles',
  'LA', 'san bernardino', 'san jose', 'San Francisco', 'anaheim', 'Riverside', 'riverside', 'San Fran', 'Stockton', 'SanDiego', 'sacramento']

_Output_
```json
{{
  "thought": "Valid terms are city names with proper capitalization.",
  "terms": [ "Los Angeles", "San Jose", "San Diego", "San Bernardino", "San Francisco", "Anaheim", "Riverside", "Bakersfield", "Fresno", "Stockton", "Sacramento"]
}}
```
#############
_Conversation History_
User: Let's take a look at where we run promotions.
Agent: Sure, what would you like to know?
User: We only ship to locations in California at the moment, so we need to validate the cities.

_Unique Values_
['Los Angeles', 'SJC', 'SAN', 'Sacramento', 'SJ', 'smf', 'LAX', 'Bay Area', 'SFO', 'FAT',
  'LA', 'sjc', 'San Francisco', 'BUR', 'SAN Diego', 'LGB', 'SMF', 'lax', 'OAK', 'san']

_Output_
```json
{{
  "thought": "The majority of terms are airport codes rather than city names, so we will treat those as the valid ones.",
  "terms": ["SJC", "SAN", "LAX", "SMF", "SFO", "FAT", "BUR", "LGB", "OAK"]
}}
```
#############
_Conversation History_
User: What are the different pages in the checkout process?
Agent: According to PageActivity, some options include Enter Credit Card, Submit Payment, and Review Order. See table for more details.
User: Yea, so Payment Submission should be under Submit Payment actually, we gotta group these together

_Unique Values_
['Enter Credit Card', 'Submit Payment', 'Add to Cart', 'Review Order', 'Payment Submission', 'enter payment', 'ReView Order', 'Submit payment', 'Review Cart', 'Order Confirmation', 'Add-to-Cart']

_Output_
```json
{{
  "thought": "Valid terms are the pages that belong to a checkout process.",
  "terms": ["Add to Cart", "Enter Credit Card", "Review Order", "Submit Payment", "Order Confirmation"]
}}
```
#############
Now it's your turn! Based on the conversation history and unique values, please group the values and decide on the most likely valid terms for the {target_column} column.

_Conversation History_
{history}

_Unique Values_
{uniques}

_Output_
"""


datetime_format_prompt = """Given the conversation history, we believe the user wants to standardize data within the {target_column} column to conform to a specific datetime format.
Currently, we need to make an informed decision about the most likely format that the user is referring to. Plausible options include:
  * Date
    - %m/%d/%Y: 01/31/2022
    - %d/%m/%Y: 31/01/2022, changing the order
    - %-m/%-d/%Y: 1/31/2022, leading zeros are optional
    - %Y-%m-%d: 2022-01-31, changing the delimiter
    - %m/%d/%y: 01/31/22, using two-digit years
    - %m-%d: 01-31, only the month and day
    - %B %d, %Y: January 31, 2022, with the month as a full name
    - %b. %d: Jan. 31, using the abbreviated month name with a period
    - %B %o: January 31st, using the ordinal with a custom `%o` specifier
    - %d %B %y: 31 January 22, unusual but possible choice
  * Time
    - %H:%M:%S: 14:59:59
    - %H:%M: 14:30, omitting seconds
    - %I:%M: 02:30, using 12-hour format
    - %I:%M %p: 02:30 PM, including AM/PM
    - %I:%M %p-: 02:30 P, abbreviated AM/PM with a custom `%p-` specifier
    - %-I:%M %p: 2:30 PM, leading zero is optional
  * Timestamp
    - %Y-%m-%d %H:%M:%S: 2019-10-20 15:48:22
    - %Y-%m-%dT%H:%M:%SZ: 2019-10-20T15:48:22Z, with UTC indicator
    - %Y-%m-%dT%H:%M:%S.%f%z: 2019-10-20T15:48:22.000000+0500, with timezone offset
    - %Y-%m-%d %H:%M:%S.%f: 2019-10-20 15:48:22.600000, including microseconds
    - %m/%d/%Y %H:%M:%S: 10/20/2019 15:48:22, changing the delimiter
    - %m/%d/%Y %I:%M %p: 10/20/2019 3:48 PM, including AM/PM
    - %Y-%m-%dT%H:%M:%S: 2019-10-20T15:48:22, local time zone
  * Week
    - %A: Monday, Thurday; full weekday name
    - %a: Mon, Thu; abbreviated weekday name
    - %a.: Mon., Thu.; abbreviated with period
    - %U: 01, 52; week as a number starting on Sunday
    - %A, %B %o: Monday, April 1st; full date with ordinal suffix
  * Quarter
    - %q: Q1, Q2; quarter as a number
    - %Q: First Quarter, Second Quarter; quarter as a full name
    - %o: 1st, 2nd; quarter as an ordinal
    - %q'%y: Q1'24, Q3'24; quarter as a number with year abbreviation
    - %Y-%q: 2021-Q1, 2022-Q3; year followed by quarter with delimiter

Note that the formatting components above can be used in a standalone fashion to also support the 'Month', 'Day', and 'Year' datatypes.
If the user has explicitly mentioned what they want, then that is obviously the correct choice, so we only need to output a single format.
Otherwise, please use the provided sample rows of the target columns to determine up to three possible formats that are the most likely choices.

In addition, to the four datatypes described above (date, time, timestamp, week), you can also choose from: year, quarter, month.
If the conversation does not focus on one of these types, then output 'unsure' as the datatype. This gives a total of 4 + 3 + 1 = 8 possible datatypes to choose from.
Feel free to mix and match any specifiers (including the custom ones or those recognizable by `.strftime()`) to develop a format that best fits the data.
Your entire response should be in well-formatted JSON with keys for a thought (string), datatype (string), and formats (list), with no further explanations after the JSON output.

For example,
---
## Date Example
| Shipping Date |
|:--------------|
| 08-15-2024    |
| 07-18-2024    |
| 07-22-2024    |
| 07-25-2024    |
| May 2         |
| 04/08/2024    |
| 04/15/2024    |
| 11/05/2023    |
| 2023-05-22    |
| 07/29/2023    |

_Conversation History_
User: When was the most recent shipment sent out?
Agent: The last shipment was sent on August 15th.
User: Actually, can we first make sure that all the shipping dates include the year at the end?

_Output_
```json
{{
  "thought": "Based on the preview, we can tell the month comes first and then the day. Based on the user request, all the formats should end with YYYY.",
  "datatype": "date",
  "formats": [ "%m-%d-%Y", "%B %d, %Y", "%m/%d/%Y" ]
}}
```

## Time Example
| LeftFacilityAt   | EstDeliveryTime |
|:-----------------|-----------------|
| 14:30:00         | 15:45           |
| 11:45:00         | 13:15           |
| 08:00:00         | 9:30            |
| 17:00:00         | 18:30:00        |
| 06:30:00         | 8:00 AM         |
| 09:45:00         | 11:15           |
| 16:32:00         | 13:30:00        |
| 15:15:00         | 14:45:00        |
| 10:30:00         | 12:00 noon      |
| 18:45:00         | 17:15           |

_Conversation History_
User: Can we standardize all the times in the bookings table?
Agent: Sure, is there a specific form you are looking for?
User: I want just the hour and minute without the seconds.

_Output_
```json
{{
  "thought": "All the proposed time formats we choose should not include the seconds portion.",
  "datatype": "time",
  "formats": [ "%H:%M", "%I:%M %p", "%-I:%M" ]
}}
```

## Week Example
For another scenario, consider the following column:
| reporting_deadline  |
|:--------------------|
| Thu                 |
| Wed                 |
| Mon                 |
| Thu                 |
| Thursday            |
| Tuesday afternoon   |
| Wed                 |
| Thu                 |
| Wed                 |
| Mon                 |

_Conversation History_
User: We can forget about that for now.
Agent: Ok, no problem. I will ignore those issues for the time being.
User: Great, let's focus on getting all the scheduled reporting deadlines into the same format.

_Output_
```json
{{
  "thought": "Based on the column preview, it is clear the pattern is to use the abbreviated weekday name.",
  "datatype": "week",
  "formats": [ "%a" ]
}}
```

## Timestamp Example
As another example, suppose the target column contains:
| timestamp_submission |
|:---------------------|
| 2024-07-15 14:30:00  |
| 2024-07-18 11:45:00  |
| 2024-07-22 08:00:00  |
| 2024-07-25 17:00:00  |
| 2024-08-02 06:30:00  |
| 2024-08-05T09:45:00Z |
| 2024-08-08 16:32:00  |
| 2024-08-11 15:15:00  |
| 2024-08-14 10:30:00  |
| 2024-08-17 18:45:00  |

_Conversation History_
User: ANything older than 3 months is irrelevant.
Agent: Ok, I will remove anything older than 3 months. How does this look?
User: Can we also make sure that we are using the standard format for the remaining timestamps?

_Output_
```json
{{
  "thought": "The standard format for timestamps varies, so I will focus on the two that appear in the column preview.",
  "datatype": "timestamp",
  "formats": [ "%Y-%m-%d %H:%M:%S", "%Y-%m-%dT%H:%M:%SZ" ]
}}
```

## Month Example
Suppose the target column contains:
| month  |
|:-------|
| 03     |
| 08     |
| 10     |
| 04     |
| May    |
| 06     |
| Feb    |
| 07     |
| Jan    |
| 09     |

_Conversation History_
User: Can you make sure that all the months are in the same format?

_Output_
```json
{{
  "thought": "Some of the months are numeric, while others are written as text.",
  "datatype": "month",
  "formats": [ "%m", "%b" ]
}}
```

## Day Example
For a final example, consider the following scenario:
| due_date |
|:---------|
| 3rd      |
| 15th     |
| 4th      |
| 15th     |
| 16th     |
| 6th      |
| 15       |
| 7th      |
| 7th      |
| 09       |

_Conversation History_
User: We need to standardize the different emails.
Agent: 273 rows in the email_address column have been successfully changed.
User: Can you do the same for the due dates?

_Output_
```json
{{
  "thought": "The preview shows a preference for the ordinal suffix, but other formats also appear.",
  "datatype": "day",
  "formats": [ "%o", "%d", "%-d" ]
}}
```
---
Now it's your turn to tackle the current case. Remember to generate a thought first and then output the appropriate datatype and formats. For reference, the target columns contain:
{preview}

_Conversation History_
{history}

_Output_
"""

textual_format_prompt = """Given the conversation history, we believe the user wants to standardize data within the {target_column} column to conform to a specific text format.
Currently, we need to make an informed decision about the most likely format that the user is referring to. Plausible options include:
  * Phone
    - '(XXX) XXX-XXXX': (*************
    - 'XXX-XXX-XXXX': ************, using hyphens
    - 'XXX.XXX.XXXX': ************, using periods
    - 'XXX XXX XXXX': ************, using spaces
    - '+X (XXX) XXX-XXXX': +1 (*************, including country code
    - 'X(XXX)XXX-XXXX': 1(650)783-4412, no space after the country code
  * Email
    - '<EMAIL>': <EMAIL>
    - 'username@domain': donotreply@soleda, without the top-level domain
    - 'username': donotreply, only the username
  * URL
    - 'protocol://subdomain.domain.tld/page': https://www.example.com/login
    - 'protocol://subdomain.domain.tld/page?query': https://www.example.com/login?user=1, allows for query parameters
    - 'subdomain.domain.tld/page': www.example.com/login, without the protocol
    - 'subdomain.domain.tld': www.example.com/login, stripped of the page and protocol
    - 'protocol://subdomain.domain.tld': http://www.example.com, with the protocol
    - 'domain.tld': example.com, without the subdomain
    - 'subdomain.domain': www.example, possible but usual option of excluding the top-level domain
  * Name
    - 'first last': Scott Lee
    - 'last first': Lee, Scott, with the last name at the beginning
    - 'last, first': Lee, Scott, with a comma
    - 'first middle last': Scott Hyung Lee, with a middle name
    - 'last first middle': Lee, Scott Hyung, with a middle name at the end
    - 'honorific first last': Dr. Scott Lee, with a title
    - 'first last suffix': Scott Lee Jr., with a suffix
  * Currency
    - '$XX.XX': $12.99, with a dollar sign and two decimal places
    - '€XX.XX': €12.99, with an euro sign and two decimal places
    - 'XX.XX': 12.99, without any currency symbol
    - 'XX,XX': 12,99, with a comma instead of a period
    - 'XX,XXX': 12,789, rounded to the nearest dollar ignoring the cents

If the user has explicitly mentioned what they want, then that is obviously the correct choice, so we only need to output a single format.
Otherwise, please use the provided sample rows of the target columns to determine up to three possible formats that are the most likely choices.

Notice that formats come with one of four datatypes: phone, url, email, or name. If the conversation does not focus on one of these types, then output 'unsure' as the datatype.
Your entire response should be in well-formatted JSON with keys for a thought (string), datatype (string), and formats (list), with no further explanations after the JSON output.

For example,
---
Suppose the target column contains:
| Email Address                 |
|:------------------------------|
| <EMAIL>          |
| jenniferlawrence4ever@gmail   |
| <EMAIL>     |
| <EMAIL>     |
| <EMAIL> |
| <EMAIL>         |
| <EMAIL>        |
| <EMAIL>    |
| <EMAIL>        |
| <EMAIL>      |

_Conversation History_
User: let's move onto the survey responses now
Agent: Certainly, what would you like to know? Is there a specific metric you are interested in?
User: i want to first make sure that we only have valid email addresses

_Output_
```json
{{
  "thought": "Since the user just wants to check whether each row has a valid email address, we can assume the standard format.",
  "datatype": "email",
  "formats": [ "<EMAIL>" ]
}}
```

As another example, suppose the target column contains:
| Website Landing Page                                                              |
|:----------------------------------------------------------------------------------|
| www.autotrader.com/cars-for-sale/all-cars?zipcode=90210                           |
| https://www.caranddriver.com/reviews/a38500515/2022-ford-f-150-lightning-drive/   |
| www.carfax.com/vehicle-history-reports/?vin=1HGCV1F34JA027906                     |
| https://www.carmax.com/cars/hybrid?location=Denver                                |
| https://www.motortrend.com/features/future-electric-cars-trucks-suvs/             |
| www.jalopnik.com/tag/classic-cars/                                                |
| https://www.autoblog.com/article/best-electric-cars/                              |
| www.carfax.com/value-my-car/?license=9VCP133&state=CA                             |
| www.autotrader.com/research/article/car-news/10-best-luxury-suvs-for-2024.jsp     |
| https://www.caranddriver.com/shopping-advice/g32463239/new-cars-under-20000/      |

_Conversation History_
User: What's the average length of the different website pages?
Agent: The average length is 58.7 characters.
User: Can I get just the websites with the stuff at the end cleaned up?

_Output_
```json
{{
  "thought": "Removing stuff at the end of a URL likely refers to query parameters, but could also mean removing the 'page' portion.",
  "datatype": "url",
  "formats": [ "protocol://subdomain.domain.tld/page", "subdomain.domain.tld", "protocol://subdomain.domain.tld"]
}}
```

For another scenario, consider the following:
| Contact Number   |
|:-----------------|
| (*************   |
| ************     |
| ************     |
| ************     |
| +1 (650)558-7214 |
| 1(650)787-5902   |
| **********       |
| **************   |
| **************   |
| **************   |

_Conversation History_
User: Separate the area code from the rest of the phone numbers into two columns, and just drop the country code.
Agent: Apologies, but the phone numbers are in different formats so I cannot split them easily.
User: Can we fix that first then?

_Output_
```json
{{
  "thought": "We know not to include the country code in any of the formats, but the rest is up for interpretation.",
  "datatype": "phone",
  "formats": [ "(XXX) XXX-XXXX", "XXX-XXX-XXXX", "(XXX)XXX-XXXX" ]
}}
```

In this example, suppose the target column has:
| Full Name        |
|:-----------------|
| Rodriguez Emily  |
| Jocelyn Reyes    |
| Patel Aisha      |
| Dr. Taylor Scott |
| Morgan Riley     |
| Jordan Casey     |
| Quinn Parker     |
| Nguyen Olivia    |
| James Wilson Sr. |
| Morales Sofia    |

_Conversation History_
User: So who bought the most in October then
Agent: The top buyer from October was Jocelyn Reyes.
User: Can we get rid of all the punctuation or anything else in the names?

_Output_
```json
{{
  "thought": "According to the preview, we should use spaces, but it's not clear whether the names are first then last, or in reversed order.
  "datatype": "name",
  "formats": [ "first last", "last first" ]
}}
```

For a final example, consider the following scenario:
| Inbound links                                                                     |
|:----------------------------------------------------------------------------------|
| https://www.tiredandtrueauto.com/?ref=autoblog&campaign=summer_sale               |
| https://www.tiredandtrueauto.com/deals/?aff=carfax&source=email                   |
| https://www.tiredandtrueauto.com/newtires/?affiliate=motortrend&ad=banner         |
| https://www.tiredandtrueauto.com/used/?partner=autotrader&medium=social           |
| https://www.tiredandtrueauto.com/allseason/?ref=caranddriver&utm_content=article  |
| https://www.tiredandtrueauto.com/financing/?aff=jalopnik&utm_medium=podcast       |
| https://www.tiredandtrueauto.com/truck/?affiliate=kbb&campaign=winter_deals       |
| https://www.tiredandtrueauto.com/luxury/?partner=edmunds&utm_source=newsletter    |
| https://www.tiredandtrueauto.com/compare/?ref=carmax&utm_campaign=trade_in        |
| https://www.tiredandtrueauto.com/installation/?aff=cars.com&utm_term=local_search |

_Conversation History_
User: Can we clean up the affiliate links?
Agent: Ok, how do you want to clean them? I can help with tasks like data validation, formatting, or removing duplicates.
User: I just want to make sure that all the links are valid. And get rid of blanks.

_Output_
```json
{{
  "thought": "Since the user just wants to check whether each row has a valid url, we use the broadest format available.",
  "datatype": "url",
  "formats": [ "protocol://subdomain.domain.tld/page?query" ]
}}
```
---
Now it's your turn to tackle the current case. Remember to generate a thought first and then output the appropriate datatype and formats. For reference, the target columns contain:
{preview}

_Conversation History_
{history}

_Output_
"""

find_pattern_prompt = """Given the conversation history, we must determine how to fill in the missing values in the '{target_column}' column.
Please begin by considering the general pattern seen in the example rows or in the user utterance provided in the conversation.
Next, describe this pattern in more precise language, focusing specifically on how to start and how to increment or calculate the next values.
Lastly, write out the exact base value and corresponding code snippet to fill in the target column.
The snippet can be written out as a Pandas function or in Python pseudo-code when the calculation is more complex.

If either the pattern, the base value, or the snippet is not clear, then output 'unsure' for that part.
Your final response should only contain well-formatted JSON including your pattern (string), base (string), and snippet (string), without any additional text or explanations.

For example,
#############
_Related Columns_
| Payment Fees |
|:-------------|
| 14           |
| 25           |
| 20           |
| 25           |
| 19           |
| 22           |
| 25           |
| 23           |

_Conversation History_
User: Let's make a new column for total payments as well.
Agent: Sure, how should we populate this column?
User: I want it to hold the total amount of all the payments made so far.

_Output_
```json
{{
  "pattern": "calculate the cumulative sum of payment fees by adding the value of the current row to the total from the previous row",
  "base": "14",
  "snippet": "= df['Payment Fees'].cumsum()"
}}
```
#############
_Related Columns_
| Student    | Presentation Date |
|:-----------|-------------------|
| Henry      | 2022-07-26        |
| Sarah      | 2022-07-27        |
| Peter      | 2022-07-28        |
| Emily      |                   |
| Shaniqua   |                   |
| Mitchell   |                   |
| Natalie    |                   |
| JingSo     |                   |

_Conversation History_
User: Yup, let's get rid of them
Agent: No problem, those values have been removed
User: Now, can you fill in the missing presentation dates?

_Output_
```json
{{
  "pattern": "increment the date by one day for each subsequent student",
  "base": "2022-07-26",
  "snippet": "= df['Presentation Date'].shift(1) + pd.DateOffset(days=1)"
}}
```
#############
_Related Columns_
| Speaker    | Utterance                                     |
|:-----------|-----------------------------------------------|
| Customer   | My phone doesn't make calls anymore           |
| Technician | I'm sorry to hear that                        |
| Customer   | Can you help me fix it?                       |
| Technician | To begin, can I get phone's version and CCID? |
| Customer   | Sure, it's an iPhone 12 with CCID 144098125   |
| Technician | Ok, let's try restarting the phone            |
| Customer   | Yeah, I did that but it didn't work           |
| Technician | Let's check the network settings next         |

_Conversation History_
User: So what I want is to fill the dialogue column with the last three turns of the chat.
Agent: To clarify, I will call the new column 'Recent Dialogue'. Does that sound good?
User: Yes, that's perfect.

_Output_
```json
{{
  "pattern": "insert the last three utterances of the conversation in separate lines",
  "base": "Customer: My phone doesn't make calls anymore",
  "snippet": "chat = ''\nfor speaker, utt_text in LastThreeTurns:\n  chat += 'speaker: utt_text + newline'"
}}
```
#############
_Related Columns_
| CPA    |
|:-------|
| 11.34  |
| 9.45   |
| 10.08  |
| 8.91   |
| 9.12   |
| 10.22  |
| 9.78   |
| 10.05  |

_Conversation History_
User: Each row should subtract off the acquisition cost and then add the LTV of each user.
Agent: In order to calculate this, I would need to know the LTV of each user. However, I don't see a column related to user account revenue or time period. What should I use?
User: Just use $45 for LTV.

_Output_
```json
{{
  "pattern": "calculate the net profit per user by subtracting the CPA and add a constant LTV of $45",
  "base": "$33.66",
  "snippet": "= df['Net Profit'].shift(1) - df['CPA'] + 45"
}}
```
#############
_Related Columns_
| BetsMade     | Winnings |
|:-------------|---------:|
| Timberwolves |          |
| Lakers       |          |
| Clippers     |          |
| Spurs        |          |
| Warriors     |          |
| Lakers       |          |
| Thunder      |          |
| Celtics      |          |

_Conversation History_
User: Let's fill in the column with our winnings.
Agent: Ok, how should we populate this column?
User: When we win a bet, then the amount increases by 10%, otherwise it stays the same

_Output_
```json
{{
  "pattern": "compare the bet made to the result to determine the outcome, then increase the winnings by 10% if the bet is successful",
  "base": "unsure",
  "snippet": "unsure"
}}
```

#############
_Related Columns_
| close_date  | close_price |
|:------------|-------------|
| 2021-04-14  | 45.76       |
| 2021-04-15  | 46.12       |
| 2021-04-16  | 45.98       |
| 2021-04-19  | 46.22       |
| 2021-04-20  | 45.89       |
| 2021-04-21  | 46.01       |
| 2021-04-22  | 45.78       |
| 2021-04-23  | 45.99       |

_Conversation History_
User: I want to smooth out the closing prices of the stock
Agent: Sure, should I calculate the average of the last few days in a new column?
User: we need the trailing average on a weekly basis

_Output_
```json
{{
  "pattern": "calculate the rolling average of last 5 business days since stock market is closed on weekends",
  "base": "45.76",
  "snippet": "= df['close_price'].rolling(window=5).mean()"
}}
```
#############
Based on the conversation and the related data, please determine the pattern, base, and snippet for populating the {target_column} column.
_Related Columns_
{preview}

_Conversation History_
{history}

_Output_
"""

pattern_code_prompt = """Given the user request and supporting details, follow the thought process to write code for filling a column based on some pattern or formula.
Supporting details include the name of the target column, possible supporting columns, and a sketch of the formula to be applied.
You will also be given a small sample of rows in the table to help you understand the data.
The existing dataframes you have are {df_tables}.
Notice that data is accessed using the format `db.tab_name['col_name']`

Focus on writing directly executable Python, which may contain comments to help with reasoning. If a request requires multiple steps, write each step on a new line.
Do NOT create any copies of the dataframe. Instead, you should modify the dataframe directly.
In fact, you should even try to perform operations in-place, rather than assigning to a new dataframe.
Your final response should only contain well-formatted Python code, without any additional text or explanations after the output.

#############
_Conversation History_
User: Let's make a new column for total payments as well.
Agent: Sure, how should we populate this column?
User: I want it to hold the total amount of all the payments made so far.

_Supporting Details_
* Prior thoughts: calculate the cumulative sum of payment fees by adding the value of the current row to the total from the previous row
* Target column: TotalPayments in Stripe_Updated
* Base value: 14
* Formula sketch: = df['Payment Fees'].cumsum()
| PaymentFees |
|:------------|
| 14          |
| 25          |
| 20          |
| 25          |
| 19          |
| 22          |
| 25          |
| 23          |

_Output_
```python
db.Stripe_Update['TotalPayments'] = db.Stripe_Update['PaymentFees'].cumsum()
```
#############
_Conversation History_
User: Yup, let's get rid of them
Agent: No problem, those values have been removed
User: Now, can you fill in the missing presentation dates?

_Supporting Details_
* Prior thoughts: increment the date by one day for each subsequent student
* Target column: PresentationDate in BB_courses
* Base value: 2022-07-26
* Formula sketch: = df['PresentationDate'].shift(1) + pd.DateOffset(days=1)
| Student    | PresentationDate |
|:-----------|-------------------|
| Henry      | 2022-07-26        |
| Sarah      | 2022-07-27        |
| Peter      | 2022-07-28        |
| Emily      |                   |
| Shaniqua   |                   |
| Mitchell   |                   |
| Natalie    |                   |
| JingSo     |                   |

_Output_
```python
# set the base date, then increment with a timedelta since it is vectorized
base_date = pd.Timestamp('2022-07-26')
db.BB_courses['PresentationDate'] = base_date + pd.TimedeltaIndex(db.BB_courses.index, unit='D')
```
#############
_Conversation History_
User: So what I want is to fill the dialogue column with the last three turns from the chat logs.
Agent: To clarify, I will call the new column 'recent_dialogue'. Does that sound good?
User: Yes, that's perfect.

_Supporting Details_
* Prior thoughts: insert the last three utterances of the conversation
* Target column: recent_dialogue in ChatLogs
* Base value: Customer: My phone doesn't make calls anymore
* Formula sketch: chat = FirstTwoTurns\nfor speaker, utt_text in LastTurn:\n  chat += 'speaker: utt_text + newline'
| Speaker    | Utterance                                     |
|:-----------|-----------------------------------------------|
| Customer   | My phone doesn't make calls anymore           |
| Technician | I'm sorry to hear that                        |
| Customer   | Can you help me fix it?                       |
| Technician | To begin, can I get phone's version and CCID? |
| Customer   | Sure, it's an iPhone 12 with CCID 144098125   |
| Technician | Ok, let's try restarting the phone            |
| Customer   | Yeah, I did that but it didn't work           |
| Technician | Let's check the network settings next         |

_Output_
```python
db.ChatLogs['recent_dialogue'] = ''
chats = []

for index in range(len(db.ChatLogs)):
  # add the current turn to the dialogue
  speaker, utt_text = db.ChatLogs.iloc[index][['Speaker', 'Utterance']]
  chats.append(f"{{speaker}}: {{utt_text}}")
  # limit to the last 3 turns
  if len(chats) > 3:
    chats.pop(0)
  db.ChatLogs.loc[index, 'recent_dialogue'] = '\n'.join(chats)
```
#############
_Conversation History_
User: Each row should subtract off the acquisition cost and then add the LTV of each user.
Agent: In order to calculate this, I would need to know the LTV of each user. I can try to calculate it using the revenue generated and sale date, should I proceed?
User: Just use $45 for LTV.

_Supporting Details_
* Prior thoughts: calculate the net profit per user by subtracting the CPA and add a constant LTV of $45
* Target column: netProfit in SalesRecord_Shopify
* Base value: $33.66
* Formula sketch: = df['netProfit'].shift(1) - df['CPA'] + 45
| CPA    |
|:-------|
| 11.34  |
| 9.45   |
| 10.08  |
| 8.91   |
| 9.12   |
| 10.22  |
| 9.78   |
| 10.05  |

_Output_
```python
# set the base value by subtracting the first CPA from the LTV
db.SalesRecord_Shopify.loc[0, 'netProfit'] = 45.0 - 11.34  # 33.66
# calculate the remaining net profit by referencing the previous row
db.SalesRecord_Shopify[1:, 'netProfit'] = db.SalesRecord_Shopify['netProfit'].shift(1) - db.SalesRecord_Shopify['CPA'] + 45
```
#############
_Conversation History_
User: I want to smooth out the closing prices of the stock
Agent: Sure, should I calculate the average of the last few days in a new column?
User: we need the trailing average on a weekly basis

_Supporting Details_
* Prior thoughts: calculate the rolling average of last 5 business days since stock market is closed on weekends
* Target column: weekly_average in StockPrices
* Base value: 45.76
* Formula sketch: = df['close_price'].rolling(window=5).mean()
| close_date  | close_price |
|:------------|-------------|
| 2021-04-14  | 45.76       |
| 2021-04-15  | 46.12       |
| 2021-04-16  | 45.98       |
| 2021-04-19  | 46.22       |
| 2021-04-20  | 45.89       |
| 2021-04-21  | 46.01       |
| 2021-04-22  | 45.78       |
| 2021-04-23  | 45.99       |

_Output_
```python
# calculate the rolling average for stocks
db.StockPrices['weekly_average'] = db.StockPrices['close_price'].rolling(window=5).mean()
# go back to fill in the base value for the first four rows since the window is not complete
db.StockPrices.loc[0, 'weekly_average'] = 45.76
db.StockPrices.loc[1, 'weekly_average'] = db.StockPrices['close_price'].iloc[:2].mean()
db.StockPrices.loc[2, 'weekly_average'] = db.StockPrices['close_price'].iloc[:3].mean()
db.StockPrices.loc[3, 'weekly_average'] = db.StockPrices['close_price'].iloc[:4].mean()
```
#############
Now it's your turn to write code to fill in the target column based on the pattern or formula provided. Remember to use the existing dataframes and write directly executable Python code.

_Conversation History_
{history}

_Supporting Details_
{supporting}
{preview}

_Output_
"""

change_datatype_prompt = """Our goal is to convert certain rows in the '{column}' column to be compatible with the {datatype} datatype.
To provide context, you will be shown a sample rows from the target column, where the rows to be converted are highlighted with an arrow (<--).
This is followed by a list of indexes that correspond to where those values occur in the column. If the number of indexes is too large, a count of instances will be provided instead.
You also have access to the desired datatype, subtype, and Pandas dtype. The possible data types and their subtypes are:
  * unique - each value holds a unique meaning. Includes IDs (often used as primary keys), pre-defined categories, set of statuses, and boolean values.
  * datetime - related to dates or times. Includes quarter, month, day, year, week, date, time, hour, minute, second, and timestamp.
  * location - the values are related to geographical locations or addresses. Includes streets, cities, states, countries, zip codes, and full addresses.
  * number - the values are numeric and can be used for calculations. Includes currency, percent, whole numbers, and decimals.
  * text - the values are textual and can include any characters. Includes email addresses, phone numbers, URLs, names, and general text.

Please start by considering how to modify the target rows so their values match the desired data subtype.
Since we are only interested in storing the core content, this often means stripping away any extraneous characters, symbols, or whitespace.
Notably, we should not need to alter any content or perform any intermediate calculations, such as taking sums or averages.
Please write the Python code necessary to convert the target rows to the correct data subtype.

You can access the main dataframe using 'main_df', and you can reference the target rows by row id or by value.
For convenience, we have provided an 'row_ids' variable as a list containing all the row ids to be converted. You also have access to Pandas as 'pd', so there is no need to import it.
Your final response should only contain well-formatted Python code with comments, while abstaining from any additional text or explanations after the output.

For example,
#############
_Target_
Datatype: datetime, Subtype: date, Pandas dtype: datetime64[ns]
Column: downloadDate

_Samples_
2022-09-28
2022-09-27
2022-09-29
2022-10-02
2022-09-30
2022-09-27 00:01:00 <-- [117, 135, 245]
2022-10-01
2022-10-03
[36 other unique values ...]

_Output_
```python
# trim timestamps into just a date portion
main_df['downloadDate'] = main_df['downloadDate'].astype(str).str[:10]
main_df['downloadDate'] = pd.to_datetime(db.Analytics['downloadDate'], format='%Y-%m-%d')
```

_Target_
datatype: number, subtype: currency, Pandas dtype: float64
Column: Amount Paid

_Samples_
$25.00
$30.00
$32.00
$39.00
$28.00
$40.00
thirty-two dollars <-- [98]
$29.00
[86 other unique values ...]

_Output_
```python
# assigning a new value at the given row id is straightforward
main_df.loc[98, 'Amount Paid'] = 32.00
```

_Target_
datatype: datetime, subtype: date, Pandas dtype: datetime64[ns]
Column: downloadDate

_Samples_
2003-08-21
2003-08-22
08242003 <-- [584, 587, 588]
2003-08-23
2003-08-25
08252003 <-- [585]
2003-09-01
2003-08-29
2003-08-30
[93 other unique values ...]

_Output_
```python
# convert the date to a standard format and then to a datetime object
main_df.loc[row_ids, 'downloadDate'] = pd.to_datetime(main_df.loc[row_ids, 'downloadDate'], format='%m%d%Y')
```

_Target_
datatype: text, subtype: url, Pandas dtype: object
Column: referral_source

_Samples_
shopify.com <-- [61]
soleda.ai <-- [62]
orbitalinsights.ai <-- [63]
microsoft.azurelake.com <-- [64]
aws.amazon.com <-- [75]
soleda.ai <-- [78]
redhat.com <-- [79]
cloud.google.com <-- [82]
[304 other unique values ...]

_Output_
```python
# As far as the values are concerned, no changes are needed to convert them to a URL
pass
```

_Target_
datatype: datetime, subtype: minute, Pandas dtype: float64
Column: Avg. Session Duration

_Samples_
1.312
1.425
1.679
$1.89 <-- [75]
2.126
$2.34 <-- [206]
2.555
2.782
[493 other unique values ...]

_Output_
```python
# strip the dollar sign and convert to a float
main_df['Avg. Session Duration'] = (
  main_df['Avg. Session Duration']
  .astype(str).str.replace('$', '').astype(float)
)
```

_Target_
datatype: text, subtype: phone, Pandas dtype: object
Column: contact_info

_Samples_
(650)845-8221
(650)665-9725
(650)783-4412
(556)253-5880
(450)609-2303
+1 (650)774 -8901 <-- [75]
+ 1 (650)595-2121 <-- [206]
************ <-- [256]
(650)822-0403
+1 (650)903-0403 <-- [306]
[1926 other unique values ...]

_Output_
```python
# remove all non-numeric characters and then add back the parentheses and hyphens
db.conference_downloaded['contact_info'] = (
  db.conference_downloaded['contact_info']
  .str.replace(r'\D', '', regex=True)
  .str.extract(r'(\d{{10}})', expand=False)
  .apply(lambda x: f'({{x[:3]}}){{x[3:6]}}-{{x[6:]}}')
)
```

_Target_
datatype: datetime, subtype: time, Pandas dtype: object
Column: estimated arrival

_Samples_
User: The ones with numbers should be converted to times.
09:00
530 <-- [155]
630 <-- [125]
16:00
missing
615 <-- [204]
07:30
06:00
13:30
[77 other unique values ...]

_Output_
```python
# Extract the target rows for conversion
target_rows = main_df.loc[row_ids, 'estimated arrival']
# Perform the conversion in a general manner
main_df.loc[row_ids, 'estimated arrival'] = (
  target_rows.astype(str).str.zfill(4).str.slice(0, 2) + ':' + target_rows.str.slice(2)
)
# convert the entire column to time format
main_df['estimated arrival'] = pd.to_datetime(main_df['estimated arrival'], format='%H:%M').dt.time
```

_Target_
datatype: number, subtype: whole, Pandas dtype: int64
Column: TotalClicks

_Samples_
8
10
11
7
6.0 <-- 15 instances
9
12
6
0
[230 other unique values ...]

_Output_
```python
# round the float values to convert into whole numbers
main_df['TotalClicks'] = main_df['TotalClicks'].round().astype(int)
```
#############
Now it's your turn! Given the conversation history and supporting details, please convert the target rows into the {datatype} data subtype.

_Conversation History_
{history}

_Target_
datatype: {datatype}, subtype: {subtype}, Pandas dtype: {dtype}
Column: {column}

_Samples_
{samples}

_Output_
"""

subtype_prompt = """Given the conversation history and unique values, we need to determine the most likely subtype for the {column} column.
Please consider the conversation history and the unique values to determine the subtype.
If the subtype is not clear, output 'unsure'.
Your final response should only contain the subtype as a string, without any additional text or explanations.

For example,
#############
_Conversation History_
User: Can you check the data for any inconsistencies?
Agent: Sure, what specifically should I be looking for?
User: I'm concerned about the data in the 'Status' column.

_Unique Values_
| Status   |
|:---------|
| Active   |
| Inactive |
| Active   |
| Active   |

_Output_
```text
categorical
```

#############
_Conversation History_
{history}

_Unique Values_
{preview}

_Output_
"""

imputation_filtering_prompt = """Our main goal is to fill in the blanks in the target column: {target_column}.
Before performing imputation, we will first need to:
  a. identify the relevant source columns to focus on
  b. determine the default value that represents a blank
  c. filter for the appropriate rows to impute

To aid you in this task, you will be provided with:
  * the name of the target column to be imputed
  * recent conversation history for context
  * a preview of randomly sampled rows from table_df
    - table_df will contain the target column, as well as the candidate source columns
    - a true null in the table will be displayed as: {true_null}
  * the distribution of unique values in the target column:
    - the top 10 unique values and their counts
    - followed by the count of empty rows that are truly null
    - finally, the number of all other unique values not among the top 10
    - if there are less than 10 unique values, then the final row will not be shown because it would be a count of zero

When deciding where to narrow our focus:
  * source_columns
    - only include column(s) needed to impute the target values
    - it's possible that the target column itself may be a useful source of information, in which case you should include it as well
  * blank_value
    - use the preview to see if there is a default string or number that represents a blank
    - common examples include 'null', 'none', 'na', 0, or 'unknown'
    - the default value cannot be {true_null} because this is an actual null value
    - if there is no clear default value, then you should set blank_value as an empty string ''
  * row_mask
    - the row mask should be a boolean expression that can be evaluated using exec()
    - use 'table_df' as the name of the dataframe within the expression
    - be sure to add parentheses around each condition to ensure proper order of operations
    - typical checks include making sure the source column is not null, the target column matches the default value, or other relevant conditions based on the conversation history

Please consider where we should focus our attention, and also think about the type of relationship between the source and target columns.
In particular, can the target be derived from the source with a formula, or is it more of a mapping exercise based on world knowledge?
Your entire response should only contain well-formatted JSON including keys for your thought (string), source_columns (list), blank_value (string), and row_mask (string).
There should be no additional text or explanations before or after the JSON output.

For example,
---
1) Target Column: last_interaction

_Conversation History_
User: How many active users have there been in the last 90 days?
Agent: Before moving ahead, how to you define active users? Should I use last_interaction date?
User: Yeah, that sounds right.
Agent: Ok, there are many missing values in the last_interaction column. Should I ignore those users in my calculations?
User: No, can you fill them in using other activity dates and times?

_Sample Rows_
| account_creation_date | purchase_timing | email_open_timestamp | last_interaction    | website_visit_logs  |
|:----------------------|-----------------|----------------------|---------------------|---------------------|
| 2023-02-15            | early           | 2024-01-12T14:35:27  | 2024-03-10T16:22:10 | 2024-02-03T09:12:55 |
| 2022-11-03            | late            | 2023-12-20T08:45:13  | null                | 2024-01-15T11:30:42 |
| 2023-07-21            | early           | 2023-12-05T17:20:33  | 2024-02-28T10:15:44 | 2024-01-22T14:05:18 |
| 2022-05-18            | on-time         | 2023-11-20T09:30:21  | 2024-02-17T15:22:08 | 2023-12-10T13:45:37 |
| 2023-09-30            | null            | 2024-01-05T11:25:42  | 2024-01-05T11:25:42 | null                |

_Unique Values_
2024-02-28T10:15:44 - 2 rows
2024-02-17T15:22:08 - 1 row
2024-03-10T16:22:10 - 1 row
2024-01-12T14:35:27 - 1 row
2024-01-05T11:25:42 - 1 row
2024-12-20T08:45:13 - 1 row
2024-12-05T17:20:33 - 1 row
2024-11-20T09:30:21 - 1 row
2024-10-10T14:10:20 - 1 row
2024-09-30T10:05:10 - 1 row
56 empty rows
1406 remaining values ...

_Output_
```json
{{
  "thought": "The target column contains the most recent interaction across all activities, so we should look for non-null dates and take the maximum. We can ignore the purchase_timing column because it does not contain date information.",
  "source_columns": ["account_creation_date", "email_open_timestamp", "website_visit_logs"],
  "blank_value": "null",
  "row_mask": "(table_df['last_interaction'].isna()) | (table_df['last_interaction'] == 'null')"
}}
```

2) Target column: county

_Conversation History_
User: Figure out the missing counties for me in the LoanApplications table.

_Sample Rows_
| zip_code | county        |
|:---------|---------------|
| 94172    | San Francisco |
| 90280    | Los Angeles   |
| 94017    | San Mateo     |
| 94121    | San Francisco |
| 94118    | San Francisco |
| 94083    | San Mateo     |
| 95103    | Santa Clara   |
| 94130    | San Francisco |

_Unique Values_
San Mateo - 212 rows
Santa Clara - 108 rows
San Francisco - 170 rows
Los Angeles - 73 rows
Riverside - 52 rows
San Bernardino - 36 rows
208 empty rows

_Output_
```json
{{
  "thought": "The valid target values are all county names in California, while the source values are zip codes. There is no logical relationship between the two, so a mapping is the right choice.",
  "source_columns": ["zip_code"],
  "blank_value": "",
  "row_mask": "((table_df['county'].isna()) | (table_df['county'] == '')) & (table_df['zip_code'].notna())"
}}
```

3) Target column: month

_Conversation History_
User: it's the month column that's causing problems
Agent: OK, I see that many of the values are missing, should I try to fill them in?
User: Yea, go for it. Just note, we really only care about campaigns that have been launched.

_Sample Rows_
| created_date | launched_date |  month  |
|:-------------|---------------|--------:|
| 04/13/2024   | N/A           | April   |
| 05/15/2024   | N/A           | May     |
| 04/25/2024   | 05/01/2024    | April   |
| 03/30/2024   | N/A           | March   |
| Mar          | N/A           | N/A     |
| 04/10/2024   | N/A           | April   |
| 5/2/2024     | 06/01/2024    | N/A     |
| 06/12/2024   | 06/01/2024    | June    |

_Unique Values_
April - 235 rows
March - 208 rows
May - 198 rows
June - 182 rows
63 empty rows

_Output_
```json
{{
  "thought": "Target values are months from March to June, which are derived from the created_date. I will also need the launched_date to filter for active campaigns. Some dates are in the wrong format, which I can standardize with code.",
  "source_columns": ["created_date", "launched_date"],
  "blank_value": "N/A",
  "row_mask": "((table_df['month'].isna()) | (table_df['month'] == 'N/A')) & (table_df['created_date'].notna()) & (table_df['launched_date'].notna())"
}}
```

4) Target column: daily active users

_Conversation History_
User: We lost tracking data for a few days in March after the system upgrade, do you see it?
Agent: Yes, I see the gap in daily active users data from March 2nd to March 5th.
User: Can you smooth that out for the report?

_Sample Rows_
| date       | day of week | feature releases | daily active users |
|:-----------|-------------|------------------|--------------------|
| 2024-03-08 | Friday      | None             | 15,420             |
| 2024-03-09 | Saturday    | Search Upgrade   | 14,830             |
| 2024-03-10 | Sunday      | None             | 0                  |
| 2024-03-11 | Monday      | None             | 0                  |
| 2024-03-12 | Tuesday     | None             | 0                  |
| 2024-03-13 | Wednesday   | None             | 16,250             |

_Unique Values_
1,956 - 3 rows
1,832 - 2 rows
1,560 - 2 rows
1,782 - 1 row
2,014 - 1 row
2,246 - 1 row
1,838 - 1 row
1,420 - 1 row
1,752 - 1 row
1,941 - 1 row
97 empty rows
166 remaining values ...

_Output_
```json
{{
  "thought": "I can use linear interpolation between the known values before and after the gap. The date is useful for ordering. The default value is the number 0, rather than a string.",
  "source_columns": ["date", "daily active users"],
  "blank_value": 0,
  "row_mask": "(table_df['daily active users'].isna()) | (table_df['daily active users'] == 0)"
}}
```

5) Target column: State

_Conversation History_
User: Can we analyze shipping costs by state?
Agent: Certainly, I can break down the shipping costs by state. However, I notice that some states are missing in our data.
User: Oh, I see. Can you fill in the null states for me?

_Sample Rows_
| Address                                   | Location | State |
|:------------------------------------------|----------|-------|
| 723 Market St, San Diego, CA 92101        | South    | CA    |
| 455 Washington Ave, Eugene, OR 97401      | North    | OR    |
| 889 Wilshire Blvd, Los Angeles, CA 90017  | South    | CA    |
| 101 Madison St, Seattle, WA 98104         | North    | WA    |
| 2400 Valencia St, San Francisco, CA 94103 | South    | CA    |
| 675 Morrison St, Portland, OR 97205       | North    | OR    |

_Unique Values_
CA - 577 rows
OR - 242 rows
WA - 201 rows
CO - 156 rows
AZ - 104 rows
NV - 38 rows
135 empty rows

_Output_
```json
{{
  "thought": "Start by splitting the address by comma and grabbing the first two characters. Given there are only six options, I can then verify whether the extracted characters match a valid state abbreviation. For every row still missing a state, I will use the city to form a mapping.",
  "source_columns": ["Address", "State"],
  "blank_value": "",
  "row_mask": "((table_df['State'].isna()) | (table_df['State'] == '')) & (table_df['Address'].notna())"
}}
```

6) Target column: est_deal_amount

_Conversation History_
User: These new opportunities don't have projected values.
Agent: You're right. I see several new opportunities are missing estimated deal amounts. Would you like me to help fill in those estimates?
User: Yes, use our historical data to get a ballpark figure.

_Sample Rows_
| client_size | product_category     | client_industry    | closed_deal_amount   | est_deal_amount |
|:------------|----------------------|--------------------|----------------------|-----------------|
| Enterprise  | Cloud Infrastructure | Healthcare         | $758,290             | $548,000        |
| SMB         | CRM Software         | Retail             | $28,355              | $19,500         |
| Enterprise  | Security Solutions   | Manufacturing      | $556,248             | null            |
| Mid-Market  | Data Analytics       | Technology         | $210,175             | null            |
| Mid-Market  | Data Analytics       | Financial Services | $121,451             | $82,500         |
| Mid-Market  | Marketing Automation | Technology         | null                 | $55,000         |

_Unique Values_
$55,000 - 15 rows
$116,000 - 13 rows
$75,000 - 9 rows
$23,000 - 8 rows
$150,000 - 7 rows
$235,000 - 5 rows
$128,000 - 5 rows
$37,500 - 3 rows
$62,500 - 3 rows
$87,500 - 2 rows
724 empty rows
209 remaining values ...

_Output_
```json
{{
  "thought": "Group historical deals by size, category, and industry to calculate median sizes for closed deals. Assign the matching value for each empty est_deal_amount based on its attributes.",
  "source_columns": ["client_size", "product_category", "client_industry", "closed_deal_amount"],
  "blank_value": "null",
  "row_mask": "(table_df['est_deal_amount'].isna()) | (table_df['est_deal_amount'] == 'null')"
}}
```

7) Target column: Industry

_Conversation History_
User: I'm not sure, can you help me figure out the missing industries for me?

_Sample Rows_
| Company Name                 | HasContated | HasResponded | Industry                            |
|:-----------------------------|-------------|--------------|-------------------------------------|
| Summit Property Management   | True        | False        | Real Estate and Rental and Leasing  |
| Wellness Alliance Medical    | True        | True         | Health Care and Social Assistance   |
| Spectrum Data Systems        | True        | True         | Information                         |
| Mosaic Creative Studios      | False       | False        | Arts, Entertainment, and Recreation |
| Centrix Holdings Corporation | True        | True         | <N/A>                               |
| Guardian Trust Financial     | False       | False        | Finance and Insurance               |
| Vertex Research Associates   | True        | True         | <N/A>                               |
| Landmark Entertainment Group | False       | False        | Arts, Entertainment, and Recreation |

_Unique Values_
Finance and Insurance - 114 rows
Arts, Entertainment, and Recreation - 102 rows
Real Estate and Rental and Leasing - 95 rows
Professional, Scientific, and Technical Services - 82 rows
Health Care and Social Assistance - 73 rows
Information - 57 rows
Management of Companies and Enterprises - 31 rows
Manufacturing - 35 rows
Retail Trade - 23 rows
Accommodation and Food Services - 11 rows
813 empty rows
4 remaining values ...

_Output_
```json
{{
  "thought": "There is no functional way to impute the missing industries, so the best method is to use world knowledge to form a mapping based on the company names.",
  "source_columns": ["Company Name"],
  "blank_value": "",
  "row_mask": "((table_df['Industry'].isna()) | (table_df['Industry'] == '')) & (table_df['Company Name'].notna())"
}}
```
---
Now it's your turn! Please determine the relevant source columns and row mask to focus on the appropriate data to impute, along with appropriate blank value.
Target column: {target_column}

_Conversation History_
{history}

_Sample Rows_
{data_preview}

_Unique Values_
{unique_values}

_Output_
"""

imputation_method_prompt = """Our main goal is to fill in the blanks in the target column: {target_column}.
Please start by determining the most appropriate method of imputation, chosen from:
  * func: apply a function which takes in the source values as input and returns the target value
    - choose this when there is a clear logical relationship between the source and target values
    - choosing 'func' is also appropriate when any of the following conditions are met:
      a. you are interpolating missing values based on a sequence or pattern
      b. there is a formula that can be applied to the source values to obtain the target values
      c. data preparation or transformations need to be applied during the imputation process
      d. the target values can be extracted from the source values, because it is a subset of the source
  * map: fill in the missing values with a specific value based on a mapping from source values to target values
    - choose this when the connection from source to target is a matter of world knowledge, such as mapping a country to its capital
    - choosing 'map' is also appropriate when any of the following conditions are met:
      a. the source and target values are both categorical
      b. applying transformations through code would be unnecessarily complex or simply infeasible
      c. the target values are not clearly defined by the source values, and require manual mapping to resolve
    - if a mapping is *programatically* created based on the table data, then this is actually a 'func' method rather than 'map'
  * both: use a function to derive some portion of the target values, and then use a mapping to fill in the rest
    - choose this if the scenario matches at least one condition in both the 'func' and 'map' categories
    - when choosing this option, your thought process should include details on how you plan to split the work between the function and mapping

To aid you in this task, you will be provided with the conversation history, as well as a preview of randomly sampled rows from the source and target columns.
In addition, you will also be provided with the distribution of unique values in the target column:
  * the top 10 unique values and their counts
  * followed by the count of blank rows that need to be imputed
  * finally, the number of all other unique values not among the top 10
  * if there are less than 10 unique values, then the final row will not be shown because it would be a count of zero

Additionally, we will narrow our focus by choosing only the columns that are necessary to achieve our goal:
  * the full list of source columns is provided as the headers within the sample rows
  * if we choose the 'func' method, we should only select the columns that are actually used in the function
  * if we choose the 'map' method, we should only select the columns that will serve as keys in our mapping
  * if we choose the 'both' method, we should select columns used by either the function or mapping
  * only include the target column if we are using it to derive the missing values from the same column

Your entire response should only contain well-formatted JSON including your thought (string), chosen method (token), and selected columns (list).
Our goal is imputation, so when making your decision, focus on rows that have missing values, rather than the target column as a whole.
There should be no additional text or explanations before or after the JSON output.

For example,
---
1) Target Column: last_interaction
_Conversation History_
User: How many active users have there been in the last 90 days?
Agent: Before moving ahead, how to you define active users? Should I use last_interaction date?
User: Yeah, that sounds right.
Agent: Ok, there are many missing values in the last_interaction column. Should I ignore those users in my calculations?
User: No, can you fill them in using other activity dates and times?

_Sample Rows_
| account_creation_date | purchase_dates | email_open_timestamp | last_interaction    | website_visit_logs  |
|:----------------------|----------------|----------------------|---------------------|---------------------|
| 2023-02-15            | 2023-04-10     | 2024-01-12T14:35:27  | 2024-03-10T16:22:10 | 2024-02-03T09:12:55 |
| 2022-11-03            | 2023-01-17     | 2023-12-20T08:45:13  | null                | 2024-01-15T11:30:42 |
| 2023-07-21            | 2023-08-05     | 2023-12-05T17:20:33  | 2024-02-28T10:15:44 | 2024-01-22T14:05:18 |
| 2022-05-18            | 2022-08-12     | 2023-11-20T09:30:21  | 2024-02-17T15:22:08 | 2023-12-10T13:45:37 |
| 2023-09-30            | null           | 2024-01-05T11:25:42  | 2024-01-05T11:25:42 | null                |

_Unique Values_
2024-02-28T10:15:44 - 2 rows
2024-02-17T15:22:08 - 1 row
2024-03-10T16:22:10 - 1 row
2024-01-12T14:35:27 - 1 row
2024-01-05T11:25:42 - 1 row
2024-12-20T08:45:13 - 1 row
2024-12-05T17:20:33 - 1 row
2024-11-20T09:30:21 - 1 row
2024-10-10T14:10:20 - 1 row
2024-09-30T10:05:10 - 1 row
56 empty rows
1406 remaining values ...

_Output_
```json
{{
  "thought": "Find the most recent interaction across all activities involves looking for non-null dates and taking the maximum. This is a formula I can write with a function.",
  "method": "func",
  "columns": ["account_creation_date", "purchase_dates", "email_open_timestamp", "website_visit_logs"]
}}
```

2) Target column: county

_Conversation History_
User: Figure out the missing counties for me in the LoanApplications table.

_Sample Rows_
| zip_code | county        |
|:---------|---------------|
| 94172    | San Francisco |
| 90280    | Los Angeles   |
| 94017    | San Mateo     |
| 94121    | San Francisco |
| 94118    | San Francisco |
| 94083    | San Mateo     |
| 95103    | Santa Clara   |
| 94130    | San Francisco |

_Unique Values_
San Mateo - 212 rows
Santa Clara - 108 rows
San Francisco - 170 rows
Los Angeles - 73 rows
Riverside - 52 rows
San Bernardino - 36 rows
208 empty rows

_Output_
```json
{{
  "thought": "The valid target values are all county names in California, while the source values are zip codes. There is no logical relationship between the two, so a mapping is the right choice.",
  "method": "map",
  "columns": ["zip_code"]
}}
```

3) Target column: month

_Conversation History_
User: it's the month column that's causing problems
Agent: OK, I see that many of the values are missing, should I try to fill them in?
User: Yea, go for it

_Sample Rows_
| created_date | launched_date |  month  |
|:-------------|---------------|--------:|
| 04/13/2024   | N/A           | April   |
| 05/15/2024   | N/A           | May     |
| 04/25/2024   | 05/01/2024    | April   |
| 03/30/2024   | N/A           | March   |
| Mar          | N/A           | null    |
| 04/10/2024   | N/A           | April   |
| 5/2/2024     | N/A           | null    |
| 06/12/2024   | 05/01/2024    | June    |

_Unique Values_
April - 235 rows
March - 208 rows
May - 198 rows
June - 182 rows
63 empty rows

_Output_
```json
{{
  "thought": "Target values are months from March to June, which are derived from the created_date. Some dates are in the wrong format, which I can standardize first using some code. Then, I can deal with the rest using a mapping.",
  "method": "both",
  "columns": ["created_date"]
}}
```

4) Target column: daily active users

_Conversation History_
User: We lost tracking data for a few days in March after the system upgrade, do you see it?
Agent: Yes, I see the gap in daily active users data from March 2nd to March 5th.
User: Can you smooth that out for the report?

_Sample Rows_
| date       | day of week | feature releases | daily active users |
|:-----------|-------------|------------------|--------------------|
| 2024-03-08 | Friday      | None             | 15,420             |
| 2024-03-09 | Saturday    | Search Upgrade   | 14,830             |
| 2024-03-10 | Sunday      | None             | null               |
| 2024-03-11 | Monday      | None             | null               |
| 2024-03-12 | Tuesday     | None             | null               |
| 2024-03-13 | Wednesday   | None             | 16,250             |

_Unique Values_
1,956 - 3 rows
1,832 - 2 rows
1,560 - 2 rows
1,782 - 1 row
2,014 - 1 row
2,246 - 1 row
1,838 - 1 row
1,420 - 1 row
1,752 - 1 row
1,941 - 1 row
97 empty rows
166 remaining values ...

_Output_
```json
{{
  "thought": "I can use linear interpolation between the known values before and after the gap. The date is useful for ordering. I will resolve all blanks for completeness.",
  "method": "func",
  "columns": ["date", "daily active users"]
}}
```

5) Target column: State

_Conversation History_
User: Can we analyze shipping costs by state?
Agent: Certainly, I can break down the shipping costs by state. However, I notice that some states are missing in our data.
User: Oh, I see. Can you fill in the null states for me?

_Sample Rows_
| Address                                   | Zip Code | State |
|:------------------------------------------|----------|-------|
| 723 Market St, San Diego, CA 92101        | 92101    | CA    |
| 455 Washington Ave, Eugene, OR 97401      | 97401    | OR    |
| 889 Wilshire Blvd, Los Angeles, CA 90017  | 90017    | CA    |
| 101 Madison St, Seattle, WA 98104         | 98104    | WA    |
| 2400 Valencia St, San Francisco, CA 94103 | 94103    | CA    |
| 675 Morrison St, Portland, OR 97205       | 97205    | OR    |

_Unique Values_
CA - 577 rows
OR - 242 rows
WA - 201 rows
CO - 156 rows
AZ - 104 rows
NV - 38 rows
135 empty rows

_Output_
```json
{{
  "thought": "Start by splitting the address by comma and grabbing the first two characters. Given there are only six options, I can then verify whether the extracted characters match a valid state abbreviation. For every row still missing a state, I will use the city to form a mapping.",
  "method": "both",
  "columns": ["Address"]
}}
```

6) Target column: est_deal_amount

_Conversation History_
User: These new opportunities don't have projected values.
Agent: You're right. I see several new opportunities are missing estimated deal amounts. Would you like me to help fill in those estimates?
User: Yes, use our historical data to get a ballpark figure.

_Sample Rows_
| client_size | product_category     | client_industry    | closed_deal_amount   | est_deal_amount |
|:------------|----------------------|--------------------|----------------------|-----------------|
| Enterprise  | Cloud Infrastructure | Healthcare         | $758,290             | $548,000        |
| SMB         | CRM Software         | Retail             | $28,355              | $19,500         |
| Enterprise  | Security Solutions   | Manufacturing      | $556,248             | null            |
| Mid-Market  | Data Analytics       | Technology         | $210,175             | null            |
| Mid-Market  | Data Analytics       | Financial Services | $121,451             | $82,500         |
| Mid-Market  | Marketing Automation | Technology         | null                 | $55,000         |

_Unique Values_
$55,000 - 15 rows
$116,000 - 13 rows
$75,000 - 9 rows
$23,000 - 8 rows
$150,000 - 7 rows
$235,000 - 5 rows
$128,000 - 5 rows
$37,500 - 3 rows
$62,500 - 3 rows
$87,500 - 2 rows
724 empty rows
209 remaining values ...

_Output_
```json
{{
  "thought": "Group historical deals by size, category, and industry to calculate median sizes for closed deals. Assign the matching value for each empty est_deal_amount based on its attributes.",
  "method": "func",
  "columns": ["client_size", "product_category", "client_industry", "closed_deal_amount"]
}}
```

7) Target column: Industry

_Conversation History_
User: I'm not sure, can you help me figure out the missing industries for me?

_Sample Rows_
| Company Name                 | HasContated | HasResponded | Industry                                |
|:-----------------------------|-------------|--------------|-----------------------------------------|
| Summit Property Management   | True        | False        | Real Estate and Rental and Leasing      |
| Wellness Alliance Medical    | True        | True         | Health Care and Social Assistance       |
| Spectrum Data Systems        | True        | True         | Information                             |
| Mosaic Creative Studios      | False       | False        | Arts, Entertainment, and Recreation     |
| Centrix Holdings Corporation | True        | True         | Management of Companies and Enterprises |
| Guardian Trust Financial     | False       | False        | Finance and Insurance                   |

_Unique Values_
Finance and Insurance - 114 rows
Arts, Entertainment, and Recreation - 102 rows
Real Estate and Rental and Leasing - 95 rows
Professional, Scientific, and Technical Services - 82 rows
Health Care and Social Assistance - 73 rows
Information - 57 rows
Management of Companies and Enterprises - 31 rows
Manufacturing - 35 rows
Retail Trade - 23 rows
Accommodation and Food Services - 11 rows
813 empty rows
4 remaining values ...

_Output_
```json
{{ 
  "thought": "There is no functional way to impute the missing industries, so the best method is to use world knowledge to form a mapping based on the company names.",
  "method": "map",
  "columns": ["Company Name"]
}}
```
---
Please determine the most appropriate method for imputation, chosen only from the following options: [map, func, both]
Then, select the relevant source columns that are necessary. Do not include any other text before or after the JSON output.
Target column: {target_column}

_Conversation History_
{history}

_Sample Rows_
{data_preview}

_Unique Values_
{unique_values}

_Output_
"""

impute_function_prompt = """Your task is to write a Python function called 'fill_blanks' to help impute the null values in the target column.
We will proceed in two phases, first by writing a function to impute as many values as possible, and then by creating a mapping for the remaining values.
To aid you in this task, you will be provided the target column name, the conversation history, and some previous thoughts to guide code generations.
You will also be shown a preview of sampled rows from 'main_df':
  * rows with non-null target values are purposely chosen so that you can see what valid values look like
  * pay attention to details such as capitalization and abbreviations (when dealing with text), or range and precision (when dealing with numbers)
  * rows with blank target values will be appended to the bottom of the samples if they don't already appear earlier
  * these rows are useful for understanding why a target value may be missing, or issues you may need to account for when writing the function

In addition, you can use Pandas (as 'pd'), NumPy (as 'np'), and regex (as 're') if needed. You should *not* import any other libraries.
Do not create copies of the dataframe, and instead perform operations in place whenever possible.
Occasionally, it may be unclear how to fill in the missing target values. If this happens, first check if the user has provided any hints on how to deal with unresolved values.
Otherwise, it is perfectly acceptable to leave the dataframe unchanged. In fact, you should prefer to leave the targets blank rather than making any assumptions.

There are potentially many more blank rows beyond the samples shown, so do NOT try to form a mapping in code! Leave that to the next phase.
More specifically, you should keep the target rows unfilled when any of the following conditions are met:
  * filling the missing values is largely based on world knowledge, rather than a formula applied to the source values
  * the source and target values are both categorical, and there are only a small number of cases to resolve
  * applying transformations through code would be unnecessarily complex or simply infeasible
  * there is no clear formula to derive the target values from the source values, so a mapping is more appropriate

Please start by considering the conversation history and the format of existing target values so you can match their style.
Then generate a single function named `fill_blanks(main_df)`, which takes in a dataframe as input and returns the modified dataframe.
The function should be kept relatively short and simple, and should not exceed 32 lines of code. If there is an edge case, leave that for the next phase instead!
If a mapping is appropriate, then you should instead return the dataframe without any changes. Keep the function as simple as possible by avoiding excessive complexity.
Your entire response should contain directly executable Python code with inline comments to help with reasoning, but no additional text or explanations outside the code block.

For example,
---
## Target Column: last_interaction
_Conversation History_
User: How many customers haven't engaged with us in the last 90 days?
Agent: Based on the data, approximately 3,200 customers haven't had any interaction with us in the past 90 days.
User: I notice a lot of blank values in the last interaction data. Can we fill those in somehow?

_Sample Rows_
| account_creation_date | purchase_dates | email_open_timestamp | last_interaction    | website_visit_logs  |
|:----------------------|----------------|----------------------|---------------------|---------------------|
| 2023-02-15            | 2023-04-10     | 2024-01-12T14:35:27  | 2024-03-10T16:22:10 | 2024-02-03T09:12:55 |
| 2023-07-21            | 2023-08-05     | 2023-12-05T17:20:33  | 2024-02-28T10:15:44 | 2024-01-22T14:05:18 |
| 2022-05-18            | 2022-08-12     | 2023-11-20T09:30:21  | 2024-02-17T15:22:08 | 2023-12-10T13:45:37 |
| 2023-09-30            | null           | 2024-01-05T11:25:42  | 2024-01-05T11:25:42 | null                |
| 2022-11-03            | 2023-01-17     | 2023-12-20T08:45:13  | null                | 2024-01-15T11:30:42 |
| 2023-07-04            | 2023-06-15     | null                 | null                | 2024-01-22T14:05:18 |

_Previous Thought_
Find the most recent interaction across all activities involves looking for non-null dates and taking the maximum. This is best done with a function.

_Output_
```python
def fill_blanks(main_df):
  # Convert all relevant columns to datetime format
  date_cols = ['account_creation_date', 'purchase_dates', 'email_open_timestamp', 'website_visit_logs', 'last_interaction']
  for col in date_cols:
    main_df[col] = pd.to_datetime(main_df[col], errors='coerce')
    
  # For each row with missing last_interaction
  mask = main_df['last_interaction'].isna()  
  for idx in main_df[mask].index:
    # Get dates from all interaction columns
    interaction_dates = [
      main_df.at[idx, 'email_open_timestamp'],
      main_df.at[idx, 'website_visit_logs'],
      main_df.at[idx, 'purchase_dates'],
      main_df.at[idx, 'account_creation_date']  # Last resort
    ]
    # If we have any valid dates, use the most recent one
    valid_dates = [d for d in interaction_dates if pd.notna(d)]
    if valid_dates:
      main_df.at[idx, 'last_interaction'] = max(valid_dates)
  return main_df
```

## Target Column: county

_Conversation History_
User: Figure out the missing counties for me in the LoanApplications table.

_Sample Rows_
| zip_code | county        |
|:---------|---------------|
| 94172    | San Francisco |
| 90280    | Los Angeles   |
| 94017    | San Mateo     |
| 94121    | San Francisco |
| 94118    | San Francisco |
| 94083    | San Mateo     |
| 95103    | Santa Clara   |
| 94130    | San Francisco |
| 91331    | <N/A>         |
| 92336    | <N/A>         |
| 95002    | <N/A>         |

_Previous Thought_
The valid target values are all county names in California, while the source values are zip codes. There is no logical relationship between the two, so a mapping is the right choice.

_Output_
```python
def fill_blanks(main_df):
  # mapping is most appropriate for this task, so I should leave the dataframe as is
  mapping = {{ }}
  return main_df
```

## Target Column: est_deal_amount
_Conversation History_
User: These new opportunities don't have projected values.
Agent: You're right. I see several new opportunities are missing estimated deal amounts. Would you like me to help fill in those estimates?
User: Yes, use our historical data to get a ballpark figure.

_Sample Rows_
| client_size | product_category     | client_industry    | closed_deal_amount   | est_deal_amount |
|:------------|----------------------|--------------------|----------------------|-----------------|
| Enterprise  | Cloud Infrastructure | Healthcare         | $758,290             | $548,000        |
| SMB         | CRM Software         | Retail             | $28,355              | $19,500         |
| Mid-Market  | Data Analytics       | Financial Services | $82,451              | $121,500        |
| Mid-Market  | Marketing Automation | Technology         | $0                   | $55,000         |
| Enterprise  | Security Solutions   | Manufacturing      | $556,248             | $0              |
| Mid-Market  | Data Analytics       | Technology         | $210,175             | $0              |

_Previous Thought_
Group historical deals by size, category, and industry to calculate median sizes for closed deals. Assign the matching value for each empty est_deal_amount based on its attributes.

_Output_
```python
def fill_blanks(main_df):
  # Group by key attributes and calculate median deal size for known values
  grouped_medians = main_df[main_df['closed_deal_amount'].notna()].groupby(
    ['client_size', 'product_category', 'client_industry']
  )['closed_deal_amount'].median()
  # Calculate global median for fallback
  global_median = main_df['est_deal_amount'].median()

  # Fill in missing values
  mask = main_df['est_deal_amount'].isna() | (main_df['est_deal_amount'] == 0)
  for idx in main_df[mask].index:
    client_size = main_df.at[idx, 'client_size']
    product_category = main_df.at[idx, 'product_category']
    client_industry = main_df.at[idx, 'client_industry']
    
    # Try to find median for the exact combination
    try:
      median_amount = grouped_medians[(client_size, product_category, client_industry)]
      main_df.at[idx, 'est_deal_amount'] = median_amount
    except KeyError:
      # Just focus on company size if no match is found
      size_median = main_df[
        (main_df['client_size'] == client_size) &
        (main_df['closed_deal_amount'].notna())
      ]['closed_deal_amount'].median()

      main_df.at[idx, 'est_deal_amount'] = size_median if pd.notna(size_median) else global_median
  return main_df
```

## Target Column: destination_city
_Conversation History_
User: Can we analyze shipping costs by state?
Agent: Certainly, the average cost to ship to California is $10.52, Texas is $9.24, and Washington is $11.75. See table for more.
User: Oh, just noticed that many of the city names are missing. Can you fill those in somehow?

_Sample Rows_
| destination_city  | destination_state |
|:------------------|-------------------|
| Los Angeles       | CA                |
| San Jose          | CA                |
| Dover             | DE                |
| Jefferson City    | MO                |
| null              | PA                |
| Atlanta           | GA                |
| N/A               | TX                |
| N/A               | GA                |

_Previous Thought_
Filter for just states that have a missing city. Then, for each unique state, find the most common city associated with that state.
Finally, replace the missing city with the most common city for its state.

_Output_
```python
def fill_blanks(main_df):
  # Gather counts of cities per state from rows where both city and state are available
  state_city_counts = main_df[
    main_df['destination_city'].notna() & 
    main_df['destination_state'].notna()
  ].groupby(['destination_state', 'destination_city']).size().reset_index(
    name='count'
  )
  
  # Get the most common city for each state
  most_common_cities = (
    state_city_counts.loc[state_city_counts.groupby('destination_state')['count'].idxmax()]
    .set_index('destination_state')['destination_city']
    .to_dict()
  )
  
  # Fill in missing cities
  mask = main_df['destination_city'].isna() & main_df['destination_state'].notna()
  main_df.loc[mask, 'destination_city'] = main_df.loc[mask, 'destination_state'].map(most_common_cities)
  return main_df
```

## Target Column: daily active users
_Conversation History_
User: We lost tracking data for a few days in March after the system upgrade, do you see it?
Agent: Yes, I see the gap in daily active users data from March 2nd to March 5th.
User: Can you smooth that out for the report?

_Sample Rows_
| date       | day of week | feature releases | daily active users |
|:-----------|-------------|------------------|--------------------|
| 2024-03-08 | Friday      | None             | 15,420             |
| 2024-03-09 | Saturday    | Search Upgrade   | 14,830             |
| 2024-03-10 | Sunday      | None             | null               |
| 2024-03-11 | Monday      | None             | null               |
| 2024-03-12 | Tuesday     | None             | null               |
| 2024-03-13 | Wednesday   | None             | 16,250             |

_Previous Thought_
I can use linear interpolation between the known values before and after the gap. I will resolve all blanks for completeness.

_Output_
```python
def fill_blanks(main_df):
  # Convert to floats and dates for pre-processing
  if isinstance(main_df['daily_active_users'].iloc[0], str):
    main_df['daily_active_users'] = main_df['daily_active_users'].str.replace(',', '').astype('float')
  main_df['date'] = pd.to_datetime(main_df['date'])

  # Sort the dataframe by date to ensure correct interpolation
  main_df = main_df.sort_values('date')
  
  # Find continuous ranges of missing values
  main_df['missing'] = main_df['daily_active_users'].isna()
  main_df['group'] = (main_df['missing'] != main_df['missing'].shift()).cumsum()
  
  # Process each group of missing values
  for group_id, group_df in main_df[main_df['missing']].groupby('group'):
    if len(group_df) == 0: continue
    
    # Get indices of the missing range, skip if at edge of dataframe
    before_idx = group_df.index.min() - 1
    after_idx = group_df.index.max() + 1
    if before_idx < 0 or after_idx >= len(main_df):
      continue

    # Find the nearest non-null values, skip if no valid neighbors
    before_value = main_df.loc[before_idx, 'daily_active_users']
    after_value = main_df.loc[after_idx, 'daily_active_users']
    if pd.isna(before_value) or pd.isna(after_value):
      continue
    
    # Calculate the number of steps between the known values
    steps = (after_idx - before_idx)
    step_size = (after_value - before_value) / steps
    
    # Fill in the missing values with linear interpolation
    for i, idx in enumerate(range(before_idx + 1, after_idx)):
      main_df.at[idx, 'daily_active_users'] = before_value + step_size * (i + 1)
  
  # Clean up temporary columns
  main_df = main_df.drop(['missing', 'group'], axis=1)  
  return main_df
```

## Target Column: view_to_purchase_ratio
_Conversation History_
User: Some products don't have conversion rates.
Agent: You're right. I see several products, particularly in the Home Decor category, are missing view-to-purchase ratios.
User: Could we estimate those based on similar products?

_Sample Rows_
| product_category | price_point | discount_percentage | season          | competitor_pricing | view_to_purchase_ratio |
|:-----------------|-------------|---------------------|-----------------|--------------------|-----------------------:|
| Men's Clothes    | Premium     | 0%                  | Winter          | Higher             |                   2.8% |
| Women's Clothes  | Mid-range   | 15%                 | Summer          | Similar            |                   3.4% |
| Children's Shoes | Budget      | 20%                 | Back-to-School  | Lower              |                   4.2% |
| Home Decor       | Premium     | 10%                 | Holiday         | Similar            |                  <N/A> |
| Electronics      | Premium     | 5%                  | Black Friday    | Higher             |                   1.9% |
| Women's Clothes  | Premium     | 10%                 | Holiday         | Similar            |                  <N/A> |

_Previous Thought_
Identify similar products with the same category and price range that were available during the period with missing data.
Calculate the average CVR and apply to the rows with missing view-to-purchase ratios. I do NOT need to consider all the other columns.

_Output_
```python
def fill_blanks(main_df):
  # Group by product category and price point to calculate average conversion rates
  grouped_means = main_df[main_df['view_to_purchase_ratio'].notna()].groupby(
    ['product_category', 'price_point']
  )['view_to_purchase_ratio'].mean()
  # Calculate global average for fallback
  global_mean = main_df['view_to_purchase_ratio'].mean()
  
  # Fill in missing values
  mask = main_df['view_to_purchase_ratio'].isna()
  for idx in main_df[mask].index:
    product_category = main_df.at[idx, 'product_category']
    price_point = main_df.at[idx, 'price_point']
    
    # Try to find the average for this specific category and price point
    try:
      avg_ratio = grouped_means[(product_category, price_point)]
      main_df.at[idx, 'view_to_purchase_ratio'] = avg_ratio
    except KeyError:
      # Just focus on category if no match is found
      category_mean = main_df[
        (main_df['product_category'] == product_category) & 
        (main_df['view_to_purchase_ratio'].notna())
      ]['view_to_purchase_ratio'].mean()
      
      main_df.at[idx, 'view_to_purchase_ratio'] = category_mean if pd.notna(category_mean) else global_mean  
  return main_df
```
---
Now it's your turn! Given the conversation history and supporting details, please write succinct code to impute the missing values in the target column.
Remember, you should return the dataframe without any changes if the target is best filled through a manual mapping rather than a function.

## Real Scenario: {target_column}
_Conversation History_
{history}

_Sample Rows_
{data_preview}

_Previous Thought_
{thought}

_Output_
"""

pick_source_col_prompt = """Our goal is to impute the missing values in the target column by creating a mapping from the source column.
However, there are multiple source columns to choose from, so we need to determine which one is the most appropriate.
Specifically, the target column is '{target_column}', and the candidate source columns are: {source_columns}.
Your task is to decide which single source column is the most useful for creating the mapping.
Usually, this is the one with the most content, but you should review the sample rows to make an informed decision.
Your entire response should be a single string representing the name of the source column, with no further explanations before or after the output.

To provide context, here is a preview of rows randomly sampled from the table:
{data_preview}

Additionally, here is the recent conversation history so you can see what the user requested:
{history}

Now, which source column should we use to create the mapping?
"""

impute_mapping_prompt = """Your task is to create a mapping to help impute the missing values in the target column.
To aid you in this task, you will be provided with:
  * conversation history to provide context
  * names of the source and target columns
  * random samples of current source-to-target pairs to see the shape of actual mappings
  * the unique row values from the source column that need to be mapped

Occassionally, it may be unclear how to map the source values to the target values.
1. If the confusion is because there are multiple plausible target values, then you should:
  * separate the target values with a forward slash '/' to indicate that any of the values are acceptable
  * only include up to the top 3 most likely values to keep the mapping simple
  * if there are more than 3 values, then use an ellipsis '...' as the final value to indicate there are more possible values

2. If there isn't enough information to form a definitive mapping, but you are highly confident in your prediction, then you should:
  * state your best guess, and mark it as a guess by surrounding the value with square brackets '[]'
  * you are a highly intelligent analyst, so most of your guesses will be correct, and do not need to be surrounded with brackets

3. Finally, if the there isn't enough information to form any kind of mapping due to high ambiguity, then you should:
  * check the conversation history to see if the user has provided any hints on how to deal with unresolved values
  * if not, then review the target sample values and determine if there is already a default value that can be used
  * if the samples don't provide a signal or the source value itself is a default or missing value, then set the target to '<N/A>', which represents a true null

Please start by considering the conversation history and the format of existing values to determine what target values should look like.
Pay attention to details such as capitalization and punctuation (when dealing with text), or range and precision (when dealing with numbers).
Your entire response should only contain well-formatted JSON including your thought (string) and mapping (dict), with no additional text or explanations.

For example,
---
_Conversation History_
User: What are the top cities with the highest number of signups?
Agent: The top cities are New York, Chicago, and Lafayette. See table for more.
User: Many of the states are still empty for some reason, can you fix that for me?

_Column Names_
Source column(s): City
Target column(s): State

_Sample Pairs_
Los Angeles --> CA
San Jose --> CA
Dover --> DE
Jefferson City --> MO
Atlanta --> GA

_Unique Row Values_
Tallahasee
SaintPaul
Albequerque
Portland
Montgomury
Bostin

_Output_
```json
{{
  "thought": "The target values are US states, while the source values are cities. Many of the cities include typos, which is likely what caused the missing values."
  "mapping": {{
    "Tallahasee": "FL",
    "SaintPaul": "MN",
    "Albequerque": "NM",
    "Portland": "OR/ME",
    "Montgomury": "AL"
    "Bostin": "MA"
  }}
}}
```

_Conversation History_
User: What's the distribution of sizes for all the TVs?
Agent: Most of the TVs are 55 inches, see the chart for more.
User: Please fill the missing sizes for me. If something is unclear, then set it to 60.

_Column Names_
Source column(s): Product Name, Product Type, Manufacturer
Target column(s): Size (inches)

_Sample Pairs_
Samsung QN90C Neo QLED 4K Smart TV (2023 Model) | QLED TV | Samsung --> 65
LG C4 OLED evo 4K Smart TV with ThinQ AI and webOS 24 | OLED TV | LG --> 55
Sony BRAVIA XR A95L OLED 4K Ultra TV with Dolby Vision, Giant 75\" | OLED TV | Sony --> 75
TCL 6-Series Mini-LED QLED 4K UHD Smart Google TV with Dolby Atmos | LED TV | TCL --> 60
VIZIO M-Series Quantum X 4K HDR Smart TV with Voice Remote and Dolby Vision | LED TV | Vizio --> 50
Hisense U8N Mini-LED ULED 4K Premium 66\" Smart TV with Quantum Dot | ULED TV | Hisense --> 66
Sony HT-A7000 7.1.2ch Dolby Atmos Soundbar with Built-in Subwoofers | Soundbar | Sony --> 48
LG S95TR 9.1.5 Channel High-Res Audio Soundbar with Wireless Subwoofer and Rear Speakers | Soundbar | LG --> 47

_Unique Row Values_
Sony XR-X90L Full Array LED 4K Ultra HD Smart Google TV with XR Cognitive Processor | LED TV | Sony
LG G4 Gallery Edition OLED evo 4K Smart TV with Wall Mount Design | OLED TV | LG
Samsung The Frame QLED 4K Smart TV (2024) with Customizable Bezel and Art Mode | QLED TV | Samsung
TCL QM8 QLED 4K UHD Smart Mini LED TV with Google TV and 120Hz Panel | QLED TV | TCL
Hisense U6N ULED 4K Quantum Dot Smart TV with Full Array Local Dimming | ULED TV | Hisense
VIZIO P-Series Quantum X 4K UHD LED HDR Smart TV with AMD FreeSync Premium | LED TV | Vizio
LG S80TR 3.1.3 Channel Sound Bar with Dolby Atmos and DTS:X | Soundbar | LG
VIZIO M-Series All-in-One 2.1 Channel Immersive Soundbar with DTS Virtual:X | Soundbar | Vizio

_Output_
```json
{{
  "thought": "The target values are sizes ranging from 35 to to 75 inches. I will use the product name and type to determine the size when possible, but I will have to guess the size for most of the products. I will default to 60 inches since the user mentioned that as a fallback.",
  "mapping": {{
    "Sony XR-X90L Full Array LED 4K Ultra HD Smart Google TV with XR Cognitive Processor | LED TV | Sony": "[60]",
    "LG G4 Gallery Edition OLED evo 4K Smart TV with Wall Mount Design (65 inches) | OLED TV | LG": "65",
    "Samsung Frame 55\" 4K Smart TV (2024) with Customizable Bezel and Art Mode | QLED TV | Samsung": "55",
    "TCL QM8 QLED 4K UHD Smart Mini LED TV with Google TV and 120Hz Panel | QLED TV | TCL": "[60]",
    "Hisense U6N ULED 4K Quantum Dot Smart TV with Full Array Local Dimming | ULED TV | Hisense": "[60]",
    "VIZIO P-Series Quantum X 4K UHD LED HDR Smart TV with AMD FreeSync Premium | LED TV | Vizio": "[60]",
    "LG S80TR 3.1.3 Channel Sound Bar with Dolby Atmos and DTS:X | Soundbar | LG": "[35]",
    "VIZIO M-Series All-in-One 2.1 Channel Immersive Soundbar with DTS Virtual:X | Soundbar | Vizio": "[35]"
  }}
}}
```

_Conversation History_
User: What is the percentage of men vs. women?
Agent: Men bought 14.6% of bicycles, while women bought 23.4% of bicycles.
User: Wait, what about everyone else? Oh, I see. A bunch of nulls.

_Column Names_
Source column(s): FirstName, LastName
Target column(s): Gender

_Sample Pairs_
Henry | Kim --> Male
Emily | Rodriguez --> Female
Aisha | Patel --> Female
Dr. Taylor | Scott --> Male
Jordan | Casey --> Male
James | Wilson Sr. --> Male
Peter | Chen --> Male

_Unique Row Values_
Nguyen | Olivia
Quinn | Parker
Jocelyn | Reyes
Diego | Lopez
null | Smith
Hassan | Ahmed
Morales | Sofia
Morgan | Bailey

_Output_
```json
{{
  "thought": "Some of the first and last names seem to be reversed, which is likely the cause of confusion. Based on the sample pairs, I know my choices are either male or female.",
  "mapping": {{
    "Nguyen | Olivia": "Female",
    "Quinn | Parker": "Male/Female",
    "Jocelyn | Reyes": "Female",
    "Diego | Lopez": "Male",
    "null | Smith": "<N/A>",
    "Hassan | Ahmed": "Male",
    "Morales | Sofia": "Female",
    "Morgan | Bailey": "Male/Female"
  }}
}}
```

_Conversation History_
User: it's the month column that's causing problems
Agent: OK, I see that many of the values are missing, should I try to fill them in?
User: Yea, go for it

_Column Names_
Source column(s): created_date
Target column(s): month

_Sample Pairs_
04/13/2024 --> April
05/15/2024 --> May
04/25/2024 --> April
03/30/2024 --> March
05/08/2024 --> May
04/10/2024 --> April
06/12/2024 --> June

_Unique Row Values_
4/18/2024
5/12/2024
Apr
5/8/2024
A
March
3/15/2024
4/27/2024

_Output_
```json
{{
  "thought": "Target values are months from March to June, so the letter 'A' is probably April rather than August. The likely the cause of confusion likely stems from the fact that many of the source dates are in the wrong format or accidentally written as text.",
  "mapping": {{
    "4/18/2024": "April",
    "5/12/2024": "May",
    "Apr": "April",
    "5/8/2024": "May",
    "A": "[April]",
    "March": "March",
    "3/15/2024": "March",
    "4/27/2024": "April"
  }}
}}
```

_Conversation History_
User: Can you figure out the missing counties for me?

_Column Names_
Source column(s): zip_code
Target column(s): county

_Sample Pairs_
94172 --> San Francisco
90280 --> Los Angeles
94017 --> San Mateo
94121 --> San Francisco
94118 --> San Francisco
94083 --> San Mateo
95103 --> Santa Clara
94130 --> San Francisco

_Unique Row Values_
94088
91331
unknown
92336
95002
94402
91710
92504

_Output_
```json
{{
  "thought": "The target values are county names in California, while the source values are zip codes. The 'unknown' value is a true null, while the rest can be mapped to a county.",
  "mapping": {{
    "94088": "Santa Clara",
    "91331": "Los Angeles",
    "unknown": "<N/A>",
    "92336": "San Bernardino",
    "95002": "Santa Clara",
    "94402": "San Mateo",
    "91710": "San Bernardino",
    "92504": "Riverside"
  }}
}}
```

_Conversation History_
User: I'm not sure, can you help me figure out the missing industries for me?

_Column Names_
Source column(s): Company Name
Target column(s): Industry

_Sample Pairs_
Metro Transit Solutions --> Transportation and Warehousing
Digital Nexus Technologies --> Information
Meridian Capital Partners --> Finance and Insurance
Precision Analytics Consulting --> Professional, Scientific, and Technical Services
Horizon Realty Group --> Real Estate and Rental and Leasing
Swift Logistics Group --> Transportation and Warehousing
Brightpath Learning Institute --> Educational Services
Excel Academic Foundation --> Educational Services

_Unique Row Values_
Summit Property Management
Wellness Alliance Medical
Spectrum Data Systems
Centrix Holdings Corporation
Guardian Trust Financial
Mosaic Creative Studios
Vertex Research Associates
Landmark Entertainment Group

_Output_
```json
{{
  "thought": "The target values are NAICS industry codes so I will use the standard naming convention. While some of the company names clearly connect to the industry, others I would have to guess.",
  "mapping": {{
    "Summit Property Management": "Real Estate and Rental and Leasing",
    "Wellness Alliance Medical": "Health Care and Social Assistance",
    "Spectrum Data Systems": "Information",
    "Mosaic Creative Studios": "[Arts, Entertainment, and Recreation]",
    "Centrix Holdings Corporation": "Management of Companies and Enterprises",
    "Guardian Trust Financial": "Finance and Insurance",
    "Vertex Research Associates": "[Professional, Scientific, and Technical Services]",
    "Landmark Entertainment Group": "Arts, Entertainment, and Recreation"
  }}
}}
```
---
Now it's your turn! Given the conversation history and supporting details, please create a mapping to help impute the missing values in the target column.

_Conversation History_
{history}

_Column Names_
Source column(s): {source_cols}
Target column(s): {target_cols}

_Sample Pairs_
{sample_pairs}

_Unique Row Values_
{unique_values}

_Output_
"""

impute_routing_prompt = """Given the conversation history, your task is to determine the scope of the cleaning presented in the user's request.
For our purposes, the valid scopes are:
  * update: changing existing cells, such as trimming whitespace, or find and replace operations.
  * impute: filling in missing values based on some pattern or formula, including interpolation
  * pattern: flash filling a column based on some pattern where each row is dependent on the previous row above it
  * insert: adding a staging column as an intermediate cleaning step before reaching the final target column
  * validate: ensuring every row in the column belongs to some predefined set of values (such as accounts being active/inactive) or fall within some predefined range
  * resolve: a cleaning request that involves multiple steps, including any combination of datatype conversion, formatting, deletion, or the above operations

Concretely, suppose we want a clean the 'source' column for the different names of marketing channels that we have been tracking:
  * if we want to split the 'utm_source' column into 'source' and 'medium', then we are performing an insertion.
  * if the 'source' column already exists, and we are changing specific values (eg. 'Google Ads' to 'Google'), then we are performing an update.
  * if our goal is to fill in the blanks in the 'source' column, then we are performing an imputation.
  * if the 'source' has a value of 'google,google,google,bing,yahoo,bing,bing ...', and we want to break this into N rows, then we are following a pattern.
  * if we want to ensure all the values in the 'source' column are belong to the set of ['Google', 'Meta', 'Twitter', or 'LinkedIn'], then we are performing a validation.
    - for example, perhaps other values are 'google', 'Google Search', or 'Google-Ads', and we want to map all of those to 'Google'
    - another example is if we have alternate names like 'Facebook' and 'FB', and we want to map both of those to 'Meta'
  * if we want to perform any other type of cleaning, or if any combination of the above, then we are resolving a complex cleaning task.

{warning_msg}
Please start by considering what exactly the user is trying to accomplish, then decide on the appropriate scope.
Your entire response should be in well-formatted JSON including your thought (string) and the chosen scope (token), with no further explanations after the JSON output.

For example,
---
_Conversation History_
User: Can you help me fill in the missing values in the 'Customer Lifetime Value' column?
Agent: Certainly! How would you like to approach this? We could use an average, median, or perhaps a more sophisticated method.
User: Let's use the average of the existing values.

_Output_
```json
{{
  "thought": "The user wants to fill in missing values in the 'Customer Lifetime Value' column using the average of existing values. This is a clear case of imputation.",
  "scope": "impute"
}}
```

_Conversation History_
User: I need to clean up the 'Product Category' column.
Agent: Sure, what kind of cleaning do you need for the 'Product Category' column?
User: I want to change all instances of 'Electronics' to 'Electrical' and 'Clothing' to 'Apparel'.

_Output_
```json
{{
  "thought": "Standardizing values in the 'Product Category' column is akin to a find and replace operation, which involves updating existing values.",
  "scope": "update"
}}
```

_Conversation History_
User: How long does it take for a customer to open an email after it's sent?
Agent: The average time to open is around 3.2 days. However, there are many empty rows in the 'Send Date' column which may affect the results.
User: Let's fill in the blanks for Send Date by dragging down the first non-empty value.

_Output_
```json
{{
  "thought": "We are filling each blank cell by copying the date from the row above it, which means we are following a pattern.",
  "scope": "pattern"
}}
```

_Conversation History_
User: Many of the leads include personal emails, rather than work emails. Can you help me filter them out?
Agent: Certainly, should I just delete the rows containing personal emails?
User: No, create a new boolean column called 'IsPersonal', which flag the email type. Then we can use that when we query the data later.

_Output_
```json
{{
  "thought": "The user wants to create a staging a new column that flags whether an email is personal or not, which means inserting a new column.",
  "scope": "insert"
}}
```

_Conversation History_
User: The contact info are a mess. Most of it is emails, some are phone numbers, and a lot of them are just plain empty.
Agent: I see. How would you like to approach cleaning this column?
User: We need to keep only the emails, and move the phone numbers to a new column, then figure out a way to grab the missing ones.

_Output_
```json
{{
  "thought": "The user want to format emails, insert new columns, and impute missing values. This is clearly a complex cleaning task that requires multiple steps.",
  "scope": "resolve"
}}
```

_Conversation History_
User: We need to make sure all the values in the 'Customer Status' column are clean.
Agent: Certainly! How did you want to handle the values?
User: They should only be 'Active', 'Inactive', or 'Pending'. Anything else needs to be corrected.

_Output_
```json
{{
  "thought": "The user wants to ensure all values belong to a predefined set of valid statuses, so this is data validation.",
  "scope": "validate"
}}
```
---
Now it's your turn! Please decide on the scope of the cleaning task based on the conversation history.
For additional context, here is a preview of the data:
{data_preview}

_Conversation History_
{history}

_Output_
"""

undo_prompt = """Given the conversation history, we need to determine whether the user wants to undo the previous action.
If the user wants to undo, then output 'yes'. Otherwise, output 'no'.

_Conversation History_
{history}

_Output_
"""

persist_prompt = """Given the conversation history, we need to determine whether the user wants to save the results to disk.
If the user wants to save, then output 'yes'. Otherwise, output 'no'.

_Conversation History_
{history}

_Output_
"""