clarify_metric_prompt = """In accordance with the conversation history, we want to calculate the {expanded} metric.
However, there is ambiguity that needs to be resolved surrounding which columns should be used to represent the variables within the metric.
For more context, the formula is constructed as a tree of variables, starting with the root node which is the highest level Expression representing the metric.
The internal variables mix and match other Expressions until we reach the leaf nodes, which are constructed as Clause variables that reference specific columns in a table.

Expressions are composed of a name, variables, and relation, which conveys the relationship between those variables.
Valid relations include: add (+), subtract (-), multiply (*), divide (/), exponent (^), and (&), or (|), less_than (<), greater_than (>), equals (=), conditional (?), and placeholder.
The 'placeholder' relation is of particular interest since it indicates that we are unsure about how to structure the expression, and therefore serves as a great candidate for clarification.
Each Clause is composed of a name, aggregation (agg), table (tab), row, and column (col). They represent the data that is grounded to specific columns in a table.
Valid aggregations include: sum, count, average, top, bottom, min, max, greater_than, less_than, not, equals, empty, filled, constant, all.
For the aggregations of [top, bottom, greater_than, less_than, not, equals, constant], the 'row' field is used to specify the value of N.
The difference between top/bottom versus min/max is that top/bottom can be used to specify the number of values to return, while min/max only returns a single value.
Whereas not/equals are used to filter the column by a specific value, the empty/filled relations are used to filter for null or non-null values, respectively.
Both Expressions and Clauses contain a 'verified' field, which indicates whether the variable has been confirmed by the user. If a variable has already been verified, we don't need to ask about it again.

The different levels of ambiguity are:
  * general - high uncertainty about the user's intent; This is unlikely since we know the goal is to calculate a metric. We include this option only for completeness.
  * partial - we are unsure about which columns to use; Most often occurs when there are multiple options for a given column, and we need to ask the user which one to use.
  * specific - we are missing a specific piece of information such as an aggregation function, filter condition, or relationship between variables.
  * confirmation - we have a fairly complete candidate formula, but want to confirm that the structure and its details are correct.

To help with this task, you are also given:
  * Metric: the full name of the metric and short version
  * Thought: previous thought process, providing context for potential questions to ask
  * Formula: our current understanding of the formula, including the variables and their potential columns;
    When a variable is marked as verified, we already know the columns to use. Instead, focus your attention on variables that are unverified.

Start by constructing a concise thought concerning what information is still missing that prevents us from querying for the target metric.
Then, choose the level of ambiguity and generate a clarification question that will help us determine the correct columns to use and which variables they belong to.
To avoid overwhelming the user, your question response should be no longer than three (3) sentences at most.
Your entire response should be in well-formatted JSON including keys for thought (string), level (token), and question (string), with no further explanations after the JSON output.

For example,
---
_Conversation History_
User: Let's expand further. I want to look at the whole past year.
Agent: Certainly! To calculate the ROI for last year, we'll need to look at our revenue and costs. Which specific revenue streams and cost categories would you like me to consider?
User: The sum of expenses should cover the cost categories.

_Supporting Details_
  * Metric: Return on Investment (ROI)
  * Thought: There is no direct column for ROI, so we will need to calculate it ourselves. The cost variable is verified as the sum of expenses, so we no longer need to worry about it.
With that said, many reasonable options exist for 'revenue', including license_fee, service_charge, or maintenance_income.
We also need to filter for last year, which can be calculated using interact_date or visit_ts, so we might want to double check that as well.
  * Formula:
  {{
    "name": "Return on Investment", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "ROI", "verified": false, "relation": "divide",
        "variables": [
          {{
            "name": "total_revenue", "verified": false, "relation": "add",
            "variables": [
              {{"name": "license_revenue", "ver": false, "agg": "sum", "tab": "stripe_download", "col": "license_fee"}},
              {{"name": "service_revenue", "ver": false, "agg": "sum", "tab": "bookings", "col": "service_charge"}},
              {{"name": "income_revenue", "ver": false, "agg": "sum", "tab": "stripe_download", "col": "maintenance_income"}}
            ]
          }}, {{
            "name": "costs", "ver": true, "agg": "sum", "tab": "stripe_download", "col": "expenses"
          }}
        ]
      }}, {{
        "name": "past_year", "ver": false, "agg": "greater_than", "tab": "stripe_download", "col": "interact_date", "row": "12 months ago"
      }}
    ]
  }}

_Output_
```json
{{
  "thought": "There are multiple options for revenue and timing, so we need to clarify which ones to use.",
  "level": "confirmation",
  "question": "Is license fee, service charge, or maintenance income most appropriate to calculate revenue? Also, should I use interact_date or visit_ts to determine the past year?"
}}
```

_Conversation History_
User: What's the average time to close a deal for each stage?
Agent: The average time to close a deal for each stage is 39 days for 'New', 15 days for 'Qualified', and 11 days for 'Negotiation'.
User: So what does the final score look like if we count negotation stage as promoters?

_Supporting Details_
  * Metric: Net Promoter Score (NPS)
  * Thought: After removing the null values in the final_scores column, we can use the rest to determine the number of promoters and detractors.
The number of 'promoters' is the count of scores that are 9 or 10, while the number of 'detractors' is the count of scores that are 0 to 6.
We also need to avoid double counting, so we should use the DISTINCT keyword applied to either the lead_id or the survey_id.
  * Formula:
  {{
    "name": "Net Promoter Score", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "NPS", "verified": false, "relation": "subtract",
        "variables": [
          {{ "name": "promoters", "verified": false, "relation": "placeholder", variables: [] }},
          {{ "name": "detractors", "verified": false, "relation": "placeholder", variables: [] }}
        ]
      }}, {{
        "name": "unique_leads", "verified": false, "agg": "count", "tab": "pardot_report", "col": "lead_id"
      }}
    ]
  }}

_Output_
```json
{{
  "thought": "The user wants to rely on the stages to count promoters and detractors, but it is unclear how to do so.",
  "level": "specific",
  "question": "How does the Negotiation stage count towards a promoter score? I am using a formula where promoters are survey scores of 9 or 10, should we be doing something different?"
}}
```

_Conversation History_
User: I'm not sure, what options do we have for active customers?
Agent: Active users are often determined by the last time they logged in or when they last took some action, such as making a purchase or registering for an event.
User: Event registration and email signups are the activities we care most about. Let's focus on just last quarter like earlier.

_Supporting Details_
  * Metric: Retention Rate (Retain)
  * Thought: We should filter users who have been active in the last month, which is determined by their last_login date.
Filtering for the last month can be done by using the last_login column and the current date.
Additionally, we have already verified that we should use the count of unique user_ids to determine total customers.
We can use the registered_at column in the events table and the signup_date column in the users table to determine active customers.
  * Formula:
  {{
    "name": "Retention Rate", "verified": false, "relation": "divide",
    "variables": [
      {{
        "name": "active_customers", "verified": false, "relation": "and",
        "variables": [
          {{
            "name": "has_activity", "verified": false, "relation": "or",
            "variables": [
              {{"name": "event_registration", "ver": false, "agg": "filled", "tab": "events", "col": "registered_at"}},
              {{"name": "email_signup", "ver": false, "agg": "filled", "tab": "salesforce_June2021", "col": "signup_date"}}
            ] 
          }},
          {{
            "name": "last_quarter", "ver": true, "agg": "greater_than", "tab": "events", "col": "last_login", "row": "3 months ago"
          }}
        ]
      }}, {{
        "name": "total_customers", "ver": true, "agg": "count", "tab": "salesforce_June2021", "col": "user_id"
      }}
    ]
  }}

_Output_
```json
{{
  "thought": "The remaining uncertainty concerns how to determine active customers. We have a reasonable idea, but we should confirm with the user that having a non-null value in either the registered_at or signup_date columns is sufficient.",
  "level": "confirmation",
  "question": "Ok, I will grab just the last month of data. I am considering a customer as active when either the registered_at or signup_date columns for that user are not null. Is this correct?"
}}
```

_Conversation History_
User: Each unique id in the adobe table is an impression.
Agent: So the count of unique aa_uuids from the AdobeAnalyticsEndpoint table counts as impressions, which we can then filter for Instagram by using the ad_platform column. Is that right?
User: Yea, that's right
Agent: Great, and how should we be calculating clicks? Also, is there a time range you were thinking of, such as the last month?
User: Last month sounds fine

_Supporting Details_
  * Metric: Click-Thru Rate (CTR)
  * Thought: We should start by filtering for Instagram using the ad_platform column with AdobeAnalyticsEndpoint.
That table offers aa_uuid to determine impressions, but we need to make sure we only count unique impressions to avoid double counting.
There isn't a clicks column, but cost_per_click and ad_spend can be used to approximate the number of clicks, which we should review with the user.
We also have more details about the time range to consider, so we should look for the appropriate columns to use for that.
  * Formula: 
  {{
    "name": "Click-Through Rate", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "CTR", "verified": false, "relation": "divide",
        "variables": [
          {{
            "name": "clicks", "ver": false, "relation": "divide",
            "variables": [
              {{"name": "cost", "ver": false, "agg": "all", "tab": "AdobeAnalyticsEndpoint", "col": "ad_spend"}},
              {{"name": "cost_per_click", "ver": false, "agg": "all", "tab": "AdobeAnalyticsEndpoint", "col": "cost_per_click"}}
            ]
          }}, {{
            "name": "impressions", "ver": true, "agg": "count", "tab": "AdobeAnalyticsEndpoint", "col": "aa_uuid"
          }}
        ]
      }}, {{
        "name": "Instagram_filter", "ver": true, "agg": "equals", "tab": "AdobeAnalyticsEndpoint", "col": "ad_platform", "row": "Instagram"
      }}, {{
        "name": "last_month", "ver": false, "agg": "greater_than", "tab": "Segment_scrubbed", "col": "activityTimestamp", "row": "30 days ago"
      }}
    ]
  }}

_Output_
```json
{{
  "thought": "There is ambiguity around which columns are used to calculate clicks, and also which columns to use to filter for the time range.",
  "level": "partial",
  "question": "I'm not seeing an obvious clicks column, perhaps I could divide ad_spend by cost_per_click to calculate clicks? Also, can you suggest the column to filter for the last month? I'm thinking of using activityTimestamp."
}}
```

_Conversation History_
User: What's the CPM if we only consider US visitors?
Agent: I'll use TotalPaid for spend and PlatformViews for impressions like earlier, but I don't see anything to filter for US visitors.
User: The WebAnalytics table has a IPAddress column, which we can use to filter for US visitors.

_Supporting Details_
  * Metric: Cost Per Mille (CPM)
  * Thought: We have verified the total ad spend, but still need to filter for US visitors based on the IPAddress column.
While there is an IPAddress column in the WebAnalytics table, it's unclear how to join it with the GoogleReportJune table.
More critically, the IPAddress data contains numbers rather than country names, so it's unclear how to use it.
  * Formula: 
  {{
    "name": "Cost Per Mille", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "CPM", "verified": true, "relation": "divide",
        "variables": [
          {{"name": "ad_spend", "ver": true, "agg": "sum", "tab": "GoogleReportJune", "col": "TotalPaid"}},
          {{"name": "impressions", "ver": true, "agg": "sum", "tab": "GoogleReportJune", "col": "PlatformViews"}}
        ]
      }}, {{
        "name": "US_visitors", "ver": false, "agg": "equals", "tab": "WebAnalytics", "col": "IPAddress", "row": "US"
      }}
    ]
  }}

_Output_
```json
{{
  "thought": "The main variables are verified, but the specific filtering mechanism based on IPAddress is unclear.",
  "level": "specific",
  "question": "Thanks for that information, I'm thinking of joining WebAnalytics to GoogleReportJune based on VendorID. I'm not sure how to identify US visitors based on the IPAddress column though, can you help?"
}}
```

_Conversation History_
User: You can use the entryURL column to filter for users coming from LinkedIn.
Agent: Got it. To calculate conversions, I will use the userID column to join with the Purchases table and then sum the number of purchases. Does that sound right?
User: Conversions are rows where the userActivity is 'transaction_complete', there is no need to join with the Purchases table.

_Supporting Details_
  * Metric: Conversion Rate (CVR)
  * Thought: We should start by filtering for users coming from LinkedIn by using the entryURL column and filter for the 'impression' value to determine visits.
We also need to filter for the past week, which can come from the saleDate or the activityTimestamp column.
Conversions should be calculated by the 'transaction_complete' value in userActivity, which does not require joining with another table.
When writing the query, we need to make sure that we only count conversions that are a subset of the visits rather than repeating the same filters.
  * Formula: 
  {{
    "name": "Conversion Rate", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "CVR", "verified": true, "relation": "divide",
        "variables": [
          {{"name": "conversions", "ver": true, "agg": "equals", "tab": "DatadogLogs", "col": "userActivity", "row": "transaction_complete"}},
          {{"name": "visits", "ver": true, "agg": "equals", "tab": "DatadogLogs", "col": "userActivity", "row": "impression"}}
        ]
      }}, {{
        "name": "from_LinkedIn", "ver": false, "agg": "equals", "tab": "DatadogLogs", "col": "entryURL", "row": "linkedin"
      }}, {{
        "name": "past_week", "ver": false, "agg": "greater_than", "tab": "DatadogLogs", "col": "activityTimestamp", "row": "7 days ago"
      }}
    ]
  }}

_Output_
```json
{{
  "thought": "There are two viable options for calculating the time range. The activityTimestamp column is more likely than saleDate since it is already part of DatadogLogs, but we should confirm.",
  "level": "confirmation",
  "question": "Got it, I will use impressions and transaction_complete to calculate visits and conversions. I'm planning to use activityTimestamp to filter for the past week, is that correct?"
}}
```

_Conversation History_
User: Well, what I actually want is to compare two things. First is visitors from email vs. visitors from paid media. Second, I want to see the email stats on a month over month basis.
Agent: Sure, I can use 'email' to filter for email visitors and 'google' to filter for paid media visitors, does that sound right?
User: You should include Facebook, Instagram, and LinkedIn as well as part of paid.

_Supporting Details_
  * Metric: Visitor Source Comparison (VSC)
  * Thought: We should filter for visits from email, which can be calculated using the has_viewed column where the incoming_channel_tag is email.
There doesn't seem to be a column in the userSource table to measure when something occured though, so we might need to calculate it by joining with something else.
We also need to perform a segmentation the visitor growth on a monthly basis to figure out how traffic has been growing, but we can defer that for now.
  * Formula:
  {{
    "name": "Visitor Source Comparison", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "VSC", "verified": false, "relation": "divide",
        "variables": [
          {{
            "name": "email_visits", "verified": false, "relation": "and",
            "variables": [
              {{"name": "has_viewed", "ver": false, "agg": "equals", "tab": "userSource", "col": "has_viewed", "row": true}},
              {{"name": "incoming_channel", "ver": false, "agg": "equals", "tab": "userSource", "col": "incoming_channel_tag", "row": "email"}}
            ]
          }}, {{
            "name": "paid_media_visits", "verified": false, "relation": "or",
            "variables": [
              {{"name": "google_visits", "ver": false, "agg": "equals", "tab": "userSource", "col": "incoming_channel_tag", "row": "google"}},
              {{"name": "facebook_visits", "ver": false, "agg": "equals", "tab": "userSource", "col": "incoming_channel_tag", "row": "facebook"}},
              {{"name": "instagram_visits", "ver": false, "agg": "equals", "tab": "userSource", "col": "incoming_channel_tag", "row": "instagram"}},
              {{"name": "linkedin_visits", "ver": false, "agg": "equals", "tab": "userSource", "col": "incoming_channel_tag", "row": "linkedin"}}
            ]
          }}
        ]
      }}, {{
        "name": "monthly_basis", "ver": false, "agg": "greater_than", "tab": "userSource", "col": "unsure", "row": "1 month ago"
      }}
    ]
  }}

_Output_
```json
{{
  "thought": "Visitor Source Comparison seems to be an aggregation of visits from email and paid media, rather than a business metric. Also, there is ambiguity around the appropriate columns for grouping by month.",
  "level": "partial",
  "question": "Are you trying to calculate a custom Vistor Source metric, or are you just trying to compare the two? Calculating a metric entails creating a new formula, whereas a basic comparison is simpler.
              In either case, I don't see a column in the userSource table that covers timing. Which column should be used to infer when someone visited?"
}}
```
---
Now it's your turn! Please generate a concise thought followed by the ambiguity level and an insightful question to clarify the key points of uncertainty.

_Conversation History_
{history}

_Supporting Details_
  * Metric: {metric}
  * Thought: {thought}
  * Formula: {formula}

_Output_
"""

query_to_visual_prompt = """Based on the conversation history, we have executed a query on the database for some data.
Given a preview of the data, please determine if it is appropriate to visualize the results.
Please start by thinking about the context of the conversation and whether the query output can be visualized.
Then respond in well-formatted JSON including keys for thought (string) and convert (boolean), with no further explanations after the JSON output.

For example,
---
_Conversation History_
User: How many visitors did we get in the past month coming from email?
Agent: Email visitors this month comes to 121,975
User: I meant broken down by day.

_Preview_
| date       | page_views |
|:-----------|:-----------|
| 2024-09-14 | 10,229     |
| 2024-09-15 | 8,756      |
| 2024-09-16 | 5,811      |
| 2024-09-17 | 1,908      |
| 2024-09-18 | 4,020      |
| 2024-09-19 | 2,876      |
| 2024-09-20 | 3,732      |
| 2024-09-21 | 5,993      |

_Output_
```json
{{
  "thought": "The date can serve as the x-axis and page_views as the y-axis.",
  "convert": true
}}
```

_Conversation History_
User: What's the latest release candidate that is considered stable?
Agent: That would be 72.6.1
User: Are we still getting a lot of downloads for it?

_Preview_
| firmware_version | downloads |
|------------------|-----------|
| 72.6.1           | 3146      |

_Output_
```json
{{
  "thought": "Since there is only one row of data, visualizing as a graph would not add much value.",
  "convert": false
}}
```

_Conversation History_
User: I want to flag high-value customers who have not logged in for a long time.
Agent: How do you define high-value customers?
User: Customers who have spent more than $1,000 with us.

_Preview_
| customer_id   | days_since_last_login | total_purchase_value |
|---------------|-----------------------|----------------------|
| 192837465728  | 29                    | $15,780              |
| 129384756473  | 8                     | $12,875              |
| 928374561029  | 25                    | $7,489               |
| 847362910293  | 52                    | $4,199               |
| 463728192837  | 2                     | $2,450               |
| 384756291038  | 31                    | $1,917               |
| 582947102938  | 15                    | $1,450               |
| 756382910274  | 7                     | $1,250               |

_Output_
```json
{{
  "thought": "Comparing login frequency to purchase value does not tell us anything interesting. Moreover, the scale of the values makes it difficult to display the data in a single graph.",
  "convert": false
}}
```

_Conversation History_
User: So how much did she generate?
Agent: Maria generated $1,181,700 in invoices last month.
User: Who are the top sales reps across all dealerships?

_Preview_
| dealership       | sales_rep       |
|------------------|-----------------|
| AutoMax Downtown | Maria Rodriguez |
| CarWorld East    | Yuki Tanaka     |
| Honda DriveTime  | Diego Lopez     |
| Elite Motors     | Hassan Ahmed    |
| Luxury Wheels    | Aisha Patel     |
| BMW of San Jose  | James Wilson    |
| Sunset Motors    | Michael Nguyen  |
| Toyota of LA     | Sophia Chu      |

_Output_
```json
{{
  "thought": "There are no numeric values to plot, so a visualization is not appropriate.",
  "convert": false
}}
```

_Conversation History_
User: Yes, subscriptions is what we're looking for.
Agent: Is there any specific time range you want to consider?
User: No, let's look at all the results

_Preview_
| variant               | conversion_rate |
|-----------------------|-----------------|
| No Change (control)   | 2.32%           |
| Dark Mode Support     | 2.86%           |
| Team Collaboration    | 2.26%           |
| Dark Mode + Collab    | 3.39%           |

_Output_
```json
{{
  "thought": "The variants can serve as the x-axis and conversion_rate as the y-axis",
  "convert": true
}}
```
---
Now it's your turn to decide whether the query results can be visualized.

_Conversation History_
{history}

_Preview_
{preview}

_Output_
"""

# There is also an optional description field that provides a brief explanation of the metric. This is typically present only at the root node.
exemplar_variables_prompt = """Given the conversation history, your task is to construct the formula for calculating the '{metric}' metric.
Formulas are constructed as a tree of Expression objects, where the root node is the final Expression representing the metric.
The internal nodes are called variables that combine or compare other Expressions, while the leaf nodes are Clauses that reference specific columns in a table.

Each Expression is composed of a name, variables, and relation, which conveys the relationship between those variables. The variables are either other Expressions or Clauses.
Valid relations include: add (+), subtract (-), multiply (*), divide (/), exponent (^), and (&), or (|), less_than (<), greater_than (>), equals (=), conditional (?).
Typically speaking, 'add', 'subtract', 'multiply', 'divide', 'exponent', and 'conditional' represent mathematical operations that return numeric values.
On the other hand, 'and', 'or', 'less_than', 'greater_than', and 'equals' represent logical operations that return boolean values.
In addition, 'conditional' is also unique as the only relation that requires three variables instead of two: `variable1 ? variable2 : variable3`.
There is one last relationship called 'placeholder', which should be used if and only if you are unsure about how to properly structure the expression at that level.

At a high level, examples of metrics may take the form:
  * ARPU = Formula('Average Revenue per User', relation='divide', variables=[revenue, customer])
  * CAC = Formula('Customer Acquisition Cost', relation='divide', variables=[cost, customer])
  * CPC = Formula('Cost per Click', relation='divide', variables=[ad_spend, click])
  * CTR = Formula('Click-Through Rate', relation='divide', variables=[click, impression])
  * LTV = Formula('Customer Lifetime Value', relation='divide', variables=[ARPU, Churn])
  * MAU = Formula('Monthly Active Users', relation='and', variables=[active_count, past_month])
  * Profit = Formula('Net Profit', relation='subtract', variables=[revenue, cost])
  * Revenue = Formula('Total Revenue', relation='multiply', variables=[price, quantity])
  * ROAS = Formula('Return on Ad Spend', relation='divide', variables=[revenue, ad_spend])
  * Views = Formula('Total Page Views', relation='add', variables=[google_visits, facebook_visits, instagram_visits])

Clauses are composed of a name, aggregation (agg), table (tab), row, and column (col). They represent the data that is grounded to specific columns in a table. Valid aggregations are:
  * sum - total of all non-null values
  * count - count the number of unique values
  * average - take the mean of all non-null values
  * top - the top N values, where N > 1. If N == 1, then use 'max' instead.
  * bottom - the bottom N values, where N > 1. If N == 1, then use 'min' instead.
  * min - minimum value in the column, equivalent to 'bottom 1'
  * max - maximum value in the column, equivalent to 'top 1'
  * equals - filter for rows where the column equals a specific value N
  * not - filter for rows where the column does not contain a specific value N
  * less_than - filter for rows where the column is less than a specific value N
  * greater_than - filter for rows where the column is greater than a specific value N
  * empty - simply decides whether the row is null
  * filled - opposite of empty, decides whether the row is not null
  * constant - a constant value of N that is not derived from the data
  * all - no aggregation, just keep all the raw values in the column

For the aggregations of [top, bottom, greater_than, less_than, not, equals, constant], the 'row' field is used to specify the value of N.
When choosing a name for the clause, pick something short and descriptive that contains no more than 2-3 parts, connected by underscores.
Rates (ie. CTR, CVR, etc.) are expected to range from 0 to 1, and should NOT be multiplied by 100 to convert into a percentage.
You do not need to worry about how tables are joined together. Your only job is to construct the formula as accurately as possible.


Please start by thinking carefully about what the user is trying to calculate, and focus on the tables and columns that are most relevant to the calculation.
If the metric is complex, try to break down the calculation into smaller steps and think about how each step can be represented as an expression.
If it is unclear how to structure the expression, use the 'placeholder' relation with an empty list of variables to indicate that you need some guidance from the user.
Your entire response should be in well-formatted JSON starting with keys for thought (string) and formula (dict).
The formula dict should be a tree of expression objects, expanded to include all child expressions and clauses. There should be no further text or explanations after the JSON output.

For example,
---
Suppose we have 3 tables with the following columns:
* TransactionId, CampaignId, AdGroupId, Keyword, TotalClicks, Impressions, MaxCPCBid, GoogleAdSpend, FacebookAdSpend, InstagramSpend, TransactionDate, AdPlacement, LandingPage in ppc_transactions
* AmpId, EmailAddress, CartId, ShippingMethod, HasDiscountApplied, RecurringPayment, OneTimePayment, PaymentMethod, TransactionStatus, Completed, CheckoutDate in checkout_amplitude
* outreach_id, email_header, email_title, email_body, recipient_address, image_link, hero_callout, send_time, open_time, click_time, reviewed in Outreach Revised

_Conversation History_
User: How about our email performance?
Agent: What specifically would you like to know?
User: Is the open rate increasing?
In this example, we are trying to calculate Email Open Rate (Open).

_Output_
```json
{{
  "thought": "Emails sent is best captured as the count of outreach ids where send time is not null. Emails opened follows the same logic, but with open time.",
  "formula": {{
    "name": "Email Open Rate",
    "relation": "divide",
    "variables": [
      {{
        "name": "emails_sent",
        "relation": "and",
        "variables": [
          {{"name": "outreach", "agg": "count", "tab": "Outreach Revised", "col": "outreach_id"}},
          {{"name": "was_sent", "agg": "filled", "tab": "Outreach Revised", "col": "send_time"}}
        ]
      }}, {{
        "name": "emails_opened",
        "relation": "and",
        "variables": [
          {{"name": "outreach", "agg": "count", "tab": "Outreach Revised", "col": "outreach_id"}},
          {{"name": "was_opened", "agg": "filled", "tab": "Outreach Revised", "col": "open_time"}}
        ]
      }}
    ]
  }}
}}
```

_Conversation History_
User: yes, go for it.
Agent: Great, I have joined the data from all the platforms together within the ppc_transactions table.
User: So what is the total return on our ad spend for those three channels?
In this example, we are trying to calculate Return on Ad Spend (ROAS).

_Output_
```json
{{
  "thought": "Total return should take both subscriptions and one-time purchases into account. The PPC table suggests we are advertising on Google, Facebook and Instagram, so we should sum up across all platform to get total spend.",
  "formula": {{
    "name": "Return on Ad Spend",
    "relation": "divide",
    "variables": [
      {{
        "name": "total return",
        "relation": "add",
        "variables": [
          {{"name": "subscriptions", "agg": "sum", "tab": "checkout_amplitude", "col": "RecurringPayment"}},
          {{"name": "one_time_purchases", "agg": "sum", "tab": "checkout_amplitude", "col": "OneTimePayment"}}
        ]
      }}, {{
        "name": "total spend",
        "relation": "add",
        "variables": [
          {{"name": "google", "agg": "sum", "tab": "ppc_transactions", "col": "GoogleAdSpend"}},
          {{"name": "facebook", "agg": "sum", "tab": "ppc_transactions", "col": "FacebookAdSpend"}},
          {{"name": "instagram", "agg": "sum", "tab": "ppc_transactions", "col": "InstagramSpend"}}
        ]
      }}
    ]
  }}
}}
```

For our second scenario, suppose we now work with the following columns:
* customer_id, vehicle_id, start_date, end_date, pickup_location, dropoff_location, status, final_expenses, generated_revenue, payment_status in Q4Bookings
* ticket_id, issue_date, issue_type, issue_description, product_name, assigned_agent, resolution_status, resolution_date, feedback_notes, followup_required in SupportLogs
* renewal_id, completion_status, account_name, transaction_id, is_paid, vendor_address, product_price, renewal_discount, renewal_revenue in MonthlyRenewals

_Conversation History_
User: Let's look at just the month of April.
Agent: The revenue in April was approximately $272,100
User: So what is our net profit at that time?
In this example, we are trying to calculate Net Profit (Profit).

_Output_
```json
{{
  "thought": "The only column related to costs is final_expenses. There are two columns related to revenue: generated_revenue and renewal_revenue. We will go with generated_revenue because it shares the same table as final_expenses. We also need to filter for April.",
  "formula": {{
    "name": "Net Profit",
    "relation": "and"
    "variables": [
      {{
        "name": "Profit",
        "relation": "subtract",
        "variables": [
          {{"name": "revenue", "agg": "sum", "tab": "Q4Bookings", "col": "generated_revenue"}},
          {{"name": "expenses", "agg": "sum", "tab": "Q4Bookings", "col": "final_expenses"}},
        ]
      }}, {{
        "name": "In_April", "agg": "equals", "tab": "Q4Bookings", "col": "start_date", "row": "April"
      }}
    ]
  }}
}}
```

_Conversation History_
User: Who are our high value accounts within California, Oregon, and Washington?
Agent: How do you define high value accounts?
User: Those with the highest monthly renewal rates.
In this example, we are calculating High Value Accounts (Whale).

_Output_
```json
{{
  "thought": "The user has specified the states on the west coast, so we can filter for those states and then sort by account size to get the biggest accounts. We can grab the account names for better display.",
  "formula": {{
    "name": "High Value Accounts",
    "relation": "and",
    "variables": [
      {{
        "name": "top_accounts",
        "relation": "and",
        "variables": [
          {{"name": "top_accounts", "agg": "top", "tab": "MonthlyRenewals", "col": "renewal_revenue", "row": "7"}},
          {{"name": "account_identifier", "agg": "all", "tab": "MonthlyRenewals", "col": "account_name"}}
        ]
      }}, {{
        "name": "west_coast", 
        "relation": "or",
        "variables": [
          {{"name": "from_CA", "agg": "equals", "tab": "Q4Bookings", "col": "pickup_location", "row": "California"}},
          {{"name": "from_OR", "agg": "equals", "tab": "Q4Bookings", "col": "pickup_location", "row": "Oregon"}},
          {{"name": "from WA", "agg": "equals", "tab": "Q4Bookings", "col": "pickup_location", "row": "Washington"}}
        ]
      }}
    ]
  }}
}}
```

Moving on to our third scenario, we have six possible tables to choose from:
* ActivityId, CampaignId, Channel, ActivityType, Spend, IsConversion, ReferrerURL, PredConversionValue, BBTimestamp, BBLink, TargetAudience, Notes in Trifecta_Activity
* EnrollmentID, CourseID, StudentID, EnrollmentDate, CompletionStatus, Feedback, CertificateLink, PaymentStatus, ReferralSource in Trifecta_Enrollment
* SurveyID, UserID, Rating, Feedback, SurveyDate, SurveyType, SurveyLink, SurveyStatus, SurveySource in Trifecta_Surveys

_Conversation History_
User: Do we have any data from the LinkedIn campaign?
Agent: Yes, we have some rows where the Channel is 'LinkedIn'
User: What is the click through rate for those ads?
In this example, we are calculating Click-Through Rate (CTR).

_Output_
```json
{{
  "thought": "Clicks and impressions are probably activity types, but it is not clear, so we'll set a placeholder for now. As for the LinkedIn filter, we can use the Channel column.",
  "formula": {{
    "name": "LinkedIn CTR",
    "relation": "and",
    "variables": [
      {{
        "name": "Click-Through Rate",
        "relation": "divide",
        "variables": [
          {{ "name": "clicks", "relation": "placeholder", "variables": [] }},
          {{ "name": "impressions", "relation": "placeholder", "variables": [] }}
        ]
      }}, {{
        "name": "from_LinkedIn", "agg": "equals", "tab": "Trifecta_Activity", "col": "Channel", "row": "LinkedIn"
      }}
    ]
  }}
}}
```

_Conversation History_
User: Can you take a look at the survey data?
Agent: Sure, what specifically would you like to know?
User: What's our NPS for the latest one?
In this example, we are calculating Net Promoter Score (NPS).

_Output_
```json
{{
  "thought": "Calculating NPS requires subtracting the detractors (score <= 6) from the promoters (score >= 9). We can use the Rating column to determine the score.",
  "formula": {{
    "name": "Net Promoter Score",
    "relation": "multiply",
    "variables": [
      {{
        "name": "Net Promoter Ratio",
        "relation": "divide",
        "variables": [
          {{
            "name": "Promoter Difference",
            "relation": "subtract",
            "variables": [
              {{
                "name": "Promoter Count",
                "relation": "or",
                "variables": [
                  {{ "name": "Nine Rating", "agg": "equals", "tab": "Trifecta_Surveys", "col": "Rating", "row": "9" }},
                  {{ "name": "Above Nine", "agg": "greater_than", "tab": "Trifecta_Surveys", "col": "Rating", "row": "9" }}
                ]
              }}, {{
                "name": "Detractor Count",
                "relation": "or",
                "variables": [
                  {{ "name": "Six Rating", "agg": "equals", "tab": "Trifecta_Surveys", "col": "Rating", "row": "6" }},
                  {{ "name": "Below Six", "agg": "less_than", "tab": "Trifecta_Surveys", "col": "Rating", "row": "6" }}
                ]
              }}
            ]
          }}, {{
            "name": "Total Responses", "agg": "count", "tab": "Trifecta_Surveys", "col": "SurveyID", "row": "all"
          }}
        ]
      }}, {{
        "name": "scaling_factor", "agg": "constant", "tab": "N/A", "col": "N/A", "row": "100"
      }}
    ]
  }}
}}
```

Next, this scenario has the following tables and columns:
* lead_id, first_name, last_name, email, organization, lead_source, contact_date, status, notes, follow_up_date in mq_leads
* subscription_id, user_id, plan_name, sub_timestamp, billing_cycle, payment_status, renewal_notice in subscriptions
* activity_id, user_id, activity_type, timestamp, duration, instructor, data_source, outcome, error_log in user_activity

_Conversation History_
User: What percentage of them are still sticking around after the first month?
Agent: How do you define sticking around?
User: Those who received a renewal notice, submitted their payment, and gotten it approved.
In this example, we are calculating Retention Rate (Retain).

_Output_
```json
{{
  "thought": "The user has specified the conditions for active users, so we can use the renewal notice, payment status, and sub_timestamp columns to filter for them. Total users can be calculated by counting the subscription_id or user_id.",
  "formula": {{
    "name": "Retention Rate",
    "relation": "divide",
    "variables": [
      {{
        "name": "Active Users",
        "relation": "and",
        "variables": [
          {{"name": "sub_timestamp", "agg": "greater_than", "tab": "subscriptions", "col": "sub_timestamp", "row": "06-01-2024"}},
          {{"name": "payment_status", "agg": "equals", "tab": "subscriptions", "col": "payment_status", "row": "approved"}},
          {{"name": "renewal_notice", "agg": "equals", "tab": "subscriptions", "col": "renewal_notice", "row": "true"}}
        ]
      }},
      {{
        "name": "Total Users",
        "relation": "or",
        "variables": [
          {{"name": "subscription_id", "agg": "count", "tab": "subscriptions", "col": "subscription_id"}},
          {{"name": "user_id", "agg": "count", "tab": "subscriptions", "col": "user_id"}}
        ]
      }}
    ]
  }}
}}
```

_Conversation History_
User: So we want to look at AOV times 120% as the projected future growth. AOV is the total order value divided by its frequency.
Agent: Got it, and how do we break down the total order value?
User: We look at class revenue for our target cohort, basically the last month.
Agent: This cohort is the month of June, correct? Also, how do we break down the class revenue?
User: Yes, that's right. The class revenue is the number of classes purchased multiplied by the cost per class, which we can set as $300.
In this example, we are calculating Customer Lifetime Value (LTV).

_Output_
```json
{{
  "thought": "The user has outlined LTV = AOV * 1.2, and the shared that AOV is Total Order Value / Order Frequency. Total Order Value is Class Revenue for the June cohort, which is the number of classes purchased times the cost per class. We are still missing the Order Frequency part.",
  "formula": {{
    "name": "Customer Lifetime Value",
    "relation": "multiply",
    "variables": [
      {{
        "name": "AOV",
        "relation": "divide",
        "variables": [
          {{
            "name": "total_order_value",
            "relation": "and",
            "variables": [
              {{
                "name": "class_revenue",
                "relation": "multiply",
                "variables": [
                  {{ "name": "classes_purchased", "agg": "equals", "tab": "user_activity", "col": "activity_type", "row": "purchase" }},
                  {{ "name": "cost_per_class", "agg": "constant", "tab": "N/A", "col": "N/A", "row": "300" }}
                ]
              }},
              {{ "name": "recent_purchases", "agg": "greater_than", "tab": "orders", "col": "created_at", "row": "06-01-2024" }}
            ]
          }},
          {{ "name": "order_frequency", "relation": "placeholder", "variables": [] }}
        ]
      }}, {{
        "name": "future_growth", "agg": "constant", "tab": "N/A", "col": "N/A", "row": "1.2"
      }}
    ]
  }}
}}
```

For our final scenario, suppose we have 3 tables containing the following columns:
* CustomerID, CustName, FavCuisineType, ShippingAddress, ContactNumber, IsActive, Twitter, Instagram, Yelp in CustContact
* OrderID, CustomerID, RestaurantID, OrderDate, TotalAmount, DeliveryAddress, OrderStatus, EstDeliveryTime, SpecialInstructions in CustOrders
* OfferID, OfferTitle, OfferDescription, OrderKey, StartDate, EndDate, DiscountAmount, ApplicableRestaurants, RedemptionCode in MarketingOffers

_Conversation History_
User: What is our customer acquisition cost?
Agent: Is there a specific time frame you want to consider?
User: Yes, let's look at the past quarter.
In this example, we are calculating Customer Acquisition Cost (CAC). Assume that the past quarter is from July 1, 2024, to September 30, 2024.

_Output_
```json
{{
  "thought": "CustomerID maybe roughly estimates new customers, but there is nothing related to acquisition cost at all. We can still use the OrderDate column to filter for the past quarter.",
  "formula": {{
    "name": "Customer Acquisition Cost",
    "relation": "and",
    "variables": [
      {{
        "name": "cost per customer",
        "relation": "divide",
        "variables": [
          {{"name": "acquisition_cost", "relation": "placeholder", "variables": []}},
          {{"name": "new_customers", "agg": "count", "tab": "CustContact", "col": "CustomerID"}}
        ]
      }}, {{
        "name": "past_quarter", "agg": "greater_than", "tab": "CustOrders", "col": "OrderDate", "row": "2024-07-01"
      }}
    ]
  }}
}}
```
---
Now it's your turn! Please generate a comprehensive thought followed by a JSON-formatted formula for calculating {metric}.

_Scenario Details_
Recent thought: {thought}
Valid tables: {tables}
Likely columns: {likely_cols}
All available columns: {columns}

_Conversation History_
{history}

_Output_
"""

segment_exemplar_prompt = """Given the conversation history, your task is to construct the formula for calculating the '{metric}' metric.
Although the conversation mentions additional segmentation of the metric, we will address that in a later step. Just focus on the core metric for now, including any necessary filters.
Formulas are constructed as a tree of Expression objects, where the root node is the final Expression representing the metric.
The internal nodes are called variables that combine or compare other Expressions, while the leaf nodes are Clauses that reference specific columns in a table.

Each Expression is composed of a name, variables, and relation, which conveys the relationship between those variables. The variables are either other Expressions or Clauses.
Valid relations include: add (+), subtract (-), multiply (*), divide (/), exponent (^), and (&), or (|), less_than (<), greater_than (>), equals (=), conditional (?).
Typically speaking, 'add', 'subtract', 'multiply', 'divide', 'exponent', and 'conditional' represent mathematical operations that return numeric values.
On the other hand, 'and', 'or', 'less_than', 'greater_than', and 'equals' represent logical operations that return boolean values.
In addition, 'conditional' is also unique as the only relation that requires three variables instead of two: `variable1 ? variable2 : variable3`.
There is one last relationship called 'placeholder', which should be used if and only if you are unsure about how to properly structure the expression at that level.

At a high level, examples of metrics may take the form:
  * ARPU = Formula('Average Revenue per User', relation='divide', variables=[revenue, customer])
  * CAC = Formula('Customer Acquisition Cost', relation='divide', variables=[cost, customer])
  * CPC = Formula('Cost per Click', relation='divide', variables=[ad_spend, click])
  * CTR = Formula('Click-Through Rate', relation='divide', variables=[click, impression])
  * LTV = Formula('Customer Lifetime Value', relation='divide', variables=[ARPU, Churn])
  * MAU = Formula('Monthly Active Users', relation='and', variables=[active_count, past_month])
  * Profit = Formula('Net Profit', relation='subtract', variables=[revenue, cost])
  * Revenue = Formula('Total Revenue', relation='multiply', variables=[price, quantity])
  * ROAS = Formula('Return on Ad Spend', relation='divide', variables=[revenue, ad_spend])
  * Views = Formula('Total Page Views', relation='add', variables=[google_visits, facebook_visits, instagram_visits])

Clauses are composed of a name, aggregation (agg), table (tab), row, and column (col). They represent the data that is grounded to specific columns in a table. Valid aggregations are:
  * sum - total of all non-null values
  * count - count the number of unique values
  * average - take the mean of all non-null values
  * top - the top N values, where N > 1. If N == 1, then use 'max' instead.
  * bottom - the bottom N values, where N > 1. If N == 1, then use 'min' instead.
  * min - minimum value in the column, equivalent to 'bottom 1'
  * max - maximum value in the column, equivalent to 'top 1'
  * equals - filter for rows where the column equals a specific value N
  * not - filter for rows where the column does not contain a specific value N
  * less_than - filter for rows where the column is less than a specific value N
  * greater_than - filter for rows where the column is greater than a specific value N
  * empty - simply decides whether the row is null
  * filled - opposite of empty, decides whether the row is not null
  * constant - a constant value of N that is not derived from the data
  * all - no aggregation, just keep all the raw values in the column

For the aggregations of [top, bottom, greater_than, less_than, not, equals, constant], the 'row' field is used to specify the value of N.
Rates (ie. CTR, CVR, etc.) are expected to range from 0 to 1, and should NOT be multiplied by 100 to convert into a percentage.
When choosing a name for the clause, pick something short and descriptive that contains no more than 2-3 parts, connected by underscores.

Please start by thinking carefully about what the user is trying to calculate, and focus on the tables and columns that are most relevant to the calculation.
If the metric is complex, try to break down the calculation into smaller steps and think about how each step can be represented as an expression.
If it is unclear how to structure the expression, use the 'placeholder' relation with an empty list of variables to indicate that you need some guidance from the user.
Your entire response should be in well-formatted JSON starting with keys for thought (string) and formula (dict).
The formula dict should be a tree of expression objects, expanded to include all child expressions and clauses. There should be no further text or explanations after the JSON output.

For example,
---
## Scenario 1 - Ecommerce Online Advertiser
* Tables: AdobeAnalytics_final; SalesRecord_Shopify_0812; Klaviyo_Email_Lists
* Columns: event_date, channel, campaign_id, impressions, clicks, conversions, interstitial_displays, time_on_site, landing_page_views, added_to_cart, checkout_starts in AdobeAnalytics_final;
order_id, date_created, customer_id, utm_source, utm_medium, utm_campaign, order_total, items_purchased, shipping_cost, shipping_date, discount_code in SalesRecord_Shopify_0812;
customer_id, email, first_purchase_date, last_purchase_date, total_orders, avg_order_value, campaign_analytics_id, user_tags, join_channel, email_engagement_score, subscription_status in Klaviyo_Email_Lists

_Conversation History_
User: Basically, when the user stays on a product page for more than a minute, we show them a pop-up asking them to sign up for our newsletter. So there's a sign-up rate associated with that.
Agent: Got it, I will use a sign-up as a conversion event and the number of times the pop-up is shown as the number of visits. Does that sound right?
User: yea, but we also want to check if (a) did they have anything in their shopping cart and (b) what variation they saw based on the split test assignment
In this example, we are trying to calculate Conversion Rate (CVR).

_Output_
```json
{{
  "thought": "We should divide email sign-ups by the number of times the pop-up is shown. Since Klaviyo holds the newsletter mailing list, we just need to restrict to those coming from the website, which we can do through the join_channel column. Items in cart and active split test are segments rather than filters, so we will defer that for now.",
  "formula": {{
    "name": "Conversion Rate",
    "relation": "divide",
    "variables": [
      {{
        "name": "sign_ups_from_website",
        "relation": "and",
        "variables": [
          {{"name": "email_signup", "agg": "count", "tab": "Klaviyo_Email_Lists", "col": "customer_id"}},
          {{"name": "from_website", "agg": "equals", "tab": "Klaviyo_Email_Lists", "col": "join_channel", "row": "website"}}
        ]
      }},
      {{"name": "popup_views", "agg": "sum", "tab": "AdobeAnalytics_final", "col": "interstitial_displays"}}
    ]
  }}
}}
```

_Conversation History_
User: yea, the paid spend from adobe analytics sounds correct
Agent: Facebook Ads has the highest customer acquisition cost, mainly due to a of low number of sales. Please see the table for more.
User: But maybe the sales are high value items? Maybe each buyer has high LTV?
Agent: Certainly, that could be the case. Should I calculate the LTV for customers from Facebook?
User: Not sure how we would even do that, can we just get the ROAS for Facebook instead? I want this broken down by month for the past 12 months.
In this example, we are trying to calculate Return on Ad Spend (ROAS).

_Output_
```json
{{
  "thought": "Total return is the product of the number of orders and the average order value. Total spend is the sum of the ad spend. We also need to apply a filter for Facebook and the past year. Segmenting by month is handled separately.",
  "formula": {{
    "name": "Return on Ad Spend",
    "relation": "and",
    "variables": [
      {{
        "name": "ROAS",
        "relation": "divide",
        "variables": [
          {{
            "name": "total_return",
            "relation": "multiply",
            "variables": [
              {{"name": "num_orders", "agg": "all", "tab": "SalesRecord_Shopify_0812", "col": "total_orders"}},
              {{"name": "revenue_per_order", "agg": "all", "tab": "SalesRecord_Shopify_0812", "col": "avg_order_value"}}
            ]
          }}, {{
            "name": "ad_spend", "agg": "sum", "tab": "AdobeAnalytics_final", "col": "paid_spend",
          }}
        ]
      }}, {{
        "name": "facebook_filter", "agg": "equals", "tab": "AdobeAnalytics_final", "col": "channel", "row": "Facebook"
      }}, {{
        "name": "past_year", "agg": "greater_than", "tab": "AdobeAnalytics_final", "col": "event_date", "row": "12 months ago"
      }}
    ]
  }}
}}
```

## Scenario 2 - Electronic Gaming Retailer
* Tables: MS_Dynamics_365_Sales; MSD_GameMetrics; MSD_CustomerEngagement
* Columns: Transaction_ID, Kiosk_ID, Transaction_Datetime, Payment_Type, Transaction_Total, Promo_Code_Applied, Customer_Loyalty_ID, Product_Line_Items, Associate_ID, City_Location, State_Location in MS_Dynamics_365_Sales;
Product_ID, Title, Subtitle, Studio_Name, Original_Release, Launch_Date, Console_Type, Retail_Price, Quantity_Sold, Microtransaction_Revenue, Monthly_Player_Count in MSD_GameMetrics;
Customer_ID, Loyalty_Status, Customer_Age_Bracket, Purchase_Date, Transaction_ID, Gameplay_Hours, Recent_Activity_Date, Levels_Completed, Trophies_Earned, Avg_Playtime_Minutes, Primary_Gaming_Platform in MSD_CustomerEngagement

_Conversation History_
User: I wonder if the kiosks near our stores are generating more revenue.
Agent: Sure, I can sum up all the Transaction_Total for each kiosk location. I can use City_Location to determine whether the kiosk is located, but I'll need to know where the stores are located. Also, is there a time range we should focus on?
User: Let's go with the last quarter. I'd like to get the revenue per kiosk split by whether or not they are near a store or not.
In this example, we are trying to calculate Average Revenue Per Kiosk (ARPK).

_Output_
```json
{{
  "thought": "Kiosk revenue is the sum of Transaction_Total, and then I should filter for the last quarter. Proximity to stores turns out to be a segment rather than a filter, so we can defer till later.",
  "formula": {{
    "name": "Average Revenue Per Kiosk",
    "relation": "and",
    "variables": [
      {{"name": "kiosk_revenue", "agg": "sum", "tab": "MS_Dynamics_365_Sales", "col": "Transaction_Total"}},
      {{"name": "last_quarter", "agg": "greater_than", "tab": "MS_Dynamics_365_Sales", "col": "Transaction_Datetime", "row": "3 months ago"}}
    ]
  }}
}}
```

_Conversation History_
User: That's not necessarily true, older people spend less time on the app, but they have more money to spend.
Agent: You make a good point, should I segment the average order value by age to find out? Are there any discounts or filters to apply?
User: Let's focus on just the total purchase value and forget about any in-game transactions.
In this example, we are calculating Average Order Value (AOV).

_Output_
```json
{{
  "thought": "Total purchase value is likely the product of retail price and quantity sold. Microtransaction revenue probably represents in-game transactions, so we can ignore that. We deal with age segmentation afterwards.",
  "formula": {{
    "name": "Average Order Value",
    "relation": "multiply",
    "variables": [
      {{"name": "retail_price", "agg": "all", "tab": "MSD_GameMetrics", "col": "retail_price"}},
      {{"name": "quantity_sold", "agg": "all", "tab": "MSD_GameMetrics", "col": "quantity_sold"}}
    ]
  }}
}}
```

## Scenario 3 - Flexfit Fitness Club
* Tables: FFC Membership; ClassSchedule; Flexfit Fitness Profile
* Columns: MemberID, FirstName, LastName, Email, Phone, CheckIns, MembershipType, RemainingClasses, PaymentMethod, FirstPaymentDate, LastPaymentDate, EmergencyContactName, EmergencyContactPhone, DateOfBirth, JoinDate, ReferredBy in FFC Membership;
ClassID, ClassName, Instructor, Room, DayOfWeek, StartTime, EndTime, MaxCapacity, CurrentEnrollment, Description, DifficultyLevel, EquipmentNeeded, IsSpecialEvent, IsCancelled, SubstituteInstructor in ClassSchedule;
ProfileID, MemberID, Height, Weight, BodyFatPercentage, BMI, FitnessGoals, HealthConditions, Injuries, AssessmentDate, StepsAverage, RestingHeartRate, MaxHeartRate, VO2Max, StrengthLevel, CardioLevel, MobilityLevel in Flexfit Fitness Profile

_Conversation History_
User: How many people actually end up attending our special event classes? I wonder if weekends end up being more convenient for people.
Agent: It sounds like you want to calculate the attendance rate filtered to special events, segmented by weekday vs weekend. I will count attendance using the CheckIns column, is that right?
User: Actually, let's just drill down to day of week. I'd like a graph that shows signups and then actual attendance together.
In this example, we are calculating Attendance Rate (Attend).

_Output_
```json
{{
  "thought": "Attendance is the number of check-ins, and signups is the number of classes. We will also filter for special events. We will deal with the day of week segmentation later.",
  "formula": {{
    "name": "Attendance Rate",
    "relation": "and",
    "variables": [
      {{
        "name": "attendance",
        "relation": "divide",
        "variables": [
          {{"name": "attend_class", "agg": "count", "tab": "FFC Membership", "col": "CheckIns"}},
          {{"name": "signup_for_class", "agg": "count", "tab": "ClassSchedule", "col": "ClassName"}}
        ]
      }}, {{
        "name": "special_event", "agg": "equals", "tab": "ClassSchedule", "col": "IsSpecialEvent", "row": true
      }}
    ]
  }}
}}
```

## Scenario 4 - Security Monitoring SaaS Company
* Tables: MarketingQualifiedLeads; Subscriptions; RenderActivity
* Columns: LeadID, CompanyName, ContactEmail, IndustryType, AnnualAdSpend, TeamSize, LeadSource, DemoScheduled, PocStatus, SalesRepID, ClientUUID, LastTouchpoint in MarketingQualifiedLeads;
SubscriptionID, ClientUUID, PlanTier, GPUAllocation, MonthlyQuota, BillingCycle, PaymentStatus, OverageRate, ApiKeyHash, ContractStatus in Subscriptions;
ReleaseVersion, StartTimestamp, MaxResolution, ClientUUID, ProjectName, RenderType, FeaturesList, ProcessingTime, OutputFormat, ComputeUnits, QueuePriority, RenderStatus, ErrorTrace in RenderActivity

_Conversation History_
User: I want to extract the Sales Qualified Leads out of the MQLs, which are basically the most high value prospects to go after.
Agent: Sure, how do you want to define high value prospects?
User: Ok, so there are three main criteria: (1) they should be have annual revenue > 100K and team size > 50 (2) they should have a demo scheduled or completed (3) they should have an active pilot going on
Agent: No problem, these are the Sales Qualified Leads based on your criteria.  Does this look right to you?
User: Yea, but can we break this down into industry type? Also, we should include the contact email in the results as well.
In this example, we are calculating Sales Qualified Lead (SQL).

_Output_
```json
{{
  "thought": "The user has specified the conditions for high value prospects, so we can use the AnnualAdSpend and TeamSize columns to filter for them. Demo scheduled and active pilot can be calculated by using the DemoScheduled and PocStatus columns. Email info is not part of the formula.",
  "formula": {{
    "name": "Sales Qualified Lead",
    "relation": "and",
    "variables": [
      {{
        "name": "high_value_prospect",
        "relation": "or",
        "variables": [
          {{"name": "enterprise_adspend", "agg": "greater_than", "tab": "MarketingQualifiedLeads", "col": "AnnualAdSpend", "row": "100000"}},
          {{"name": "large_team", "agg": "greater_than", "tab": "MarketingQualifiedLeads", "col": "TeamSize", "row": "50"}}
        ]
      }}, {{
        "name": "demo_scheduled", "agg": "equals", "tab": "MarketingQualifiedLeads", "col": "DemoScheduled", "row": true
      }}, {{
        "name": "active_pilot", "agg": "equals", "tab": "MarketingQualifiedLeads", "col": "PocStatus", "row": "Active"
      }}
    ]
  }}
}}
```

_Conversation History_
User: There's big spike in processing time a few days ago, what happened?
Agent: There are a number of potential causes, such as server issues or high demand. Is there anything that you suspect might have caused the spike?
User: I would start by grabbing the average processing time per hour. We can then check if there's any correlation with the new features list and error trace.
Agent: Sure, just to be clear, are you suggesting we look at when features were released and when errors occurred? I am not seeing columns for either, so could you help me out? Also, does the last 3 days sound good?
User: We should go back 5 days. And, let's go with the errors first. We should bucket them into either: critical errors (anything with the word 'downtime'), configuration errors (invalid settings, incompatible render types), or memory errors (quota exceeded, out of memory)
In this example, we are calculating Hourly Processing Time (PT).

_Output_
```json
{{
  "thought": "We want to calculate the average processing time for the last 5 days, split by hour. We will worry about segmentation by error type as a separate step.",
  "formula": {{
    "name": "Hourly Processing Time",
    "relation": "and",
    "variables": [
      {{
        "name": "avg_processing_time",
        "relation": "and",
        "variables": [
          {{"name": "processing_time", "agg": "average", "tab": "RenderActivity", "col": "ProcessingTime"}},
          {{"name": "hourly_split", "agg": "all", "tab": "RenderActivity", "col": "StartTimestamp", "row": "group by hour"}}
        ]
      }}, {{
        "name": "recent_renders", "agg": "greater_than", "tab": "RenderActivity", "col": "StartTimestamp", "row": "5 days ago"
      }}
    ]
  }}
}}
```
---
Now it's your turn! Please generate a comprehensive thought along with a formula for calculating {metric}, all within a JSON-formatted output.
As a reminder, do not focus on segmenting the metric or splitting it into buckets. We will address that in a later step.

_Scenario Details_
Recent thought: {thought}
Valid tables: {tables}
Likely columns: {likely_cols}
All available columns: {columns}

_Conversation History_
{history}

_Output_
"""

reasoning_variables_prompt = """Given the conversation history and supporting details, your task is to construct the formula for calculating the '{metric}' metric.

## Formula Structure
Formulas are constructed as a tree of Expression objects:
  - Root node: The final Expression representing the metric
  - Internal nodes: Expressions that combine or compare other expressions
  - Leaf nodes: Clauses that reference specific columns in a table

## Expressions
Each Expression is composed of:
  - name: A descriptive identifier; at the root level this is the name of the metric
  - variables: List of child Expressions or Clauses
  - relation: How the variables combine with each other

## Examples of Formulas
  * ARPU = Formula('Average Revenue per User', relation='divide', variables=[revenue, customer])
  * CAC = Formula('Customer Acquisition Cost', relation='divide', variables=[cost, customer])
  * CPC = Formula('Cost per Click', relation='divide', variables=[ad_spend, click])
  * CTR = Formula('Click-Through Rate', relation='divide', variables=[click, impression])
  * LTV = Formula('Customer Lifetime Value', relation='divide', variables=[ARPU, Churn])
  * MAU = Formula('Monthly Active Users', relation='and', variables=[active_count, past_month])
  * Profit = Formula('Net Profit', relation='subtract', variables=[revenue, cost])
  * Revenue = Formula('Total Revenue', relation='multiply', variables=[price, quantity])
  * ROAS = Formula('Return on Ad Spend', relation='divide', variables=[revenue, ad_spend])
  * Views = Formula('Total Page Views', relation='add', variables=[google_visits, facebook_visits, instagram_visits])

## Relations
Valid relations include:
  - add (+): addition, contains two or more variables
  - subtract (-): subtraction, contains two variables
  - multiply (*): product, multiplication, contains two or more variables
  - divide (/): division, rate, contains two variables: `variable1 / variable2`
  - exponent (^): exponential, square, contains exactly two variables
  - and (&): and, join, returns boolean value
  - or (|): A or B, combine, returns boolean value
  - less_than (<): less than, returns boolean value: `variable1 < variable2`
  - greater_than (>): greater than, returns boolean value: `variable1 > variable2`
  - equals (=): equals, returns boolean value: `variable1 == variable2`
  - conditional (?): conditional, if-then-else, contains three variables: `variable1 ? variable2 : variable3`
Special cases:
  - Since expressions are recursive, you can nest relations to create more complex constructions:
    * `A >= B` can be expressed by combining 'greater_than' and 'equals' with an 'or' operation: `(A > B) | (A = B)`.
    * `B < A < C` can be expressed by combining 'less_than' and 'less_than' with an 'and' operation: `(B < A) & (A < C)`.
  - When you are unsure about how to structure the expression, use the 'placeholder' relation with an empty list of variables to indicate that you need some guidance from the user.
  - If there is any ambiguity about how to structure the expression, go as far as you can and then use 'placeholder' rather than making assumptions.
  - Pay careful attention when setting the order of variables for operations like division and subtraction because `A / B` is not the same as `B / A`.

## Clauses
Clauses ground the formula to specific columns in a table. Each clause has the following properties:
  - name: Identifier for the clause
  - aggregation (agg): How to process the column values, set to 'all' by default
  - table (tab): Source table name
  - column (col): Column name within the table
  - row (optional): Specific value to filter the column by, may be omitted if aggregation is 'all'

## Aggregations
The valid aggregations are:
  * sum - total of all non-null values
  * count - count the number of unique values
  * average - take the mean of all non-null values
  * top - the top N values, where N > 1. If N == 1, then use 'max' instead.
  * bottom - the bottom N values, where N > 1. If N == 1, then use 'min' instead.
  * min - minimum value in the column, equivalent to 'bottom 1'
  * max - maximum value in the column, equivalent to 'top 1'
  * equals - filter for rows where the column equals a specific value N
  * not - filter for rows where the column does not contain a specific value N
  * less_than - filter for rows where the column is less than a specific value N
  * greater_than - filter for rows where the column is greater than a specific value N
  * empty - simply decides whether the row is null
  * filled - opposite of empty, decides whether the row is not null
  * constant - a constant value of N that is not derived from the data
  * all (default) - no aggregation, just keep all the raw values in the column
For the aggregations of [top, bottom, greater_than, less_than, not, equals, constant], the 'row' field is used to specify the value of N.
If the aggregation is set to 'constant', then the table and column fields can be set to 'N/A' to indicate that the value is not derived from the data.
Rates (ie. CTR, CVR, etc.) are expected to range from 0 to 1, and should NOT be multiplied by 100 to convert into a percentage.

## Naming Conventions
When choosing a name for the expression or clause, pick something short and descriptive that contains no more than 2-3 parts, connected by underscores.
For instance, suppose you are calculating the average order value for a specific product called 'Orbit Face Cleanser' in the last month:
  - a good name would be 'orbit_cleanser_aov', since it only contains the three most important parts connected by underscores
  - another good option might be 'monthly_orbit_aov' depending on what is more relevant in the context
  - a bad name would be 'average_order_value_orbit_face_cleanser_last_month', since it contains eight parts
  - even a name like 'orbit_face_cleanser_aov' is too long, since it contains four parts
  - with that said, 'last_month_face' is short enough, but is a _terrible_ name because it does not describe the metric being calculated
The exception to this rule is the root node, which should be the full name of the metric.

## Output Structure
Suppose the columns are immediately apparent from the table, and the metric is Click-Through Rate (CTR) on LinkedIn. Then, the output might be:
```json
{{
  "name": "Click-Through Rate",
  "relation": "and",
  "variables": [
    {{
      "name": "CTR",
      "relation": "divide",
      "variables": [
        {{"name": "clicks", "agg": "sum", "tab": "activities", "col": "total clicks"}},
        {{"name": "impressions", "agg": "count", "tab": "activities", "col": "page impressions"}}
      ]
    }}, {{
      "name": "LinkedIn_filter", "agg": "equals", "tab": "activities", "col": "channel", "row": "LinkedIn"
    }}
}}
```
Note that the root node is the full name of the metric, while the child nodes are short and descriptive, connected by underscores.
Furthermore, note the LinkedIn filter comes first since it is a condition that applies to the entire formula, not just the clicks or impressions.

Alternatively, suppose we have to dig a bit deeper to find the relevant columns. For this example, we use the Net Profit (NP) metric. In this case, the output might look like:
```json
{{
  "name": "Net Profit",
  "relation": "subtract",
  "variables": [
    {{
      "name": "total_revenue",
      "relation": "add",
      "variables": [
        {{"name": "paid_subscription", "agg": "sum", "tab": "shopify_results", "col": "subcription revenue"}},
        {{"name": "one_time_purchase", "agg": "sum", "tab": "shopify_results", "col": "purchases made"}}
      ]
    }},
    {{
      "name": "total_cost",
      "relation": "add",
      "variables": [
        {{"name": "vendor_cost", "agg": "sum", "tab": "shopify_results", "col": "final shopify fees"}},
        {{"name": "advertising_cost", "agg": "sum", "tab": "google_ads", "col": "total spend"}}
      ]
    }}
  ]
}}
```

Finally, suppose we are only able to build part of the formula. For this case, we can use the 'placeholder' relation.
In this example, we want to calculate Customer Acquisition Cost (CAC) in the past two months, but we are unsure what determines a new customer. The output might look like:
```json
{{
  "name": "Customer Acquisition Cost",
  "relation": "divide",
  "variables": [
    {{
      "name": "acquisition_cost",
      "relation": "add",
      "variables": [
        {{
          "name": "google_spend",
          "relation": "and",
          "variables": [
            {{"name": "google_spend", "agg": "sum", "tab": "google_ads", "col": "budget"}},
            {{"name": "past_two_months", "agg": "greater_than", "tab": "google_ads", "col": "date", "row": "2023-06-01"}}
          ]
        }},
        {{
          "name": "social_media_spend",
          "relation": "and",
          "variables": [
            {{"name": "facebook_spend", "agg": "sum", "tab": "facebook_ads", "col": "total cost"}},
            {{"name": "past_two_months", "agg": "greater_than", "tab": "facebook_ads", "col": "view_date", "row": "2023-06-01"}}
          ]
        }},
        {{"name": "print_spend",
          "relation": "and",
          "variables": [
            {{"name": "magazine_spend", "agg": "sum", "tab": "print_ads", "col": "printing fees"}},
            {{"name": "past_two_months", "agg": "greater_than", "tab": "print_ads", "col": "delivery_date", "row": "2023-06-01"}}
          ]
        }}
      ]
    }},
    {{
      "name": "new_customers",
      "relation": "and",
      "variables": [
        {{"name": "acquired_customers", "relation": "placeholder", "variables": []}}
        {{"name": "past_two_months", "agg": "greater_than", "tab": "customer_activity", "col": "event_timestamp", "row": "2023-06-01 00:00:00"}}
      ]
    }}
  ]
}}
```

## Conversation History
{history}

## Supporting Details
Available Tables: {tables}
Likely Columns: {likely_cols}
All Valid Columns: {columns}

## Your Task
Now, please think carefully about how to construct the formula for calculating {metric}, and then output it in JSON format.
There should be no additional text or explanations before or after the JSON output.
"""

segment_reasoning_prompt = """Given the conversation history and supporting details, your task is to construct the formula for calculating the '{metric}' metric.
Although the conversation mentions additional segmentation of the metric, we will address that in a later step. Just focus on the core metric for now, including any necessary filters.

## Formula Structure
Formulas are constructed as a tree of Expression objects:
  - Root node: The final Expression representing the metric
  - Internal nodes: Expressions that combine or compare other expressions
  - Leaf nodes: Clauses that reference specific columns in a table

## Expressions
Each Expression is composed of:
  - name: A descriptive identifier; at the root level this is the name of the metric
  - variables: List of child Expressions or Clauses
  - relation: How the variables combine with each other

## Examples of Formulas
  * ARPU = Formula('Average Revenue per User', relation='divide', variables=[revenue, customer])
  * CAC = Formula('Customer Acquisition Cost', relation='divide', variables=[cost, customer])
  * CPC = Formula('Cost per Click', relation='divide', variables=[ad_spend, click])
  * CTR = Formula('Click-Through Rate', relation='divide', variables=[click, impression])
  * LTV = Formula('Customer Lifetime Value', relation='divide', variables=[ARPU, Churn])
  * MAU = Formula('Monthly Active Users', relation='and', variables=[active_count, past_month])
  * Profit = Formula('Net Profit', relation='subtract', variables=[revenue, cost])
  * Revenue = Formula('Total Revenue', relation='multiply', variables=[price, quantity])
  * ROAS = Formula('Return on Ad Spend', relation='divide', variables=[revenue, ad_spend])
  * Views = Formula('Total Page Views', relation='add', variables=[google_visits, facebook_visits, instagram_visits])

## Relations
Valid relations include:
  - add (+): addition, contains two or more variables
  - subtract (-): subtraction, contains two variables
  - multiply (*): product, multiplication, contains two or more variables
  - divide (/): division, rate, contains two variables: `variable1 / variable2`
  - exponent (^): exponential, contains exactly two variables: `variable1 ^ variable2`
  - and (&): and, join, returns boolean value
  - or (|): A or B, combine, returns boolean value
  - less_than (<): less than, returns boolean value: `variable1 < variable2`
  - greater_than (>): greater than, returns boolean value: `variable1 > variable2`
  - equals (=): equals, returns boolean value: `variable1 == variable2`
  - conditional (?): conditional, if-then-else, contains three variables: `variable1 ? variable2 : variable3`
Special cases:
  - Since expressions are recursive, you can nest relations to create more complex constructions:
    * `A >= B` can be expressed by combining 'greater_than' and 'equals' with an 'or' operation: `(A > B) | (A = B)`.
    * `B < A < C` can be expressed by combining 'less_than' and 'less_than' with an 'and' operation: `(B < A) & (A < C)`.
  - When you are unsure about how to structure the expression, use the 'placeholder' relation with an empty list of variables to indicate that you need some guidance from the user.
  - If there is any ambiguity about how to structure the expression, go as far as you can and then use 'placeholder' rather than making assumptions.
  - Pay careful attention when setting the order of variables for operations like division and subtraction because `A / B` is not the same as `B / A`.
  - You do not need to worry about how to join tables together to form Expressions since that will be handled later.

## Clauses
Clauses ground the formula to specific columns in a table. Each clause has the following properties:
  - name: Identifier for the clause
  - aggregation (agg): How to process the column values, set to 'all' by default
  - table (tab): Source table name
  - column (col): Column name within the table
  - row (optional): Specific value to filter the column by, may be omitted if aggregation is 'all'

## Aggregations
The valid aggregations are:
  * sum - total of all non-null values
  * count - count the number of unique values
  * average - take the mean of all non-null values
  * top - the top N values, where N > 1. If N == 1, then use 'max' instead.
  * bottom - the bottom N values, where N > 1. If N == 1, then use 'min' instead.
  * min - minimum value in the column, equivalent to 'bottom 1'
  * max - maximum value in the column, equivalent to 'top 1'
  * equals - filter for rows where the column equals a specific value N
  * not - filter for rows where the column does not contain a specific value N
  * less_than - filter for rows where the column is less than a specific value N
  * greater_than - filter for rows where the column is greater than a specific value N
  * empty - simply decides whether the row is null
  * filled - opposite of empty, decides whether the row is not null
  * constant - a constant value of N that is not derived from the data
  * all (default) - no aggregation, just keep all the raw values in the column
For the aggregations of [top, bottom, greater_than, less_than, not, equals, constant], the 'row' field is used to specify the value of N.
Do not confuse the 'add' relation with the 'sum' aggregation. The former is used in Expressions, while the latter is used in Clauses.
Rates (ie. CTR, CVR, etc.) are expected to range from 0 to 1, and should NOT be multiplied by 100 to convert into a percentage.

## Naming Conventions
When choosing a name for the expression or clause, pick something short and descriptive that contains no more than 2-3 parts, connected by underscores.
For instance, suppose you are calculating the average order value for a specific product called 'Orbit Face Cleanser' in the last month:
  - a good name would be 'orbit_cleanser_aov', since it only contains the three most important parts connected by underscores
  - another good option might be 'monthly_orbit_aov' depending on what is more relevant in the context
  - a bad name would be 'average_order_value_orbit_face_cleanser_last_month', since it contains eight parts
  - even a name like 'orbit_face_cleanser_aov' is too long, since it contains four parts
  - with that said, 'last_month_face' is short enough, but is a _terrible_ name because it does not describe the metric being calculated
The exception to this rule is the root node, which should be the full name of the metric.

## Output Structure
Suppose the columns are immediately apparent from the table, and the metric is Click-Through Rate (CTR) on LinkedIn. Then, the output might be:
```json
{{
  "name": "Click-Through Rate",
  "relation": "and",
  "variables": [
    {{
      "name": "CTR",
      "relation": "divide",
      "variables": [
        {{"name": "clicks", "agg": "sum", "tab": "activities", "col": "total clicks"}},
        {{"name": "impressions", "agg": "count", "tab": "activities", "col": "page impressions"}}
      ]
    }}, {{
      "name": "LinkedIn_filter", "agg": "equals", "tab": "activities", "col": "channel", "row": "LinkedIn"
    }}
}}
```
Note that the root node is the full name of the metric, while the child nodes are short and descriptive, connected by underscores.
Furthermore, note the LinkedIn filter comes first since it is a condition that applies to the entire formula, not just the clicks or impressions.

Alternatively, suppose we have to dig a bit deeper to find the relevant columns. For this example, we use the Net Profit (NP) metric. In this case, the output might look like:
```json
{{
  "name": "Net Profit",
  "relation": "subtract",
  "variables": [
    {{
      "name": "total_revenue",
      "relation": "add",
      "variables": [
        {{"name": "paid_subscription", "agg": "sum", "tab": "shopify_results", "col": "subcription revenue"}},
        {{"name": "one_time_purchase", "agg": "sum", "tab": "shopify_results", "col": "purchases made"}}
      ]
    }},
    {{
      "name": "total_cost",
      "relation": "add",
      "variables": [
        {{"name": "vendor_cost", "agg": "sum", "tab": "shopify_results", "col": "final shopify fees"}},
        {{"name": "advertising_cost", "agg": "sum", "tab": "google_ads", "col": "total spend"}}
      ]
    }}
  ]
}}
```

Finally, suppose we are only able to build part of the formula. For this case, we can use the 'placeholder' relation.
In this example, we want to calculate Customer Acquisition Cost (CAC) in the past two months, but we are unsure what determines a new customer. The output might look like:
```json
{{
  "name": "Customer Acquisition Cost",
  "relation": "divide",
  "variables": [
    {{
      "name": "acquisition_cost",
      "relation": "add",
      "variables": [
        {{
          "name": "google_spend",
          "relation": "and",
          "variables": [
            {{"name": "google_spend", "agg": "sum", "tab": "google_ads", "col": "budget"}},
            {{"name": "past_two_months", "agg": "greater_than", "tab": "google_ads", "col": "date", "row": "2023-06-01"}}
          ]
        }},
        {{
          "name": "social_media_spend",
          "relation": "and",
          "variables": [
            {{"name": "facebook_spend", "agg": "sum", "tab": "facebook_ads", "col": "total cost"}},
            {{"name": "past_two_months", "agg": "greater_than", "tab": "facebook_ads", "col": "view_date", "row": "2023-06-01"}}
          ]
        }},
        {{"name": "print_spend",
          "relation": "and",
          "variables": [
            {{"name": "magazine_spend", "agg": "sum", "tab": "print_ads", "col": "printing fees"}},
            {{"name": "past_two_months", "agg": "greater_than", "tab": "print_ads", "col": "delivery_date", "row": "2023-06-01"}}
          ]
        }}
      ]
    }},
    {{
      "name": "new_customers",
      "relation": "and",
      "variables": [
        {{"name": "acquired_customers", "relation": "placeholder", "variables": []}}
        {{"name": "past_two_months", "agg": "greater_than", "tab": "customer_activity", "col": "event_timestamp", "row": "2023-06-01 00:00:00"}}
      ]
    }}
  ]
}}
```

## Conversation History
{history}

## Supporting Details
Available Tables: {tables}
Likely Columns: {likely_cols}
All Valid Columns: {columns}

## Your Task
Now, please think carefully about how to construct the formula for calculating {metric}, and then output it in JSON format.
There should be no additional text or explanations before or after the JSON output.
"""

More_exemplars = """
_Conversation History_
User: What is the range of peer review scores for all students in the last cohort?
Agent: Using CohortStartDate to determine the last cohort, the peer review scores range from 60 to 95.
User: Ok, taking a broader view, does increased student activity lead to better outcomes?
Agent: How do you want to define student activity in this context? Also, how can we measure better outcomes? I can look at areas like career outcome, salary increase, or NPS score.
User: We mainly care about how many interviews the student gets within a month after finishing the program.

_Supporting Details_
Name: Student Outcome (Outcome)
Variables: interviews, timing
Possible Columns:
  * StudentProgress.LastSubmissionURL, StudentProgress.MilestoneStatus, StudentProgress.ActiveStatus
  * LearnerSuccess.CareerOutcome, LearnerSuccess.SalaryIncrease, LearnerSuccess.NPSScore
  * CodePathCourses.CohortStartDate, CodePathCourses.CohortEndDate

_Output_
```json
{{
  "thought": "Student Outcome could be measured by the number of interviews within a month after graduation, but I don't see a column for that so it's unclear.",
  "code: "Outcome",
  "name": "Student Outcome",
  "description": "number of interviews received per student within a month after graduation",
  "expression": "within",
  "variables": ["interviews", "timing", "unsure"]
}}
```

_Conversation History_
User: How many people interacted with the AI-powered re-writing feature since we launched it?
Agent: How do you define interaction in this context?
User: If the user accepted the suggestion, then that counts as an interaction.
Agent: And when would you consider the launch date?
User: People have been using it since the beginning of the month.

_Supporting Details_
Name: Acceptance Rate (Accept)
Variables: interaction, display
Possible Columns:
  * UserActivity.suggestion_shown, UserActivity.suggestion_clicked, UserActivity.timestamp, UserActivity.user_id, UserActivity.page_url
  * ProjectTracker.release_date, ProjectTracker.feature_name, ProjectTracker.description, ProjectTracker.priority, ProjectTracker.category

Described variables competely by second turn, so medium confidence
  medium confidence - feature launch, grouped by date
  engagement metrics, number of logins, clicks per day, number of new subscriptions

_Output_
```json
{{
  "thought": "If we count acceptance as a click interaction, then we can divide suggestions clicks by suggestions shown to get the percentage. release_date or timestamp can filter for launch date.",
  "code: "Accept",
  "name": "Acceptance Rate",
  "description": "percentage of users who accepted an AI-powered suggestion when shown",
  "expression": "divide",
  "variables": ["interaction", "display"]
}}
```

_Conversation History_
User: We lowered our prices in order to get new users in the door, and I want to know the impact on full revenue. Although we might get less revenue early on, each user might pay buy more in the long run
Agent: Sure, when should we consider the new pricing strategy to have started? Also, I can consider 'plus' products to be upgrades, does that sound right?
User: Yea, anything that has 'plus' in the name is an upgrade. Add-ons are any time a user bought something in addition to their original purchase.
Agent: I see a column for product_name, but how does that connect to the product revenue?
User: You can use the product_id column to join with the income_report table.

_Supporting Details_
Name: Net Upgrade Revenue (Upgrade)
Variables: revenue, revenue
Possible Columns:
  * income_report.sales_date, income_report.product_id, income_report.product_price, income_report.final_revenue
  * purchases.customer_id, purchases.product_id, purchases.income_id, purchases.product_category, purchases.product_quantity, purchases.product_name, purchases.sales_date

_Output_
```json
{{
  "thought": "We first need to filter for active subscriptions from users who joined using the new pricing strategy. Then we sum all sales from upgrades and cross-sells with those subscribers. The formula is (A + B) filtered by C, which we don't have a clear expression for.",
  "code: "Upgrade",
  "name": "Net Upgrade Revenue",
  "description": "Total revenue generated from upgrades and cross-sells from existing customers",
  "expression": "unsure",
  "variables": ["revenue", "revenue", "active"]
}}
```

_Conversation History_
User: Maybe we can look at the customer service data then?
Agent: Sure, what would you like to know?
User: Can we calculate the percentage of tickets resolved when the customer's first contacted us?

_Supporting Details_
Name: First Contact Resolution Rate (FCRR)
Variables: deal, timing
Possible Columns:
  * Zendesk.ticket_id, Zendesk.requester, Zendesk.issue_type, Zendesk.message_header, Zendesk.message_body, Zendesk.open_timestamp, Zendesk.closed_timestamp, Zendesk.status, Zendesk.resolution_time
  * Amplitude.event_id, Amplitude.user_id, Amplitude.event_type, Amplitude.event_time, Amplitude.properties
  * Braze.campaign_message_sent_date, Braze.campaign_message_open_date, Braze.campaign_message_click_date

_Output_
```json
{{
  "thought": "We can first filter for all resolved tickets, then count those which were resolved in the first contact, and then divide the two to get our answer. However, the variables displayed don't seem to match the ones we need.",
  "code: "FCRR",
  "name": "First Contact Resolution Rate",
  "description": "percentage of tickets that are immediately resolved during the first contact",
  "expression": "divide",
  "variables": ["unsure"]
}}
```

_Conversation History_
User: Are we close to hitting our end of quarter projections?
Agent: I can see a column for daily closed deals, but how can we estimate the number of deals we have left to close?
User: We might find something if we dive into our average deal velocity.

_Supporting Details_
Name: Average Deal Velocity (ADV)
Variables: deal, timing
Possible Columns:
  * Salesforce.opportunity_id, Salesforce.stage, Salesforce.deal_size, Salesforce.close_date, Salesforce.first_contact_date, Salesforce.last_contact_date, Salesforce.lead_source, Salesforce.lead_score, Salesforce.deal_size
  * Hubspot.lead_id, Hubspot.source, Hubspot.qualified, Hubspot.propensity_score

_Output_
```json
{{
  "thought": "The user has told us about a potential metric, but we don't have enough information to properly calculate it.",
  "code: "ADV",
  "name": "Average Deal Velocity",
  "description": "unsure",
  "expression": "unsure",
  "variables": ["unsure"]
}}
```

_Conversation History_
User: We should count that using Past Due Outstanding, which is the total amount of money that is owed to us.
Agent: How do you determine when something is considered past due?
User: Amounts listed within accounts receivable are all past due, we just need to subtract any credit balance to get the final amount.
Agent: So we just subtract the credit balance from accounts receivable, is that correct?
User: Yes, just make sure to filter for the last month as well.

_Supporting Details_
Name: Past Due Outstanding (PDO)
Variables: credit, receivable
Possible Columns:
  * payment_processing.accounts_receivable, payment_processing.accounts_payable, payment_processing.account_balance, payment_processing.invoice_date, payment_processing.payment_date
  * customer_info.credit_balance, customer_info.credit_limit, customer_info.credit_score

_Output_
```json
{{
  "thought": "We can calculate the past due outstanding by subtracting the credit balance in the last month from accounts receivable in the last month.",
  "code: "PDO",
  "name": "Past Due Outstanding",
  "description": "total amount of money that is owed to the company",
  "expression": "subtract",
  "variables": ["receivable", "credit"]
}}
```
"""

variable_completion_prompt = """Based on the conversation history, we have generated a candidate formula for calculating the {metric} metric.
However, the formulation is incomplete, meaning some of the variables are not fully populated, so we need your help to finish it.
For some background, a Formula is a tree of variable objects, where variables take the form of either Expressions or Clauses.
Each Expression is composed of a name, its own variables, and a relation, which conveys the relationship between the variables of that expression.
At the leaves of the tree, the variables are Clauses which are composed of a name, aggregation, table, row, and column. They represent the data that is grounded to specific columns in a table.

Valid relations include: add (+), subtract (-), multiply (*), divide (/), exponent (^), and (&), or (|), less_than (<), greater_than (>), equals (=), conditional (?).
There is one last relationship called 'placeholder', which is used to indicate that we were unsure about how to structure the expression at that level.
Your task is to distill the latest feedback from the user to fill in the placeholder variables and complete the formula.
With that said, if the ambiguity still persists, we would rather continue using the 'placeholder' relation rather than making unfounded assumptions.
If the user has explicitly admitted that the Formula is not possible, then please output the name of the metric as 'N/A' with an empty variable list.

Valid aggregations for clauses are:
  * sum - total of all non-null values
  * count - count the number of unique values
  * average - take the mean of all non-null values
  * top - the top N values, where N > 1. If N == 1, then use 'max' instead.
  * bottom - the bottom N values, where N > 1. If N == 1, then use 'min' instead.
  * min - minimum value in the column, equivalent to 'bottom 1'
  * max - maximum value in the column, equivalent to 'top 1'
  * equals - filter for rows where the column equals a specific value N
  * not - filter for rows where the column does not contain a specific value N
  * less_than - filter for rows where the column is less than a specific value N
  * greater_than - filter for rows where the column is greater than a specific value N
  * empty - simply decides whether the row is null
  * filled - opposite of empty, decides whether the row is not null
  * constant - a constant value of N that is not derived from the data
  * all - no aggregation, just keep all the raw values in the column

For the aggregations of [top, bottom, greater_than, less_than, not, equals, constant], the 'row' field is used to specify the value of N.
Rates (ie. CTR, CVR, etc.) are expected to range from 0 to 1, and should NOT be multiplied by 100 to convert into a percentage.
When choosing a name for the clause, pick something short and descriptive that contains no more than 2-3 parts, connected by underscores.
Since clauses are the leaves of the tree, the goal is to have all branches of the formula reach down to the clause level.

Please start by carefully reviewing the conversation history and the partially completed formula, which should serve as your starting point.
Then, examine the preview of the relevant data to decide how to construct the remaining variables to complete the expression.
Note that segmentation of the metric is not necessary at this stage, so focus only on completing the Formula for the core metric.
Your entire response should be in well-formatted JSON starting with keys for thought (string) and finished formula (dict), with no further text or explanations after the JSON output.

For example,
---
## Scenario 1
* Tables: amp_events; orders_acct; monthly_inventory
* Columns: event_id, event_type, event_timestamp, source_link, viewed_url, user_id, session_id, device_type, browser_type, ip_address, user_agent in amp_events;
order_id, purchase_item, page_views, payment_amt, payment_method, purchase_timestamp, shipping_addr, billing_addr, is_deleted, last_modified_dt in orders_acct;
item_sk, item_code, sku, cat_id, cat_name, sub_cat_id, price_msrp, price_current, qty_on_hand, qty_allocated, qty_backordered, supplier_id, supplier_name, date_added, last_restock_dt, reorder_point, is_active, discontinued_flag in monthly_inventory;

_Partial Formula_
{{
  "name": "Click-Through Rate", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "CTR", "verified": false, "relation": "divide",
      "variables": [
        {{"name": "click_event", "relation": "placeholder", "variables": [] }},
        {{"name": "impression_event", "ver": false, "agg": "sum", "tab": "orders_acct", "col": "page_views"}}
      ]
    }}, {{
      "name": "last_month", "ver": false, "agg": "greater_than", "tab": "amp_events", "col": "event_timestamp", "row": "30 days ago"
    }}
  ]
}}

_Conversation History_
User: What is the CTR for the last 30 days?
Agent: Certainly, I can calculate the CTR for the past month by summing the page views in the orders table to get the number of impressions. How should I determine the clicks?
User: That's totally wrong. You should be using the event_type column to determine both clicks and impressions, rather than joining with the orders table.

_Data Preview_
For example purposes, suppose the unique values in the event_type column are:
['view_product', 'refund', 'cancel', 'visit_site', 'add_to_cart', 'purchase', 'checkout', 'credit_card_details']

_Output_
```json
{{
  "thought": "We should use the events to determine clicks and impressions. The best candidates seem to be 'view_product' and 'visit_site', so we will use those for now.",
  "formula": {{
    "name": "Click-Through Rate", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "CTR", "relation": "divide",
        "variables": [
          {{"name": "click_event", "ver": false, "agg": "equals", "tab": "amp_events", "col": "event_type", "row": "view_product"}},
          {{"name": "impression_event", "ver": false, "agg": "equals", "tab": "amp_events", "col": "event_type", "row": "visit_site"}}
        ]
      }}, {{
        "name": "last_month", "ver": false, "agg": "greater_than", "tab": "amp_events", "col": "event_timestamp", "row": "30 days ago"
      }}
    ]
  }}
}}
```

## Scenario 2
* Tables: CodePathCourses; StudentProgress; LearnerSuccess; MarketingCampaigns
* Columns: CourseID, TrackName, LeadMentorID, TechStackJSON, CohortStartDate, CohortEndDate, WeeklyCommitmentHrs, DeliveryFormat, DifficultyLevel, CurrentEnrollment, MaxCapacity, PreReqSkills, GitHubTemplateURL in CodePathCourses;
ProgressID, CourseID, LearnerUUID, EnrollmentDate, MilestoneStatus, LastSubmissionURL, PeerReviewScore, TuitionPlanType, EmploymentStatus, MentorNotes, ActiveStatus in StudentProgress;
SuccessID, LearnerUUID, CourseID, JobPlacement, SalaryIncrease, TestimonialBody, DateSubmitted, NPSScore, CompanyPlaced, ShowcasePermission in LearnerSuccess;
CampaignID, InitiativeName, CareerSegment, LinkedInAudience, CampaignTitle, CampaignBody, AdGroupID, PartnershipType, BudgetAllocated, BudgetSpent, LeadSource in MarketingCampaigns

_Partial Formula_
{{
  "name": "Monthly Active Users", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "MAU", "verified": false, "relation": "and",
      "variables": [
        {{"name": "unique_users", "agg": "count", "tab": "StudentProgress", "col": "LearnerUUID", "ver": true}},
        {{"name": "active_filter", "agg": "equals", "tab": "StudentProgress", "col": "ActiveStatus", "row": true, "ver": false}}
      ]
    }}, {{
      "name": "cohort_filter", "verified": false, "relation": "placeholder", "variables": []
    }}
  ]
}}

_Conversation History_
User: Ok, taking a step back, what are our monthly actives?
Agent: I can take the count of unique students based on LearnerUUID from the StudentProgress table and filter active students. Does that work?
User: Yes, that sounds fine. I just want to focus on students from the April cohort though.
Agent: Sure, should I be using a CohortStartDate or CohortEndDate?
User: Actually, the right way is to look for the correct UTM parameters in the LastSubmissionURL. The student must also have ActiveStatus listed as true.

_Data Preview_
For example purposes, suppose a URL in the LastSubmissionURL column has the form:
'https://www.codepath.com/assignments?cohort=January&track=webdev&due_date=2025-01-19'

_Output_
```json
{{
  "thought": "We should revise the cohort filter to use the LastSubmissionURL column instead of the CohortStartDate column. I can also simplify the formula by collapsing the MAU variable.",
  "formula": {{
    "name": "Monthly Active Users", "verified": false, "relation": "and",
    "variables": [
      {{"name": "unique_users", "ver": true, "agg": "count", "tab": "StudentProgress", "col": "LearnerUUID"}},
      {{"name": "cohort_filter", "ver": false, "agg": "equals", "tab": "StudentProgress", "col": "LastSubmissionURL", "row": "cohort=April"}},
      {{"name": "active_filter", "ver": false, "agg": "equals", "tab": "StudentProgress", "col": "ActiveStatus", "row": true}}
    ]
  }}
}}
```

_Partial Formula_
{{
  "name": "Customer Acquisition Cost", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "CAC", "verified": false, "relation": "divide",
      "variables": [
        {{"name": "acquisition_cost", "agg": "sum", "tab": "MarketingCampaigns", "col": "BudgetSpent"}},
        {{"name": "new_students", "verified": false, "relation": "placeholder", "variables": []}}
      ]
    }}, {{
      "name": "time period", "agg": "greater_than", "tab": "MarketingCampaigns", "col": "EnrollmentDate", "row": "1 year ago"
    }}
  ]
}}

_Conversation History_
User: Not exactly, I want to know the channels with the highest ad spend in terms of customer acquisition.
Agent: Just to be clear, you want to calculate customer acquisition cost (CAC), where the customer is a student and an acquisition is a student joining a cohort. Does that sound right?
User: Yea, that sounds right. Make sure you're using the budget spent, not the budget allocated for acquisition cost.
Agent: Ok, I will use the BudgetSpent column for acquisition cost. I'm not seeing a column to count the number of students though, what should I use here?
User: Oh you're right, we need to figure out how to join with the StudentProgress table first. I'm not even sure if that's possible.

_Output_
```json
{{
  "thought": "We cannot calculate CAC without joining the tables first, so we should wrap up the metric calculation for now and come back to it later.",
  "formula": {{"name": "N/A", "relation": "placeholder", "variables": []}}
}}
```

## Scenario 3
* Tables: tbl_customer_master; mkt_campaigns_v2; inv_items_current; promo_discount_rules
* Columns: cust_id, user_uuid, f_name, l_name, customer_email, registration_ts, loyalty_pts_current, loyalty_pts_lifetime, loyalty_tier, shipping_addr, billing_addr, is_deleted, newsletter_flag, last_modified_dt in tbl_customer_master;
campaign_id, campaign_code, promo_name, dt_start, dt_end, segment_code, channel_type, budget_allocated, budget_spent, response_pct, asset_url, is_active, created_by, modified_by, created_dt, modified_dt in mkt_campaigns_v2;
item_sk, item_code, sku, cat_id, cat_name, sub_cat_id, price_msrp, price_current, qty_on_hand, qty_allocated, qty_backordered, supplier_id, supplier_name, date_added, last_restock_dt, reorder_point, is_active, discontinued_flag in inv_items_current;
promo_sk, legacy_promo_id, promo_desc, valid_from, valid_to, discount_pct, discount_amt, min_purchase_amt, excluded_items, coupon_code, times_used, max_uses, single_use_flag, stackable_flag, created_ts, modified_ts in promo_discount_rules

_Partial Formula_
{{
  "name": "Return on Ad Spend", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "All_ROAS", "verified": true, "relation": "divide",
      "variables": [
        {{
          "name": "total return", "verified": true, "relation": "multiply",
          "variables": [
            {{"name": "current_price", "ver": true, "agg": "all", "tab": "inv_items_current", "col": "price_current"}},
            {{"name": "current_quantity", "ver": true, "agg": "all", "tab": "inv_items_current", "col": "qty_on_hand"}}
          ]
        }}, {{
          "name": "ad spend", "verified": true, "relation": "add",
          "variables": [
            {{"name": "discount_amount", "ver": true,  "agg": "sum", "tab": "mkt_campaigns_v2", "col": "discount_amt"}},
            {{"name": "budget_spent", "ver": true, "agg": "sum", "tab": "mkt_campaigns_v2", "col": "budget_spent"}},
          ]
        }}
      ]
    }}, {{
      "name": "recent_promos", "verified": false, "relation": "placeholder", "variables": []
    }}
  ]
}}

_Conversation History_
User: Actually, total ad spend includes discount amount in addition to budget spent.
Agent: It seems that the promo_name from mkt_campaigns_v2 does not line up with the promo_sk from promo_discount_rules, any ideas on how to join the two tables together?
User: Same as before, if it's running at the same time, they are the same promotion.
Agent: Is the date added column from inv_items_current the right one to use for time, or should I use the valid_from and valid_to columns?
User: I think the valid from and to are more accurate. Anything from March to June should be fine.

_Output_
```json
{{
  "thought": "The correct method to filter for recent promos is to use the valid_from and valid_to columns to specify the date range.",
  "formula": {{
    "name": "Return on Ad Spend", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "All_ROAS", "verified": true, "relation": "divide",
        "variables": [
          {{
            "name": "total return", "verified": true, "relation": "multiply",
            "variables": [
              {{"name": "current_price", "agg": "all", "tab": "inv_items_current", "col": "price_current", "ver": true}},
              {{"name": "current_quantity", "agg": "all", "tab": "inv_items_current", "col": "qty_on_hand", "ver": true}}
            ]
          }}, {{
            "name": "ad spend", "verified": true, "relation": "add",
            "variables": [
              {{"name": "discount_amount", "agg": "sum", "tab": "mkt_campaigns_v2", "col": "discount_amt", "ver": true}},
              {{"name": "budget_spent", "agg": "sum", "tab": "mkt_campaigns_v2", "col": "budget_spent", "ver": true}}
            ]
          }}
        ]
      }}, {{
        "name": "recent_promos", "verified": false, "relation": "and",
        "variables": [
          {{"name": "after_march", "agg": "greater_than", "tab": "promo_discount_rules", "col": "valid_from", "row": "March", "ver": false}},
          {{"name": "before_june", "agg": "less_than", "tab": "promo_discount_rules", "col": "valid_to", "row": "June", "ver": false}}
        ]
      }}
    ]
  }}
}}
```

* Tables: tbl_customer_master; mkt_campaigns_v2; inv_items_current; promo_discount_rules
* Columns: cust_id, user_uuid, f_name, l_name, customer_email, registration_ts, loyalty_pts_current, loyalty_pts_lifetime, loyalty_tier, shipping_addr, billing_addr, is_deleted, newsletter_flag, last_modified_dt in tbl_customer_master;
campaign_id, campaign_code, promo_name, dt_start, dt_end, segment_code, channel_type, budget_allocated, budget_spent, response_pct, asset_url, is_active, created_by, modified_by, created_dt, modified_dt in mkt_campaigns_v2;
item_sk, item_code, sku, cat_id, cat_name, sub_cat_id, price_msrp, price_current, qty_on_hand, qty_allocated, qty_backordered, supplier_id, supplier_name, date_added, last_restock_dt, reorder_point, is_active, discontinued_flag in inv_items_current;
promo_sk, legacy_promo_id, promo_desc, valid_from, valid_to, discount_pct, discount_amt, min_purchase_amt, excluded_items, coupon_code, times_used, max_uses, single_use_flag, stackable_flag, created_ts, modified_ts in promo_discount_rules

_Partial Formula_
{{
  "name": "Targeted Promo Usage", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "Promotion Usage Rate", "verified": true, "relation": "divide",
      "variables": [
        {{"name": "promo_used", "agg": "greater_than", "tab": "promo_discount_rules", "col": "times_used", "row": 0, "ver": true}},
        {{"name": "promo_offered", "agg": "count", "tab": "promo_discount_rules", "col": "promo_sk", "ver": true}}
      ]
    }}, {{
      "name": "time_frame", "verified": false, "relation": "placeholder", "variables": []
    }}, {{
      "name": "category_filter", "verified": false, "relation": "or",
      "variables": [
        {{"name": "home_appliance_filter", "agg": "equals", "tab": "promo_discount_rules", "col": "excluded_items", "row": "home appliances", "ver": false}},
        {{"name": "kitchenware_filter", "agg": "equals", "tab": "promo_discount_rules", "col": "excluded_items", "row": "kitchenware", "ver": false}}
      ]
    }}
  ]
}}

_Conversation History_
User: Do you think people have been using our promo codes at all?
Agent: That's an interesting question. I can count the number of people who used the promo code by looking at the times_used column in the promo_discount_rules table. How does that sound?
User: Yes, let's focus on categories of either 'home appliances' or 'kitchenware'.
Agent: You got it, I am planning to use the excluded_items column to filter for those categories. Is that correct? Also, is there a particular time frame you want to focus on?
User: The categories are actally based on the cat_name. 

_Output_
```json
{{
  "thought": "The user has hinted that 'cat_name' is actually short for 'category name'. The time frame variable was not discussed, so we keep that part the same as before.",
  "formula": {{
    "name": "Targeted Promo Usage", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "Promotion Usage Rate", "verified": true, "relation": "divide",
        "variables": [
          {{"name": "promo_used", "agg": "greater_than", "tab": "promo_discount_rules", "col": "times_used", "row": 0, "ver": true}},
          {{"name": "promo_offered", "agg": "count", "tab": "promo_discount_rules", "col": "promo_sk", "ver": true}}
        ]
      }}, {{
        "name": "time_frame", "verified": false, "relation": "placeholder", "variables": []}
      }}, {{
        "name": "category_filter", "verified": false, "relation": "or",
        "variables": [
          {{"name": "home_appliance_filter", "agg": "equals", "tab": "inv_items_current", "col": "cat_name", "row": "home appliances", "ver": false}},
          {{"name": "kitchenware_filter", "agg": "equals", "tab": "inv_items_current", "col": "cat_name", "row": "kitchenware", "ver": false}}
        ]
      }}
    ]
  }}
}}
```

## Scenario 4
* Tables: booking_requests_v3; discount_programs_active; assistance_tickets_main
* Columns: booking_ref_id, pax_uuid, caregiver_uuid, journey_start_ts, journey_end_ts, payment_amt, pickup_addr, pickup_access_notes, mobility_equipment, booking_status_cd, payment_status_cd, special_instructions_txt in booking_requests_v3;
promo_sk, program_name, partner_org_id, discount_pct, eligible_vehicle_types, requires_medical_docs, docs_required_txt, total_claims, valid_from_dt, valid_to_dt in discount_programs_active;
ticket_sk, customer_book_id, caregiver_uuid, created_ts, severity_level, issue_category_main, medical_emergency_flag, issue_details_encrypted, assigned_agent_id, resolution_code, satisfaction_rating, compliance_review_flag in assistance_tickets_main

_Partial Formula_
{{
  "name": "Repeat Ticket Issuers", "verified": false, "relation": "greater_than",
  "variables": [
    {{"name": "tickets_per_customer", "ver": false, "agg": "count", "tab": "assistance_tickets_main", "col": "customer_book_id", "row": "group by customer_book_id"}},
    {{"name": "repeat_threshold", "ver": false, "agg": "constant", "tab": "N/A", "col": "N/A", "row": "1"}}
  ]
}}

_Conversation History_
User: Yea, sure why not. Only people who have filed tickets more than once.
Agent: Got it. To calculate that, I will count the number of tickets per customer and group by customer_book_id. Then, I will filter for customers who have more than one ticket. Does that work?
User: Ok, and which segmentation are you thinking of handling first?
Agent: I was thinking of starting with payment amounts. Should I go for pick-up locations or customer satisfaction instead?
User: Payment amounts is fine. Let's only look at folks who have spent more than $100.

_Output_
```json
{{
  "thought": "Segmentation is done after the main metric is calculated, but the user has specified a filtering condition before segmentation, so we should add it to the formula.",
  "formula": {{
    "name": "Repeat Ticket Issuers", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "RTI", "verified": false, "relation": "greater_than", "relation": "greater_than",
        "variables": [
          {{"name": "tickets_per_customer", "ver": false, "agg": "count", "tab": "assistance_tickets_main", "col": "customer_book_id", "row": "group by customer_book_id"}},
          {{"name": "repeat_threshold", "ver": false, "agg": "constant", "tab": "N/A", "col": "N/A", "row": "1"}}
        ]
      }}, {{
        "name": "payment_filter", "ver": false, "agg": "greater_than", "tab": "booking_requests_v3", "col": "payment_amt", "row": 100}
      }}
    ]
  }}
}}
```

_Partial Formula_
{{
  "name": "Conversion Rate", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "CVR", "verified": false, "relation": "divide",
      "variables": [
        {{"name": "conversion", "ver": false, "agg": "filled", "tab": "booking_requests_v3", "col": "payment_status_cd"}},
        {{"name": "possible_conversions", "verified": false, "relation": "placeholder", "variables": []}}
      ]
    }}, {{
      "name": "time_frame", "ver": false, "agg": "greater_than", "tab": "assistance_tickets_main", "col": "created_ts", "row": "7 days ago"
    }}
  ]
}}

_Conversation History_
User: What about conversion rate then? Can we look at that for the past week?
Agent: Sure, I can calculate the conversion rate by checking whether payment status is filled, but I'm not sure how to count the possible conversions in the denominator. What should I use?
User: Not really, conversions are when the booking status is paid and the total possible is any non-null booking status. Also, journey timestamp is the right place to look for time.

_Output_
```json
{{
  "thought": "The conversion and total possible conditions are based on the booking status column, so we should update the formula accordingly.",
  "formula": {{
    "name": "Conversion Rate", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "CVR", "verified": false, "relation": "divide",
        "variables": [
          {{"name": "conversion", "ver": false, "agg": "equals", "tab": "booking_requests_v3", "col": "booking_status_cd", "row": "paid"}},
          {{"name": "total_possible", "ver": false, "agg": "filled", "tab": "booking_requests_v3", "col": "booking_status_cd"}}
        ]
      }}, {{
        "name": "time_frame", "ver": false, "agg": "greater_than", "tab": "booking_requests_v3", "col": "journey_start_ts", "row": "7 days ago"
      }}
    ]
  }}
}}
```
---
Now it's your turn! Please think carefully about how to fill in the missing variables from the placeholder.
Then observe the preview of the relevant data to decide how to construct the remaining variables to complete the expression.

_Partial Formula_
{formula}

_Conversation History_
{history}

_Data Preview_
{preview}

_Output_
"""

variable_disagreement_prompt = """Based on the conversation history, we have generated some candidate formulas for calculating the {metric} metric.
However, there is disagreement about which formulation is correct, so we now need your help to determine the right one.
For some background, a Formula is a tree of Expression objects, where the root node is the final Expression representing the metric.
The internal nodes are called variables that combine or compare other Expressions, while the leaf nodes are Clauses that reference specific columns in a table.
Each Expression is composed of a name, variables, and relation, which conveys the relationship between those variables. The variables are either other Expressions or Clauses.

Valid relations include: add (+), subtract (-), multiply (*), divide (/), exponent (^), and (&), or (|), less_than (<), greater_than (>), equals (=), conditional (?).
Typically speaking, 'add', 'subtract', 'multiply', 'divide', 'exponent', and 'conditional' represent mathematical operations that return numeric values.
On the other hand, 'and', 'or', 'less_than', 'greater_than', and 'equals' represent logical operations that return boolean values.
In addition, 'conditional' is also unique as the only relation that requires three variables instead of two: `variable1 ? variable2 : variable3`.
There is one last relationship called 'placeholder', which should be used if and only if you are unsure about how to properly structure the expression at that level.
If both candidate formulas include the 'placeholder' relation, then we should heed this warning and continue to use 'placeholder' rather than making assumptions.

Clauses are composed of a name, aggregation, table, row, and column. They represent the data that is grounded to specific columns in a table. Valid aggregations are:
  * sum - total of all non-null values
  * count - count the number of unique values
  * average - take the mean of all non-null values
  * top - the top N values, where N > 1. If N == 1, then use 'max' instead.
  * bottom - the bottom N values, where N > 1. If N == 1, then use 'min' instead.
  * min - minimum value in the column, equivalent to 'bottom 1'
  * max - maximum value in the column, equivalent to 'top 1'
  * equals - filter for rows where the column equals a specific value N
  * not - filter for rows where the column does not contain a specific value N
  * less_than - filter for rows where the column is less than a specific value N
  * greater_than - filter for rows where the column is greater than a specific value N
  * empty - simply decides whether the row is null
  * filled - opposite of empty, decides whether the row is not null
  * constant - a constant value of N that is not derived from the data
  * all - no aggregation, just keep all the raw values in the column

For the aggregations of [top, bottom, greater_than, less_than, not, equals, constant], the 'row' field is used to specify the value of N.
Rates (ie. CTR, CVR, etc.) are expected to range from 0 to 1, and should NOT be multiplied by 100 to convert into a percentage.
When choosing a name for the clause, pick something short and descriptive that contains at most four parts, connected by underscores.
Remember the 'add' relation is not the same as the 'sum' aggregation. The former is used in Expressions, while the latter is used in Clauses.

Please start by carefully reviewing the candidate formulas and the conversation history to help you understand the situation.
Then, examine where the formulas differ, consider whether each difference has merit, and decide how they might be combined to arrive at the correct answer.
Lean heavily on the preview of the relevant columns to help you make your decision since the table content is the most important factor in constructing Clauses.
Complex metrics may contain multiple levels of sub-Expressions before reaching the Clauses, so it is important to consider the entire tree structure.
Your entire response should be in well-formatted JSON starting with keys for thought (string) and formula (dict).
The formula dict should be a tree of expression objects, expanded to include all child expressions and clauses. There should be no further text or explanations after the JSON output.

For example,
---
_Data Preview_
| outreach_id | email_title              | send_time           | open_time           |
|-------------|--------------------------|---------------------|---------------------|
| 001783421   | Q4 Special Offer         | 2024-01-15 09:23:41 | 2024-01-15 14:52:18 |
| 001926688   | Your Monthly Update      | 2024-01-12 15:30:00 | <N/A>               |
| 001892374   | New Product Launch       | 2024-01-14 08:00:00 | 2024-01-14 08:12:33 |
| 001443891   | Exclusive Member Discount| 2024-01-13 11:45:22 | 2024-01-13 18:20:05 |
| 001976532   | Weekend Flash Sale       | 2024-01-15 07:00:00 | <N/A>               |
| 001957263   | Product Feedback Request | 2024-01-14 13:15:00 | 2024-01-14 15:45:12 |
| 001822869   | Partnership Opportunity  | 2024-01-13 16:20:00 | 2024-01-14 09:30:45 |
| 001926795   | Limited Time Offer       | 2024-01-15 10:00:00 | 2024-01-15 10:05:33 |

_Conversation History_
User: How about our email performance?
Agent: What specifically would you like to know?
User: Is the open rate increasing?
In this example, we are trying to calculate Email Open Rate (Open).

_Candidate Formulas_
{{
  "name": "Email Open Rate",
  "relation": "divide",
  "variables": [
    {{
      "name": "emails_sent",
      "relation": "and",
      "variables": [
        {{"name": "outreach", "agg": "count", "tab": "Outreach Revised", "col": "outreach_id"}},
        {{"name": "sent", "agg": "filled", "tab": "Outreach Revised", "col": "send_time"}}
      ]
    }}, {{
      "name": "emails_opened",
      "relation": "and",
      "variables": [
        {{"name": "outreach", "agg": "count", "tab": "Outreach Revised", "col": "outreach_id"}},
        {{"name": "opened", "agg": "filled", "tab": "Outreach Revised", "col": "open_time"}}
      ]
    }}
  ]
}}

{{
  "name": "Email Open Rate",
  "relation": "divide",
  "variables": [
    {{
      "name": "emails_sent",
      "relation": "and",
      "variables": [
        {{"name": "email_outreach", "agg": "count", "tab": "Outreach Revised", "col": "outreach_id"}},
        {{"name": "was_sent", "agg": "filled", "tab": "Outreach Revised", "col": "send_time"}}
      ]
    }}, {{
      "name": "emails_opened",
      "relation": "and",
      "variables": [
        {{"name": "email_outreach", "agg": "count", "tab": "Outreach Revised", "col": "outreach_id"}},
        {{"name": "was_opened", "agg": "filled", "tab": "Outreach Revised", "col": "open_time"}}
      ]
    }}
  ]
}}

_Output_
```json
{{
  "thought": "The only difference is the name of the variables. We will go with the second version since we prefer names that are composed of 2 to 4 parts.",
  "formula": {{
    "name": "Email Open Rate",
    "relation": "divide",
    "variables": [
      {{
        "name": "emails_sent",
        "relation": "and",
        "variables": [
          {{"name": "email_outreach", "agg": "count", "tab": "Outreach Revised", "col": "outreach_id"}},
          {{"name": "was_sent", "agg": "filled", "tab": "Outreach Revised", "col": "send_time"}}
        ]
      }}, {{
        "name": "emails_opened",
        "relation": "and",
        "variables": [
          {{"name": "email_outreach", "agg": "count", "tab": "Outreach Revised", "col": "outreach_id"}},
          {{"name": "was_opened", "agg": "filled", "tab": "Outreach Revised", "col": "open_time"}}
        ]
      }}
    ]
  }}
}}
```

_Data Preview_
| RecurringPayment | OneTimePayment | GoogleAdSpend | FacebookAdSpend | InstagramSpend |
|------------------|----------------|---------------|-----------------|----------------|
| 299.99           | 0.00           | 45.23         | 32.15           | 28.90          |
| 0.00             | 849.00         | 102.45        | 0.00            | 65.30          |
| 149.99           | 199.99         | 38.50         | 42.75           | 0.00           |
| 0.00             | 0.00           | 25.80         | 18.90           | 22.45          |
| 499.99           | 149.99         | 85.60         | 55.20           | 48.75          |
| 199.99           | 0.00           | 0.00          | 28.45           | 35.20          |
| 0.00             | 399.99         | 62.30         | 45.80           | 0.00           |
| 749.99           | 299.99         | 125.40        | 68.90           | 52.30          |

_Conversation History_
User: yes, go for it.
Agent: Great, I have joined the data from all the platforms together within the ppc_transactions table.
User: So what is the total return on our ad spend for those three channels?
In this example, we are trying to calculate Return on Ad Spend (ROAS).

_Candidate Formulas_
{{
  "name": "Return on Ad Spend",
  "relation": "divide",
  "variables": [
    {{
      "name": "total spend",
      "relation": "add",
      "variables": [
        {{"name": "google", "agg": "sum", "tab": "ppc_transactions", "col": "GoogleAdSpend"}},
        {{"name": "facebook", "agg": "sum", "tab": "ppc_transactions", "col": "FacebookAdSpend"}},
        {{"name": "instagram", "agg": "sum", "tab": "ppc_transactions", "col": "InstagramSpend"}}
      ]
    }}, {{
      "name": "total return",
      "relation": "add",
      "variables": [
        {{"name": "subscriptions", "agg": "sum", "tab": "checkout_amplitude", "col": "RecurringPayment"}},
        {{"name": "one_time_purchases", "agg": "sum", "tab": "checkout_amplitude", "col": "OneTimePayment"}}
      ]
    }} 
  ]
}}

{{
  "name": "Return on Ad Spend",
  "relation": "divide",
  "variables": [
    {{
      "name": "total return",
      "relation": "add",
      "variables": [
        {{"name": "one_time_purchases", "agg": "sum", "tab": "checkout_amplitude", "col": "OneTimePayment"}},
        {{"name": "subscriptions", "agg": "sum", "tab": "checkout_amplitude", "col": "RecurringPayment"}}
      ]
    }}, {{
      "name": "total spend",
      "relation": "add",
      "variables": [
        {{"name": "facebook", "agg": "sum", "tab": "ppc_transactions", "col": "FacebookAdSpend"}},
        {{"name": "google", "agg": "sum", "tab": "ppc_transactions", "col": "GoogleAdSpend"}},
        {{"name": "instagram", "agg": "sum", "tab": "ppc_transactions", "col": "InstagramSpend"}}
      ]
    }}
  ]
}}

_Output_
```json
{{
  "thought": "The order of the variables is different, which does not matter for the 'add' relation, but matters a lot for 'divide'. We need to put 'return' before 'spend' to calculate ROAS.",
  "formula": {{
    "name": "Return on Ad Spend",
    "relation": "divide",
    "variables": [
      {{
        "name": "total return",
        "relation": "add",
        "variables": [
          {{"name": "subscriptions", "agg": "sum", "tab": "checkout_amplitude", "col": "RecurringPayment"}},
          {{"name": "one_time_purchases", "agg": "sum", "tab": "checkout_amplitude", "col": "OneTimePayment"}}
        ]
      }}, {{
        "name": "total spend",
        "relation": "add",
        "variables": [
          {{"name": "google", "agg": "sum", "tab": "ppc_transactions", "col": "GoogleAdSpend"}},
          {{"name": "facebook", "agg": "sum", "tab": "ppc_transactions", "col": "FacebookAdSpend"}},
          {{"name": "instagram", "agg": "sum", "tab": "ppc_transactions", "col": "InstagramSpend"}}
        ]
      }}
    ]
  }}
}}
```

_Data Preview_
| generated_revenue | final_expenses | renewal_revenue | renewal_discount |
|-------------------|----------------|-----------------|------------------|
| 845.99            | 234.50         | 199.99          | 0.00             |
| 1250.00           | 378.25         | 0.00            | 0.00             |
| 650.75            | 198.30         | 299.99          | 49.99            |
| 925.50            | 285.75         | 149.99          | 25.00            |
| 1875.25           | 562.80         | 0.00            | 0.00             |
| 750.00            | 225.40         | 399.99          | 75.00            |
| 1100.25           | 345.60         | 199.99          | 0.00             |
| 525.75            | 168.90         | 299.99          | 50.00            |

_Conversation History_
User: Let's look at just the month of April.
Agent: The revenue in April was approximately $272,100
User: So what is our net profit at that time?
In this example, we are trying to calculate Net Profit (Profit).

_Candidate Formulas_
{{
  "name": "Net Profit",
  "relation": "and"
  "variables": [
    {{
      "name": "Profit",
      "relation": "subtract",
      "variables": [
        {{
          "name": "total_revenue",
          "relation": "add",
          "variables": [
            {{"name": "sum_generated_revenue", "agg": "sum", "tab": "Q4Bookings", "col": "generated_revenue"}},
            {{"name": "sum_renewal_revenue", "agg": "sum", "tab": "Q4Bookings", "col": "renewal_revenue"}}
          ]
        }}, {{
          "name": "expenses", "agg": "sum", "tab": "Q4Bookings", "col": "final_expenses"
        }}
      ]
    }}, {{
      "name": "In_April", "agg": "equals", "tab": "Q4Bookings", "col": "start_date", "row": "April"
    }}
  ]
}}

{{
  "name": "Net Profit",
  "relation": "and"
  "variables": [
    {{
      "name": "Profit",
      "relation": "subtract",
      "variables": [
        {{"name": "revenue", "agg": "sum", "tab": "Q4Bookings", "col": "generated_revenue"}},
        {{"name": "expenses", "agg": "sum", "tab": "Q4Bookings", "col": "final_expenses"}},
      ]
    }}, {{
      "name": "In_April", "agg": "equals", "tab": "Q4Bookings", "col": "start_date", "row": "April"
    }}
  ]
}}

_Output_
```json
{{
  "thought": "The discrepancy comes from choosing whether to include renewal_revenue. We will go with generated_revenue alone because it shares the same table as final_expenses, but we should clarify with the user.",
  "formula": {{
    "name": "Net Profit",
    "relation": "and"
    "variables": [
      {{
        "name": "Profit",
        "relation": "subtract",
        "variables": [
          {{"name": "revenue", "agg": "sum", "tab": "Q4Bookings", "col": "generated_revenue"}},
          {{"name": "expenses", "agg": "sum", "tab": "Q4Bookings", "col": "final_expenses"}},
        ]
      }}, {{
        "name": "In_April", "agg": "equals", "tab": "Q4Bookings", "col": "start_date", "row": "April"
      }}
    ]
  }}
}}
```

_Data Preview_
| account_name           | pickup_location   | vendor_address                          | renewal_revenue | product_name             |
|------------------------|-------------------|-----------------------------------------|-----------------|--------------------------|
| Quantum Dynamics       | Seattle, WA       | 742 Pike St, Seattle, WA 98101          | 2499.99         | Enterprise Fleet Package |
| Blue Marble Industries | Portland, OR      | 1845 SW Morrison St, Portland, OR 97205 | 899.99          | Standard Business Plan   |
| Helix Solutions Group  | Bellevue, WA      | 15700 NE 8th St, Bellevue, WA 98008     | 1799.99         | Premium Fleet Service    |
| Apex Global Ltd        | San Diego, CA     | 3425 5th Ave, San Diego, CA 92103       | 349.99          | Basic Business Plan      |
| Starlight Enterprises  | San Francisco, CA | 768 Market St, SF, CA 94102             | 2899.99         | Enterprise Plus Package  |
| Silverstream Corp      | Eugene, OR        | 255 High St, Eugene, OR 97401           | 599.99          | Standard Fleet Plan      |
| Iris Technologies      | Oakland, CA       | 492 Grand Ave, Oakland, CA 94610        | 1299.99         | Premium Business Package |
| Atlas Innovation Labs  | Seattle, WA       | 535 Westlake Ave N, Seattle, WA 98109   | 1999.99         | Enterprise Service Plan  |

_Conversation History_
User: Who are our high value accounts within California, Oregon, and Washington?
Agent: How do you define high value accounts?
User: Those with the highest monthly renewal rates.
In this example, we are calculating High Value Accounts (Whale).

_Candidate Formulas_
{{
  "name": "High Value Accounts",
  "relation": "and",
  "variables": [
    {{
      "name": "top_accounts",
      "relation": "and",
      "variables": [
        {{"name": "top_accounts", "agg": "top", "tab": "MonthlyRenewals", "col": "renewal_revenue", "row": "7"}},
        {{"name": "account_identifier", "agg": "all", "tab": "MonthlyRenewals", "col": "account_name"}}
      ]
    }}, {{
      "name": "west_coast", 
      "relation": "or",
      "variables": [
        {{"name": "from_CA", "agg": "equals", "tab": "Q4Bookings", "col": "pickup_location", "row": "California"}},
        {{"name": "from_OR", "agg": "equals", "tab": "Q4Bookings", "col": "pickup_location", "row": "Oregon"}},
        {{"name": "from WA", "agg": "equals", "tab": "Q4Bookings", "col": "pickup_location", "row": "Washington"}}
      ]
    }}
  ]
}}

{{
  "name": "High Value Accounts",
  "relation": "and",
  "variables": [
    {{
      "name": "top_accounts",
      "relation": "and",
      "variables": [
        {{"name": "top_accounts", "agg": "top", "tab": "MonthlyRenewals", "col": "renewal_revenue", "row": "7"}},
        {{"name": "account_identifier", "agg": "all", "tab": "MonthlyRenewals", "col": "account_name"}}
      ]
    }}, {{
      "name": "west_coast_region", 
      "relation": "and",
      "variables": [
        {{"name": "from_CA", "agg": "equals", "tab": "Q4Bookings", "col": "pickup_location", "row": "California"}},
        {{"name": "from_OR", "agg": "equals", "tab": "Q4Bookings", "col": "pickup_location", "row": "Oregon"}},
        {{"name": "from WA", "agg": "equals", "tab": "Q4Bookings", "col": "pickup_location", "row": "Washington"}}
      ]
    }}
  ]
}}

_Output_
```json
{{
  "thought": "The first version uses the 'or' relation to filter for the west coast, while the second version uses 'and'. Since an account can only be from one state, we should go with the first version.",
  "formula": {{
    "name": "High Value Accounts",
    "relation": "and",
    "variables": [
      {{
        "name": "top_accounts",
        "relation": "and",
        "variables": [
          {{"name": "top_accounts", "agg": "top", "tab": "MonthlyRenewals", "col": "renewal_revenue", "row": "7"}},
          {{"name": "account_identifier", "agg": "all", "tab": "MonthlyRenewals", "col": "account_name"}}
        ]
      }}, {{
        "name": "west_coast", 
        "relation": "or",
        "variables": [
          {{"name": "from_CA", "agg": "equals", "tab": "Q4Bookings", "col": "pickup_location", "row": "California"}},
          {{"name": "from_OR", "agg": "equals", "tab": "Q4Bookings", "col": "pickup_location", "row": "Oregon"}},
          {{"name": "from WA", "agg": "equals", "tab": "Q4Bookings", "col": "pickup_location", "row": "Washington"}}
        ]
      }}
    ]
  }}
}}
```

_Data Preview_
| subscription_id | user_id    | plan_name    | sub_timestamp       | renewal_notice | payment_status |
|-----------------|------------|--------------|---------------------|----------------|----------------|
| *********       | 5023-5570  | Premium Plus | 2024-01-15 14:30:00 | true           | approved       |
| *********       | 5439-3963  | Basic Plan   | 2024-01-12 09:15:00 | true           | pending        |
| *********       | 5551-0931  | Enterprise   | 2024-01-14 11:20:00 | false          | N/A            |
| 812434981       | 5971-9203  | Standard     | 2024-01-13 16:45:00 | true           | approved       |
| 812488726       | 5145-6780  | Premium Plus | 2024-01-15 08:00:00 | true           | rejected       |
| 812423467       | 5223-9864  | Basic Plan   | 2024-01-14 13:30:00 | true           | approved       |
| 812445632       | 5323-4890  | Enterprise   | 2024-01-13 10:00:00 | false          | N/A            |
| 812467890       | 5261-7556  | Standard     | 2024-01-15 15:15:00 | true           | approved       |

_Conversation History_
User: What percentage of them are still sticking around after the first month?
Agent: How do you define sticking around?
User: Those who received a renewal notice, submitted their payment, and gotten it approved.
In this example, we are calculating Retention Rate (Retain).

_Candidate Formulas_
{{
  "name": "Retention Rate",
  "relation": "divide",
  "variables": [
    {{
      "name": "Active Users",
      "relation": "and",
      "variables": [
        {{"name": "sub_timestamp", "agg": "greater_than", "tab": "subscriptions", "col": "sub_timestamp", "row": "06-01-2024"}},
        {{"name": "payment_status", "agg": "equals", "tab": "subscriptions", "col": "payment_status", "row": "paid"}},
        {{"name": "renewal_notice", "agg": "equals", "tab": "subscriptions", "col": "renewal_notice", "row": true}}
      ]
    }},
    {{
      "name": "Total Users",
      "relation": "or",
      "variables": [
        {{"name": "subscription_id", "agg": "count", "tab": "subscriptions", "col": "subscription_id"}},
        {{"name": "user_id", "agg": "count", "tab": "subscriptions", "col": "user_id"}}
      ]
    }}
  ]
}}

{{
  "name": "Retention Rate",
  "relation": "divide",
  "variables": [
    {{
      "name": "Active Users",
      "relation": "and",
      "variables": [
        {{"name": "sub_timestamp", "agg": "greater_than", "tab": "subscriptions", "col": "sub_timestamp", "row": "06-01-2024"}},
        {{"name": "payment_status", "agg": "equals", "tab": "subscriptions", "col": "payment_status", "row": "approved"}},
        {{"name": "renewal_notice", "agg": "equals", "tab": "subscriptions", "col": "renewal_notice", "row": "true"}}
      ]
    }},
    {{
      "name": "Total Users",
      "relation": "or",
      "variables": [
        {{"name": "subscription_id", "agg": "count", "tab": "subscriptions", "col": "subscription_id"}},
        {{"name": "user_id", "agg": "count", "tab": "subscriptions", "col": "user_id"}}
      ]
    }}
  ]
}}

_Output_
```json
{{
  "thought": "The first version uses 'paid' for payment status, while the second uses 'approved'. We can tell based on the data preview that 'approved' is the correct value. Separately, the first version treats the renewal_notice as a boolean rather than a string, which is the correct interpretation.",
  "formula": {{
    "name": "Retention Rate",
    "relation": "divide",
    "variables": [
      {{
        "name": "Active Users",
        "relation": "and",
        "variables": [
          {{"name": "sub_timestamp", "agg": "greater_than", "tab": "subscriptions", "col": "sub_timestamp", "row": "06-01-2024"}},
          {{"name": "payment_status", "agg": "equals", "tab": "subscriptions", "col": "payment_status", "row": "approved"}},
          {{"name": "renewal_notice", "agg": "equals", "tab": "subscriptions", "col": "renewal_notice", "row": true}}
        ]
      }},
      {{
        "name": "Total Users",
        "relation": "or",
        "variables": [
          {{"name": "subscription_id", "agg": "count", "tab": "subscriptions", "col": "subscription_id"}},
          {{"name": "user_id", "agg": "count", "tab": "subscriptions", "col": "user_id"}}
        ]
      }}
    ]
  }}
}}
```

_Data Preview_
| user_id   | billing_cycle | sub_timestamp       | activity_type | timestamp           | 
|-----------|---------------|---------------------|---------------|---------------------|
| 5781-9232 | monthly       | 2024-06-05 09:30:00 | purchase      | 2024-06-15 14:00:00 |
| 5261-7506 | quarterly     | 2024-06-12 13:15:00 | purchase      | 2024-06-12 16:30:00 |
| 5923-4051 | monthly       | 2024-06-08 11:45:00 | refund        | 2024-06-22 10:00:00 |
| 5556-1352 | monthly       | 2024-06-15 15:20:00 | purchase      | 2024-06-28 09:15:00 |
| 5234-9364 | quarterly     | 2024-06-03 10:00:00 | cancellation  | 2024-06-18 11:30:00 |
| 5667-1322 | monthly       | 2024-06-20 14:45:00 | refund        | 2024-06-25 15:45:00 |
| 5112-0628 | monthly       | 2024-06-01 08:30:00 | refund        | 2024-06-10 13:00:00 |
| 5889-8235 | quarterly     | 2024-06-18 12:00:00 | purchase      | 2024-06-30 16:15:00 |


_Conversation History_
User: So we want to look at AOV times 120% as the projected future growth. AOV is the total order value divided by its frequency.
Agent: Got it, and how do we break down the total order value?
User: We look at class revenue for our target cohort, basically the last month.
Agent: This cohort is the month of June, correct? Also, how do we break down the class revenue?
User: Yes, that's right. The class revenue is the number of classes purchased multiplied by the cost per class, which we can set as $300.
In this example, we are calculating Customer Lifetime Value (LTV).

_Candidate Formulas_
{{
  "name": "Customer Lifetime Value",
  "relation": "multiply",
  "variables": [
    {{
      "name": "AOV",
      "relation": "divide",
      "variables": [
        {{
          "name": "total_order_value",
          "relation": "and",
          "variables": [
            {{
              "name": "class_revenue",
              "relation": "multiply",
              "variables": [
                {{ "name": "classes_purchased", "agg": "equals", "tab": "user_activity", "col": "activity_type", "row": "purchase" }},
                {{ "name": "cost_per_class", "agg": "constant", "tab": "N/A", "col": "N/A", "row": "300" }}
              ]
            }},
            {{ "name": "recent_purchases", "agg": "greater_than", "tab": "orders", "col": "created_at", "row": "06-01-2024" }}
          ]
        }},
        {{ "name": "order_frequency", "relation": "placeholder", "variables": [] }}
      ]
    }}, {{
      "name": "future_growth", "agg": "constant", "tab": "N/A", "col": "N/A", "row": "1.2"
    }}
  ]
}}

{{
  "name": "Customer Lifetime Value",
  "relation": "multiply",
  "variables": [
    {{
      "name": "AOV",
      "relation": "divide",
      "variables": [
        {{
          "name": "total_order_value",
          "relation": "and",
          "variables": [
            {{
              "name": "class_revenue",
              "relation": "and",
              "variables": [
                {{ "name": "class_tuition", "agg": "sum", "tab": "user_activity", "col": "tuition_fees" }},
                {{ "name": "classes_purchased", "agg": "filled", "tab": "user_activity", "col": "activity_type" }}
              ]
            }},
            {{ "name": "recent_purchases", "agg": "greater_than", "tab": "orders", "col": "created_at", "row": "06-01-2024" }}
          ]
        }},
        {{ "name": "order_frequency", "relation": "placeholder", "variables": [] }}
      ]
    }}, {{
      "name": "future_growth", "agg": "constant", "tab": "N/A", "col": "N/A", "row": "1.2"
    }}
  ]
}}

_Output_
```json
{{
  "thought": "The second version calculates class revenue as the sum of tuition fees, while the first version multiplies the number of classes purchased by the cost per class. Both sound correct on the surface, but the first version matches the user's description and the data preview.",
  "formula": {{
    "name": "Customer Lifetime Value",
    "relation": "multiply",
    "variables": [
      {{
        "name": "AOV",
        "relation": "divide",
        "variables": [
          {{
            "name": "total_order_value",
            "relation": "and",
            "variables": [
              {{
                "name": "class_revenue",
                "relation": "multiply",
                "variables": [
                  {{ "name": "classes_purchased", "agg": "equals", "tab": "user_activity", "col": "activity_type", "row": "purchase" }},
                  {{ "name": "cost_per_class", "agg": "constant", "tab": "N/A", "col": "N/A", "row": "300" }}
                ]
              }},
              {{ "name": "recent_purchases", "agg": "greater_than", "tab": "orders", "col": "created_at", "row": "06-01-2024" }}
            ]
          }},
          {{ "name": "order_frequency", "relation": "placeholder", "variables": [] }}
        ]
      }}, {{
        "name": "future_growth", "agg": "constant", "tab": "N/A", "col": "N/A", "row": "1.2"
      }}
    ]
  }}
}}
```
---
Now it's your turn! Please generate your thoughts followed by the revised formula that best captures the user's intent.

_Data Preview_
{preview}

_Conversation History_
{history}

_Candidate Formulas_
{formula1}

{formula2}

_Output_
"""

check_existence_prompt = """The user is trying to find if certain column(s) exist based on some specified attributes.
Based on our initial attempt, we believe relevant columns include{found}

If it is immediately obvious that these are the correct columns, then we are done, so you can output an empty list as the preview.
On the other hand, if there are multiple candidates or if the user's request is ambiguous, then choose the candidate columns to preview.
Previewing a column means sampling a few rows from the table to check if it matches the user's description.

Start by constructing a concise thought concerning the user's request and then generate the potential data to preview as dicts with 'tab' and 'col' keys.
Your entire response should be in well-formatted JSON with keys of thought (string) and preview (list). There should be no further explanations after the JSON output.

For example,
---
_Conversation History_
User: Do we have any data concerning revenue?
Found columns include:
  - package_price column in packages
  - membership_fee column in members

_Example Columns_
member_id, full_name, email_address, phone_number, date_joined, membership_type, membership_fee, expiration_date, packages_bought in members;
package_id, package_name, duration, package_price, num_remaining, discount_applied, included_classes, additional_benefits in packages

_Output_
```json
{{
  "thought": "revenue can be interpreted as the sum of price or the sum of fees, so the situation is unclear",
  "preview": [
    {{ "tab": "packages", "col": "package_price" }},
    {{ "tab": "members", "col": "membership_fee" }}
  ]
}}
```

_Conversation History_
User: Do we have a column that calculates CPCs?
Found columns include the costPerClick column in GoogleAds_Q3.

_Example Columns_
gAd_ID, spendInDollars, campaignInitDate, campaignTermDate, adBounceRate, audienceFocus, adContentCode, costPerClick in GoogleAds_Q3;
orderRef, prodSKU, saleDate, acquisitionCost, buyerID, gAdRef, revenueGenerated, unitsMoved, fulfillmentStatus, customerNotes in SalesRecord_Shopify_0812;
productID, productName, itemCategory, retailPrice, totalCost, stockLevel, productWeight in Product_Details

_Output_
```json
{{
  "thought": "we have already found the column for costPerClick, so no need to preview",
  "preview": []
}}
```

_Conversation History_
User: Do we have a column that calculates CPCs?
Found columns include:
  - spendInDollars column in GoogleAds_Q3
  - clickCount column in GoogleAds_Q3

_Example Columns_
gAd_ID, spendInDollars, campaignInitDate, campaignTermDate, adBounceRate, audienceFocus, adContentCode, clickCount in GoogleAds_Q3;
orderRef, prodSKU, saleDate, acquisitionCost, buyerID, gAdRef, revenueGenerated, unitsMoved, fulfillmentStatus, customerNotes in SalesRecord_Shopify_0812;
productID, productName, itemCategory, retailPrice, totalCost, stockLevel, productWeight in Product_Details

_Output_
```json
{{
  "thought": "there isn't any column referencing CPCs, but there are columns related spend and clicks, which are relevant",
  "preview": [
    {{ "tab": "GoogleAds_Q3", "col": "spendInDollars" }},
    {{ "tab": "GoogleAds_Q3", "col": "clickCount" }}
  ]
}}
```

_Conversation History_
User: Can we take a look at Facebook spend?
Found columns include the FB_Spend column in the channel_costs table.

_Example Columns_
Channel_Results, FB_Spend, FB_ViewCount, IG_Spend, IG_ViewCount, Twitter_Spend, RetweetCount, Pinterest_Spend, PinCount, Snapchat_Spend, SnapCount, TikTok_Spend, SwipeCount in channel_costs;

_Output_
```json
{{
  "thought": "Facebook is often abbreviated as FB, so this is obvious already and does not need a preview",
  "preview": []
}}
```

_Conversation History_
User: Did you see any data regarding AOV?
Found columns include the avg_order_size column in the purchases table.

_Example Columns_
purchase_id, customer_id, product_id, purchase_date, purchase_amount, avg_order_size, num_items, purchase_type in purchases;

_Output_
```json
{{
  "thought": "The table has a column for average order size, but not average order value, so we should preview just to be sure",
  "preview": [
    {{ "tab": "subscriptions", "col": "avg_order_size" }}
  ]
}}
```

_Conversation History_
User: What data do we have about conversions?
Found columns include:
  - sign_ups column in mailchimp_download
  - subscriptions column in sub_aggregated_stripe
  - installs column in app_annie_analytics_Sept

_Example Columns_
email_id, global_user_id, sign_ups, clicks, open_rate, click_rate, bounce_rate, time_on_site in mailchimp_download;
user_id, page_views, subscriptions, actitity_type, activity_cleaned, address, phone number, refund_amount, discount_applied; in sub_aggregated_stripe:
app_guid, device, downloads, installs, uninstalls, num_clicks, time_in_app, time_in_hours in app_annie_analytics_Sept;

_Output_
```json
{{
  "thought": "Conversions can be interpreted as the number of sign-ups, number of subscriptions, or number of installations. The situation is ambiguous.",
  "preview": [
    {{ "tab": "mailchimp_download", "col": "sign_ups" }},
    {{ "tab": "app_annie_analytics_Sept", "col": "installs" }},
    {{ "tab": "sub_aggregated_stripe", "col": "subscriptions" }}
  ]
}}
```

_Conversation History_
User: Do we know how many views we got from our email campaign?
Found columns include the open_rate column in the constant_contact_res table.

_Example Columns_
cc_id, open_rate, email_address, domain, open_timestamp, delivered, downloads, click_rate, username, opt_out in constant_contact_res;

_Output_
```json
{{
  "thought": "We can calculate the number of email opens by multiplying the number of emails successfully delivered by the open rate. This requires calculation though, so we should preview the relevant columns to verify.",
  "preview": [
    {{ "tab": "constant_contact_res", "col": "delivered" }}
    {{ "tab": "constant_contact_res", "col": "open_rate" }},
  ]
}}
```
---
Now it's your turn. Given the conversation history and valid columns, decide which columns (if any) would be useful to preview.
Remember that so far the found columns include{found}
If the answer is already clear, then output an empty list for the preview. In all cases, only choose from valid tables and columns.

_Conversation History_
{history}

_Valid Columns_
{columns}

_Output_
"""

pivot_table_prompt = """Given the conversation history and existing tables, decide whether a pivot table is needed and if so, what an appropriate name for the table should be.
Pivot tables are created by querying the database and saving the results to a new permanent table, rather than displaying as a temporary view.
A query is considered sufficiently complex, and therefore warrants saving, when all of the following conditions are met:
  * it involves grouping by at least one dimension
  * it involves aggregating, filtering, or grouping by at least two additional dimensions
  * the output table should requires at least three columns

Aggregations are cases where column content is summarized, such as taking the average, sum, count, mimimum, or maximum.
Please start by thinking carefully about whether a pivot table is needed by checking against the above criteria.
If indeed a pivot table is needed, then output a short and descriptive name that matches the naming conventions of the other tables. Otherwise, output an empty string.
A short name contains fewer parts, so if a query involves the 'conversion rate from the checkout page for a cohort of visitors in June 2023', a good name:
  * should be as short as possible, without repeating the name of any existing table
  * is certainly _not_ 'conversion_rate_from_checkout' or 'conversion_rate_for_June_2023_visitors' which are excessively long
  * probably should not even be 'conversion_rate_June' or 'checkout_conversions' which are still too long
  * instead aim for 'conversion_rate' or even just 'conversions' if this does not overlap with any other table

Your entire response should be in well-formatted JSON including keys for thought (string) and table_name (string), with no further explanations after the JSON output.

For example,
---
For our first example, suppose the tables and columns are:
* Tables: mailchimp_download, sub_aggregated_stripe, app_annie_analytics_Sept
* Columns: mc_id, email_address, is_subscribed, clicks, open_rate, domain, open_timestamp, delivered in mailchimp_download;
subscription_id, actitity_type, activity_cleaned, address, phone number, refund_amount, discount_applied in sub_aggregated_stripe;
page_views, downloads, installs, uninstalls, email, num_clicks, time_in_app, time_in_hours in app_annie_analytics_Sept;

_Conversation History_
User: Do subscriptions actually lead to conversions later on?
Agent: What counts as a conversion?
User: Let's look at both downloads and installations.

_Output_
```json
{{
  "thought": "The query likely groups by subscriber, and counts the total of downloads and installs. This involves grouping and two aggregations, which is complex enough.",
  "table_name": "subscription_conversions"
}}
```

In our second example, suppose the tables and columns are:
* Tables: CodePathCourses, StudentProgress, LearnerSuccess
* Columns: CourseID, TrackName, LeadMentorID, TechStackJSON, CohortStartDate, CohortEndDate, WeeklyCommitmentHrs, DeliveryFormat, DifficultyLevel, CurrentEnrollment, MaxCapacity in CodePathCourses;
ProgressID, CourseID, EnrollmentDate, MilestoneStatus, LastSubmissionURL, PeerReviewScore, TuitionPlanType, EmploymentStatus, MentorNotes, ActiveStatus in StudentProgress;
SuccessID, LearnerUUID, CourseID, CareerOutcome, SalaryIncrease, TestimonialBody, DateSubmitted, NPSScore, CompanyPlaced, ShowcasePermission in LearnerSuccess;

_Conversation History_
User: What are the different type of tech stacks offered?
Agent: There seem to 23 different options, with the most popular being Python Flask, Python Django, and JavaScript React.
User: What's the average salary increase for any given tech stack?

_Output_
```json
{{
  "thought": "The query likely groups by tech stack and averages the salary increase. This involves grouping and one aggregation, which is not complex enough.",
  "table_name": ""
}}
```

In our third example, suppose the tables and columns are:
* Tables: AdobeAnalytics final; Subscription Membership; Canva Content (revised); Vendor Expenses
* Columns: campaign_id, ad_platform, ad_spend, ad_type, ad_copy, user_activity, conversion_count in AdobeAnalytics final;
member_id, subscription_date, renewal_date, subscription_tier, monthly_fee, activity, member_status in Subscription Membership;
video_id, trainer_id, video_campaign_id, creation_date, video_type, trainer_fee, view_count in Canva Content (revised);
vendor_id, service_provided, expense_date, expense_amount, vendor_category in Vendor Expenses

_Conversation History_
User: what's the total view count for each video type?
Agent: The Pump It Up series recieved 27,204 views followed by 'Get Fit Now' with 24,511 views. See table for more.
User: What if we looked at the average per day for each series?

_Output_
```json
{{
  "thought": "The query likely groups by video type, groups by date, and averages the view count. This involves two groupings and one aggregation, which is complex enough. The naming convention uses spaces rather than underscores.",
  "table_name": "Videos Per Day"
}}
```

Next, suppose the tables and columns are:
* Tables: qualifiedLeads, userActivityLogs, subscriptionsJune, verifiedDataSources
* Columns: lead_id, first_name, last_name, email, organization, lead_source, contact_date, status, notes, follow_up_date in qualifiedLeads;
activity_id, user_id, launch_id, activity_type, timestamp, duration, data_source, outcome, error_log in userActivityLogs;
subscription_id, lead_id, plan_name, sub_timestamp, billing_cycle, payment_amount, renewal_notice in subscriptionsJune;
launch_id, is_secure, provenance, version, features, documentation_link in verifiedDataSources

_Conversation History_
User: Which lead sources give us the best conversions to paid plans?
Agent: I'm sorry, how should I define a paid plan?
User: Anything that is attached to a payment amount greater than $0 will work. Also can you sort for highest payment to lowest?

_Output_
```json
{{
  "thought": "The query likely groups by lead source, filters by payment amount, and sorts by payment amount. The output only involves the lead source and payment amount as two columns, so it is not complex enough.",
  "table_name": ""
}}
```

For our final example, suppose the tables and columns are:
* Tables: ride_bookings, active_promos, customer_support
* Columns: ride_id, customer_id, vehicle_id, promo_code_applied, start_time, end_time, pickup_location, dropoff_location, booking_status, total_amount, payment_status in ride_bookings;
promotion_id, discount_amount, applicable_vehicle_types, terms_conditions, redemption_count, application_date in active_promos;
ticket_id, customer_id, issue_date, issue_type, issue_description, assigned_agent, resolution_status, resolution_date, feedback, follow_up_required in customer_support

_Conversation History_
User: What percentage of people who recieved a promo code actually used it?
Agent: Comparing the redemption count to the number of people who received the code results in a 23.6% conversion rate.
User: What if we restricted only to rides that happened between March 1st to May 31st?

_Output_
```json
{{
  "thought": "The query filters by date and counts people who recieved a promo code and people who used it. This involves filtering and two aggregations, but no grouping, so it is not complex enough.",
  "table_name": ""
}}
```
---
In our real case, the tables and columns are:
{valid_tab_col}

For additional context, some extracted operations are:
{operations}

_Conversation History_
{history}

_Output_
"""

empty_result_prompt = """We are trying to calculate the {metric} metric based on the conversation history:
{history}

We also believe that {thought}.
Our current results are:
{result}

Would you interpret these results as possibly empty or zero? Please reply with 'yes' or 'no', with no further explanations. """

clarifying_thought_prompt = """When performing multi-step analysis, it's important to fully understand the situation before proceeding in order to avoid embarrassing mistakes.
Given the conversation history and related thought process, your task is to capture the key points of uncertainty that could benefit from clarification.
When doing so, focus on asking questions necessary for handling the overall request, to avoid losing the forest for the trees:
  * Any critical information that is missing which impedes our analysis?
  * Are we making any unwarranted assumptions about the user's preferences?
  * What tenuous steps in our plan might we want to confirm before proceeding?

Please start your response with a concise thought that encapsulates the user's main intent, which will serve as a reminder of the task at hand.
Then, generate a series of clarifying questions that address the missing details ranked from most to least critical. Previously generated questions should not be repeated.
Your entire response should be in well-formatted JSON, with keys for thought (string) and questions (list of strings). There should be no further explanations after the JSON output.

For example,
---
For our first example, suppose the tables and columns are:
* Tables: CodePathCourses, StudentProgress, LearnerSuccess, MarketingCampaigns
* Columns: CourseID, TrackName, LeadMentorID, TechStackJSON, CohortStartDate, CohortEndDate, WeeklyCommitmentHrs, DeliveryFormat, DifficultyLevel, CurrentEnrollment, MaxCapacity, PreReqSkills, GitHubTemplateURL in CodePathCourses;
ProgressID, CourseID, LearnerUUID, EnrollmentDate, MilestoneStatus, LastSubmissionURL, PeerReviewScore, TuitionPlanType, EmploymentStatus, MentorNotes, ActiveStatus in StudentProgress;
SuccessID, LearnerUUID, CourseID, CareerOutcome, SalaryIncrease, TestimonialBody, DateSubmitted, NPSScore, CompanyPlaced, ShowcasePermission in LearnerSuccess;
CampaignID, InitiativeName, CareerSegment, LinkedInAudience, ConversionMetrics, PartnershipType, BudgetAllocated, LeadSource in MarketingCampaigns
* Existing questions:
None

_Conversation History_
User: What is the range of peer review scores for all students in the last cohort?
Agent: Using CohortStartDate to determine the last cohort, the peer review scores range from 60 to 95.
User: Ok, taking a broader view, does increased student activity lead to better outcomes?

_Prior Thought_
To analyze if increased student activity leads to better outcomes, we need to first ground our analysis using <LastSubmissionURL>, <MilestoneStatus>, and <ActiveStatus> from StudentProgress as activity indicators,
linking to outcome metrics like <CareerOutcome>, <SalaryIncrease>, and <NPSScore> from LearnerSuccess through the common <LearnerUUID> identifier.
Given so many options, we will need clarification on which combination of indicators is used to define "activity", and also how to measure which outcomes are 'better'.
Since the conversation previously referenced the last cohort, we should consider using <CohortStartDate> and <CohortEndDate> to place constraints on the time period for analysis.
For staging, we should consider creating intermediate metrics that aggregate activity levels per student while accounting for different <PreReqSkills> and <DifficultyLevel> variations across courses,
as these could confound our analysis. Several critical pieces remain ambiguous and require user input: whether to control for factors like <TechStackJSON>, whether to segment by <DeliveryFormat>,
and how exactly should we determine what constitutes a "better" outcome?

_Output_
```json
{{
  "thought": "Main goal is to figure out how 'activity' among students correlates with better outcomes.",
  "questions": [
    "Which combination of indicators or metrics define 'activity' among students?",
    "How exactly should we determine what constitutes a 'better' outcome?",
    "Should we segment the analysis by 'DeliveryFormat' or 'TechStackJSON'?",
    "Is there a threshold for 'better outcomes' that we should consider?"
  ]
}}
```
---
For our next scenario, suppose the tables and columns are:
* Tables: tbl_customer_master, mkt_campaigns_v2, inv_items_current, promo_discount_rules
* Columns: cust_id, user_uuid, f_name, l_name, customer_email, registration_ts, loyalty_pts_current, loyalty_pts_lifetime, loyalty_tier, shipping_addr, billing_addr, is_deleted, newsletter_flag, last_modified_dt in tbl_customer_master;
campaign_id, campaign_code, promo_name, dt_start, dt_end, segment_code, channel_type, budget_allocated, budget_spent, response_pct, asset_url, is_active, created_by, modified_by, created_dt, modified_dt in mkt_campaigns_v2;
item_sk, item_code, sku, cat_id, cat_name, sub_cat_id, price_msrp, price_current, qty_on_hand, qty_allocated, qty_backordered, supplier_id, supplier_name, date_added, last_restock_dt, reorder_point, is_active, discontinued_flag in inv_items_current;
promo_sk, legacy_promo_id, promo_desc, valid_from, valid_to, discount_pct, discount_amt, min_purchase_amt, excluded_items, coupon_code, times_used, max_uses, single_use_flag, stackable_flag, created_ts, modified_ts in promo_discount_rules
* Existing questions:
I am going to calculate ROAS for recent promotions by focusing on return and ad spend, is that right?
How do you want to calculate return?
Should I be aggregating the 'budget_spent' column for the same time period when calculating ad spend?

_Conversation History_
User: Yes, you can use total revenue from the inventory items to measure the return. Just use the time range to join against the campaign data.
Agent: Ok, I will look back on the last quarter of inventory sold to calculate the return. Moving onto 'ad spend', should I be aggregating the 'budget_spent' column for the same time period?
User: Actually, total ad spend includes discount amount in addition to budget spent.
Agent: It seems that the promo_name from mkt_campaigns_v2 does not line up with the promo_sk from promo_discount_rules, any ideas on how to join the two tables together?
User: Same as before, if it's running at the same time, they are the same promotion.

_Prior Thought_
To calculate promotional ROAS, we'll need to calulate return and ad spend as intermediate values, and possibly create staging tables to stitch these together.
As hinted at in the conversation, we can ground the 'return' derivation by pulling from <price_current> * <qty_on_hand> in inv_items_current,
while 'ad spend' should combine <budget_spent> from mkt_campaigns_v2 and <discount_amt> from promo_discount_rules for the last quarter.
The time period can be determined by aligning <dt_start>/<dt_end> with <valid_from>/<valid_to>, but inv_items_current lacks a clear time indicator, so that still needs clarification.
It's possible to group by <campaign_id>, segment by <channel_type>, or apply the <segment_code>, so we should confirm if any of these groupings are necessary.
Many other factors remain unclear, such as whether to consider <discontinued_flag> for calculations or if <excluded_items> should be excluded, which may introduce branching logic.

_Output_
```json
{{
  "thought": "Main goal is to calculate ROAS for recent promotions.",
  "questions": [
    "What column within the 'inv_items_current' table indicates the time range?",
    "Should we group by 'campaign_id' or segment by 'channel_type'?",
    "Do we want to break down the results by 'segment_code'?",
    "How should we handle 'discontinued_flag' and 'excluded_items'?"
  ]
}}
```
---
In our third scenario, suppose the tables and columns are:
* Tables: MarketingQualifiedLeads, ProductReleases, Subscriptions, RenderActivity
* Columns: LeadID, CompanyName, ContactEmail, IndustryType, AnnualAdSpend, TeamSize, LeadSource, DemoScheduled, PocStatus, SalesRepID, LastTouchpoint in MarketingQualifiedLeads;
ReleaseID, VersionNumber, EngineVersion, AIModelType, MaxResolution, NewFeaturesList, StabilityScore, BetaTestResults, DocumentationURL, ReleaseChannel in ProductReleases;
SubscriptionID, ClientUUID, PlanTier, GPUAllocation, MonthlyQuota, BillingCycle, PaymentStatus, OverageRate, ApiKeyHash, ContractStatus in Subscriptions;
RenderID, ClientUUID, ProjectName, RenderType, StartTimestamp, ProcessingTime, OutputFormat, ComputeUnits, QueuePriority, RenderStatus, ErrorTrace in RenderActivity
* Existing questions:
What specific date range constitutes 'a few days ago'?

_Conversation History_
User: Show me the average processing time for all renders that were completed last month.
Agent: No problem, the average processing time for completed renders is mostly around 12 minutes. Please see the table for more.
User: There's big spike in processing time a few days ago, what happened?
Agent: There are a number of potential causes, such as server issues or high demand. Is there anything that you suspect might have caused the spike?
User: I would consider the processing time broken down by render type. We can also check if there's any correlation with the new features list and beta test results.

_Prior Thought_
To investigate processing time spikes, we can ground our analysis by joining <ProcessingTime> and <RenderType> from RenderActivity with ProductReleases using <EngineVersion>,
focusing on records where <RenderStatus> indicates completion and filtering to recent <StartTimestamp> values that align with the reported spike.
The user directly mentioned cross-referencing <ProcessingTime> against <NewFeaturesList> and <BetaTestResults> from ProductReleases, so these should be calculated and staged as intermediate metrics.
We can also consider <StabilityScore> as a potential indicator, and grouping by <ClientUUID> or <ProjectName> to identify specific clients or projects that may isolate the cause of the spike.
Several aspects need more specificatio: the exact date range of "a few days ago," whether to consider <QueuePriority> or <PlanTier>, or whether the <ErrorTrace> entries are worth investigating.
While filtering by <StartTimestamp> is likely, we should be making assumptions about the user's intent, so this could be a clarified as well.

_Output_
```json
{{
  "thought": "Main goal is to determine the cause of recent spike in processing time.",
  "questions": [
    "Should we consider 'QueuePriority' or 'PlanTier' in our analysis?",
    "Is there a threshold for 'ProcessingTime' that I should be aware of?",
    "How should we handle 'ErrorTrace' entries in the analysis?"
  ]
}}
```
---
For our final example, suppose the tables and columns are:
* Tables: booking_requests_v3, discount_programs_active, assistance_tickets_main
* Columns: booking_ref_id, pax_uuid, caregiver_uuid, journey_start_ts, journey_end_ts, payment_amt, pickup_addr, pickup_access_notes, mobility_equipment, booking_status_cd, payment_status_cd, special_instructions_txt in booking_requests_v3;
promo_sk, program_name, partner_org_id, discount_pct, eligible_vehicle_types, requires_medical_docs, docs_required_txt, total_claims, valid_from_dt, valid_to_dt in discount_programs_active;
ticket_sk, customer_book_id, caregiver_uuid, created_ts, severity_level, issue_category_main, medical_emergency_flag, issue_details_encrypted, assigned_agent_id, resolution_code, satisfaction_rating, compliance_review_flag in assistance_tickets_main
* Existing questions:
How did you want to segment the people who have filed tickets?
What is a reasonable baseline amount that you expect for a payment?

_Conversation History_
User: so for example, we can see if those folks usually pay a lot, pay a little, or where they usually come from. Whatever you want to come up.
Agent: OK, I will check for any patterns among ticket issuers starting with payment amounts, pick-up locations, and customer satisfaction. Should we limit our analysis to repeat ticket issuers or some other minimum threshold of tickets?
User: Yea, sure why not. Only people who have filed tickets more than once.
Agent: Got it. My next question is, what is a reasonable baseline amount that you expect for a payment? When would you consider someone to be paying a lot or a little?
User: I guess 100 to 200 is normal, anything above $500 is over

_Prior Thought_
To analyze patterns among repeat ticket issuers, we'll ground our analysis by joining assistance_tickets_main to booking_requests_v3 through <customer_book_id>,
filtering where ticket count per <pax_uuid> is greater than one and focusing on their <payment_amt> patterns (with clear thresholds of $100-200 as normal, >$500 as high).
We also have not been given a specific time period for analysis, so we should clarify whether <created_ts> or <valid_from_dt> should be used to constrain the data.
For staging, we'll need to create intermediate aggregations that group by passenger to show their ticket frequency, average <payment_amt>, common <pickup_addr> locations,
and <satisfaction_rating> trends, possibly segmented by <issue_category_main>. Several aspects remain unclear: whether to include tickets where <medical_emergency_flag>
is true since these might skew the analysis, if <severity_level> should influence how we count tickets, whether to consider <compliance_review_flag> in our filtering.

_Output_
```json
{{
  "thought": "Main goal is to analyze patterns among repeat ticket issuers.",
  "questions": [
    "Should we use 'created_ts' or 'valid_from_dt' to constrain the time period?",
    "Tickets with 'medical_emergency_flag' might skew the analysis, so perhaps we should exclude them?",
    "Does 'severity_level' have any relevance when counting support tickets?",
    "Should tickets under compliance review be filtered out of our analysis?"
  ]
}}
```
---
Now it's your turn! Please generate a consise thought followed by a JSON-formatted list of questions to clarify the key points of uncertainty.
For our real case, the available data includes:
{valid_tab_col}
* Existing questions:
{questions}

_Conversation History_
{history}

_Prior Thought_
{thought}

_Output_
"""

# , noting whether the operation is required or merely recommended as an option to explore.
# There is no need to repeat any operations that have already been captured. 
operational_thought_prompt = """When performing multi-step analysis, it's important to fully understand the situation before proceeding in order to avoid embarrassing mistakes.
Given the conversation history and related thought process, your task is to capture the relevant operations to perform based on the user's request.
In particular, please focus on the following aspects:
  * join: joining two tables based on a common key
  * filter: filtering rows based on specific conditions
  * staging: creating intermediate staging tables to facilitate further analysis
  * aggregate: calculate metrics; the first token should be in [Sum, Count, Average, Top, Bottom, Min, Max, Ratio]
  * time_limit: constraining the analysis to a specific time period
  * comparison: comparing metrics across different groups
  * segmentation: segmenting data based on certain attributes

Please start your response with a concise thought that encapsulates the user's main intent, which will serve as a reminder of the task at hand.
Then, generate a series of operations that outline the possible steps. Your entire response should be in well-formatted JSON including keys for thought (string) and operations (list of dicts).
Each operation has keys with type (token) and details (sentence). There should be no further explanations after the JSON output.

For example,
---
For our first example, suppose the tables and columns are:
* Tables: CodePathCourses, StudentProgress, LearnerSuccess, MarketingCampaigns
* Columns: CourseID, TrackName, LeadMentorID, TechStackJSON, CohortStartDate, CohortEndDate, WeeklyCommitmentHrs, DeliveryFormat, DifficultyLevel, CurrentEnrollment, MaxCapacity, PreReqSkills, GitHubTemplateURL in CodePathCourses;
ProgressID, CourseID, LearnerUUID, EnrollmentDate, MilestoneStatus, LastSubmissionURL, PeerReviewScore, TuitionPlanType, EmploymentStatus, MentorNotes, ActiveStatus in StudentProgress;
SuccessID, LearnerUUID, CourseID, CareerOutcome, SalaryIncrease, TestimonialBody, DateSubmitted, NPSScore, CompanyPlaced, ShowcasePermission in LearnerSuccess;
CampaignID, InitiativeName, CareerSegment, LinkedInAudience, ConversionMetrics, PartnershipType, BudgetAllocated, LeadSource in MarketingCampaigns

_Conversation History_
User: What is the range of peer review scores for all students in the last cohort?
Agent: Using CohortStartDate to determine the last cohort, the peer review scores range from 60 to 95.
User: Ok, taking a broader view, does increased student activity lead to better outcomes?

_Prior Thought_
To analyze if increased student activity leads to better outcomes, we need to first ground our analysis using <LastSubmissionURL>, <MilestoneStatus>, and <ActiveStatus> from StudentProgress as activity indicators,
linking to outcome metrics like <CareerOutcome>, <SalaryIncrease>, and <NPSScore> from LearnerSuccess through the common <LearnerUUID> identifier.
Given so many options, we will need clarification on which combination of indicators is used to define 'activity', and also how to measure which outcomes are 'better'.
Since the conversation previously referenced the last cohort, we should consider using <CohortStartDate> and <CohortEndDate> to place constraints on the time period for analysis.
For staging, we should consider creating intermediate metrics that aggregate activity levels per student while accounting for different <PreReqSkills> and <DifficultyLevel> variations across courses,
as these could confound our analysis. Several critical pieces remain ambiguous and require user input: whether to control for factors like <TechStackJSON>, whether to segment by <DeliveryFormat>,
and how exactly should we determine what constitutes a 'better' outcome?

_Output_
```json
{{
  "thought": "Main goal is to figure out how 'activity' among students correlates with better outcomes.",
  "operations": [
    {{ "type": "join", "details": "Join StudentProgress and LearnerSuccess on LearnerUUID"}},
    {{ "type": "filter", "details": "Filter for certain students based on PreReqSkills and DifficultyLevel"}},
    {{ "type": "staging", "details": "Insert intermediate table that holds activity levels"}},
    {{ "type": "aggregate", "details": "Top activity levels based on LastSubmissionURL, MilestoneStatus, and ActiveStatus"}},
    {{ "type": "time_limit", "details": "Filter for students in the last cohort based on CohortStartDate"}},
    {{ "type": "comparison", "details": "Compare activity levels based on CareerOutcome and SalaryIncrease"}},
    {{ "type": "segmentation", "details": "Group by DeliveryFormat or TechStackJSON"}}
  ]
}}
```
---
For our next scenario, suppose the tables and columns are:
* Tables: tbl_customer_master, mkt_campaigns_v2, inv_items_current, promo_discount_rules
* Columns: cust_id, user_uuid, f_name, l_name, customer_email, registration_ts, loyalty_pts_current, loyalty_pts_lifetime, loyalty_tier, shipping_addr, billing_addr, is_deleted, newsletter_flag, last_modified_dt in tbl_customer_master;
campaign_id, campaign_code, promo_name, dt_start, dt_end, segment_code, channel_type, budget_allocated, budget_spent, response_pct, asset_url, is_active, created_by, modified_by, created_dt, modified_dt in mkt_campaigns_v2;
item_sk, item_code, sku, cat_id, cat_name, sub_cat_id, price_msrp, price_current, qty_on_hand, qty_allocated, qty_backordered, supplier_id, supplier_name, date_added, last_restock_dt, reorder_point, is_active, discontinued_flag in inv_items_current;
promo_sk, legacy_promo_id, promo_desc, valid_from, valid_to, discount_pct, discount_amt, min_purchase_amt, excluded_items, coupon_code, times_used, max_uses, single_use_flag, stackable_flag, created_ts, modified_ts in promo_discount_rules

_Conversation History_
User: Yes, you can use total revenue from the inventory items to measure the return. Just use the time range to join against the campaign data.
Agent: Ok, I will look back on the last quarter of inventory sold to calculate the return. Moving onto 'ad spend', should I be aggregating the 'budget_spent' column for the same time period?
User: Actually, total ad spend includes discount amount in addition to budget spent.
Agent: It seems that the promo_name from mkt_campaigns_v2 does not line up with the promo_sk from promo_discount_rules, any ideas on how to join the two tables together?
User: Same as before, if it's running at the same time, they are the same promotion.

_Prior Thought_
To calculate promotional ROAS, we'll need to calulate return and ad spend as intermediate values, and possibly create staging tables to stitch these together.
As hinted at in the conversation, we can ground the 'return' derivation by pulling from <price_current> * <qty_on_hand> in inv_items_current,
while 'ad spend' should combine <budget_spent> from mkt_campaigns_v2 and <discount_amt> from promo_discount_rules for the last quarter.
The time period can be determined by aligning <dt_start>/<dt_end> with <valid_from>/<valid_to>, but inv_items_current lacks a clear time indicator, so that still needs clarification.
It's possible to group by <campaign_id>, segment by <channel_type>, or apply the <segment_code>, so we should confirm if any of these groupings are necessary.
Many other factors remain unclear, such as whether to consider <discontinued_flag> for calculations or if <excluded_items> should be excluded, which may introduce branching logic.

_Output_
```json
{{
  "thought": "Main goal is to calculate ROAS for recent promotions.",
  "operations": [
    {{ "type": "join", "details": "Join mkt_campaigns_v2 and promo_discount_rules on valid_from/valid_to"}},
    {{ "type": "aggregate", "details": "Sum the product of price_current and qty_on_hand for return"}},
    {{ "type": "aggregate", "details": "Sum budget_spent + discount_amt for ad spend"}},
    {{ "type": "aggregate", "details": "Ratio of return to ad spend for ROAS"}},
    {{ "type": "staging", "details": "Create intermediate metrics for return and ad spend"}},
    {{ "type": "time_limit", "details": "Filter for last quarter of inventory sold"}},
    {{ "type": "comparison", "details": "Compare promo_name and promo_sk for matching promotions"}},
    {{ "type": "segmentation", "details": "Group by campaign_id, channel_type, or segment_code"}}
  ]
}}
```
---
In our third scenario, suppose the tables and columns are:
* Tables: MarketingQualifiedLeads, ProductReleases, Subscriptions, RenderActivity
* Columns: LeadID, CompanyName, ContactEmail, IndustryType, AnnualAdSpend, TeamSize, LeadSource, DemoScheduled, PocStatus, SalesRepID, LastTouchpoint in MarketingQualifiedLeads;
ReleaseID, VersionNumber, EngineVersion, AIModelType, MaxResolution, NewFeaturesList, StabilityScore, BetaTestResults, DocumentationURL, ReleaseChannel in ProductReleases;
SubscriptionID, ClientUUID, PlanTier, GPUAllocation, MonthlyQuota, BillingCycle, PaymentStatus, OverageRate, ApiKeyHash, ContractStatus in Subscriptions;
RenderID, ClientUUID, ProjectName, RenderType, StartTimestamp, ProcessingTime, OutputFormat, ComputeUnits, QueuePriority, RenderStatus, ErrorTrace in RenderActivity

_Conversation History_
User: Show me the average processing time for all renders that were completed last month.
Agent: No problem, the average processing time for completed renders is mostly around 12 minutes. Please see the table for more.
User: There's big spike in processing time a few days ago, what happened?
Agent: There are a number of potential causes, such as server issues or high demand. Is there anything that you suspect might have caused the spike?
User: I would consider the processing time broken down by render type. We can also check if there's any correlation with the new features list and beta test results.

_Prior Thought_
To investigate processing time spikes, we can ground our analysis by joining <ProcessingTime> and <RenderType> from RenderActivity with ProductReleases using <EngineVersion>,
focusing on records where <RenderStatus> indicates completion and filtering to recent <StartTimestamp> values that align with the reported spike.
The user directly mentioned cross-referencing <ProcessingTime> against <NewFeaturesList> and <BetaTestResults> from ProductReleases, so these should be calculated and staged as intermediate metrics.
We can also consider <StabilityScore> as a potential indicator, and grouping by <ClientUUID> or <ProjectName> to identify specific clients or projects that may isolate the cause of the spike.
Several aspects need more specification: the exact date range of "a few days ago," whether to consider <QueuePriority> or <PlanTier>, or whether the <ErrorTrace> entries are worth investigating.
While filtering by <StartTimestamp> is likely, we should be making assumptions about the user's intent, so this could be a clarified as well.

_Output_
```json
{{
  "thought": "Main goal is to determine the cause of recent spike in processing time.",
  "operations": [
    {{ "type": "join", "details": "Join RenderActivity and ProductReleases on EngineVersion"}},
    {{ "type": "filter", "details": "Filter out entries that contain errors based on ErrorTrace"}},
    {{ "type": "filter", "details": "Filter for RenderStatus = 'Completed'"}},
    {{ "type": "staging", "details": "Create intermediate metrics for StabilityScore"}},
    {{ "type": "aggregate", "details": "Average the ProcessingTime by RenderType"}},
    {{ "type": "time_limit", "details": "Filter for recent StartTimestamp values"}},
    {{ "type": "comparison", "details": "Compare ProcessingTime with NewFeaturesList and BetaTestResults"}},
    {{ "type": "segmentation", "details": "Group by ClientUUID or ProjectName"}}
  ]
}}
```
---
For our final example, suppose the tables and columns are:
* Tables: booking_requests_v3, discount_programs_active, assistance_tickets_main
* Columns: booking_ref_id, pax_uuid, caregiver_uuid, journey_start_ts, journey_end_ts, payment_amt, pickup_addr, pickup_access_notes, mobility_equipment, booking_status_cd, payment_status_cd, special_instructions_txt in booking_requests_v3;
promo_sk, program_name, partner_org_id, discount_pct, eligible_vehicle_types, requires_medical_docs, docs_required_txt, total_claims, valid_from_dt, valid_to_dt in discount_programs_active;
ticket_sk, customer_book_id, caregiver_uuid, created_ts, severity_level, issue_category_main, medical_emergency_flag, issue_details_encrypted, assigned_agent_id, resolution_code, satisfaction_rating, compliance_review_flag in assistance_tickets_main

_Conversation History_
User: so for example, we can see if those folks usually pay a lot, pay a little, or where they usually come from. Whatever you want to come up.
Agent: OK, I will check for any patterns among ticket issuers starting with payment amounts, pick-up locations, and customer satisfaction. Should we limit our analysis to repeat ticket issuers or some other minimum threshold of tickets?
User: Yea, sure why not. Only people who have filed tickets more than once.
Agent: Got it. My next question is, what is a reasonable baseline amount that you expect for a payment? When would you consider someone to be paying a lot or a little?
User: I guess 100 to 200 is normal, anything above $500 is over

_Prior Thought_
To analyze patterns among repeat ticket issuers, we'll ground our analysis by joining assistance_tickets_main to booking_requests_v3 through <customer_book_id>,
filtering where ticket count per <pax_uuid> is greater than one and focusing on their <payment_amt> patterns (with clear thresholds of $100-200 as normal, >$500 as high).
We also have not been given a specific time period for analysis, so we should clarify whether <created_ts> or <valid_from_dt> should be used to constrain the data.
For staging, we'll need to create intermediate aggregations that group by passenger to show their ticket frequency, average <payment_amt>, common <pickup_addr> locations,
and <satisfaction_rating> trends, possibly segmented by <issue_category_main>. Several aspects remain unclear: whether to include tickets where <medical_emergency_flag>
is true since these might skew the analysis, if <severity_level> should influence how we count tickets, whether to consider <compliance_review_flag> in our filtering.

_Output_
```json
{{
  "thought": "Main goal is to analyze patterns among repeat ticket issuers.",
  "operations": [
    {{ "type": "join", "details": "Join assistance_tickets_main and booking_requests_v3 on customer_book_id"}},
    {{ "type": "filter", "details": "Filter out tickets where medical_emergency_flag is true"}},
    {{ "type": "filter", "details": "Filter out tickets where severity_level is high"}},
    {{ "type": "filter", "details": "Filter for repeat ticket issuers where pax_uuid > 1"}},
    {{ "type": "staging", "details": "Create intermediate metrics for ticket frequency and satisfaction_rating"}},
    {{ "type": "aggregate", "details": "Sum the payment_amt patterns"}},
    {{ "type": "time_limit", "details": "Filter for recent created_ts or valid_from_dt"}},
    {{ "type": "comparison", "details": "Check total payment_amt to see how many people are within $100 to $200"}},
    {{ "type": "comparison", "details": "Compare total payment_amt to see how many people are above $500"}},
    {{ "type": "segmentation", "details": "Segment by issue_category_main"}}
  ]
}}
```
---
Now it's your turn! Please generate a consise thought followed by a JSON-formatted list of questions to clarify the key points of uncertainty.
For our real case, the available data includes:
{valid_tab_col}

_Conversation History_
{history}

_Prior Thought_
{thought}

_Output_
"""

metric_routing_prompt = """Given the conversation history, your task is to determine the scope of the analysis presented in the user's request.
Specifically, you need to decide whether the request is for a simple query, pivot table, basic analysis, multi-step analysis, or insight detection.
  * query: retrieve data from a table by filtering, grouping, sorting, or applying aggregation operations directly on the columns
  * pivot: reorganize data to show different views, which requires at least three operations, one of which must be grouping by some dimension
    - valid operations include filtering, grouping, or applying aggregations such as sum, count, average, or string_agg
    - notably, sorting and limiting rows (ie. top 3, bottom 10%, min/max) are not considered operations since they don't change data structure
    - if the phrase 'pivot table' is used or there is an explicit request to create a new table, then we are definitely dealing with 'pivot'
  * analysis: calculate a metric that requires intermediate variables rather than directly accessing the columns
  * segment: perform analysis involving metric segmentation by inserting staging columns before reaching the final answer
  * insight: open-ended exploration of the data to identify patterns or trends, anything associated with calculating multiple metrics

To guide your decision, you are also given some prior thoughts and the name of the metric we are supposed to calculate.
Common metrics include Cart Abandonment Rate, Average Order Value (AOV), Customer Acquisition Cost (CAC), Cost per Click (CPC), Click-Thru Rate (CTR), Daily Active Users (DAU), Net Promoter Score (NPS), Email Open Rate, Profit, or Return on Ad Spend (ROAS)
Concretely, suppose you want to calculate the conversion rate (CVR) for an E-commerce website:
  * if there already exist columns for 'conversions' and 'visits', then we can directly calculate CVR = conversions / visits, which is a simple query.
  * if we want conversions per day from FB Ads, then this requires grouping by day, filtering by channel, and aggregating the sum of conversions. Given three operations, we graduate from query to pivot.
  * if we need to calculate conversions by first pulling from other columns (ie. filter for the 'purchase' event type where 'purchase amount > 0'), then we are performing a basic analysis.
  * if we wanted to break down the conversion rate by day or segment it by IP address, then we are performing a segmentation analysis.
  * if our goal is to find the most effective channel, then we might consider CTR and CPC along with CVR. This is more than one metric, which implies insight detection.

Some other things to consider:
  * When we need to calculate a business metric, it is likely an 'analysis' or 'segment' scenario since key variables are often not directly available as columns
  * Generally speaking, 'pivot' is the next level of complexity after 'query' due to grouping, while 'segment' can be viewed as the next level after 'analysis' due to segmentation
  * 'insight' comes into play when things have gotten so complicated that we need to form a plan involving multiple other scopes to find the answer
  * If the prior thought or metric name are empty or TBD, do not panic! Just consider the conversation history and make an informed decision.

Please start by considering if the metric can already be directly pulled from the table. If so, then we are dealing with a 'query'.
Next, if the metric requires three or more operations where at least one is grouping, then we are dealing with a 'pivot'.
Alternatively, if the metric requires some intermediate calculations, then we are dealing with an 'analysis'.
Then, consider if we need to segment or bucket the metric based on along some dimension. If so, then we are dealing with a 'segment'.
Finally, consider if the there are actually multiple metrics being calculated or other vagueness. If so, then we are dealing with an 'insight'.
If none of the above apply, possibly because no metric can be inferred from the context, then we set the scope to 'unknown'.
Your entire response should be in well-formatted JSON including keys for thought (string) and scope (token), with no further explanations after the JSON output.

For example,
---
_Conversation History_
User: What is the range of peer review scores for all students in the last cohort?
Agent: Using CohortStartDate to determine the last cohort, the peer review scores range from 60 to 95.
User: Ok, taking a broader view, does increased student activity lead to better outcomes?

_Prior Thought_
To analyze if increased student activity leads to better outcomes, we need to first ground our analysis using <LastSubmissionURL>, <MilestoneStatus>, and <ActiveStatus> from StudentProgress as activity indicators,
linking to outcome metrics like <CareerOutcome>, <SalaryIncrease>, and <NPSScore> from LearnerSuccess through the common <LearnerUUID> identifier.
Given so many options, we will need clarification on which combination of indicators is used to define 'activity', and also how to measure which outcomes are 'better'.
Since the conversation previously referenced the last cohort, we should consider using <CohortStartDate> and <CohortEndDate> to place constraints on the time period for analysis.
For staging, we should consider creating intermediate metrics that aggregate activity levels per student while accounting for different <PreReqSkills> and <DifficultyLevel> variations across courses,
as these could confound our analysis. Several critical pieces remain ambiguous and require user input: whether to control for factors like <TechStackJSON>, whether to segment by <DeliveryFormat>,
and how exactly should we determine what constitutes a 'better' outcome?
Metric: Student Activity to Outcome Correlation (Corr)

_Output_
```json
{{
  "thought": "Connecting one student's activity to their outcome gives us a single datapoint. This score can be considered a metric. In order to compare across multiple students, we need to segment by student, which means this is segmentation analysis.",
  "scope": "segment"
}}
```

_Conversation History_
User: Yes, you can use total revenue from the inventory items to measure the return. Just use the time range to join against the campaign data.
Agent: Ok, I will look back on the last quarter of inventory sold to calculate the return. Moving onto 'ad spend', should I be aggregating the 'budget_spent' column for the same time period?
User: Actually, total ad spend includes discount amount in addition to budget spent.
Agent: It seems that the promo_name from mkt_campaigns_v2 does not line up with the promo_sk from promo_discount_rules, any ideas on how to join the two tables together?
User: Same as before, if it's running at the same time, they are the same promotion.

_Prior Thought_
To calculate promotional ROAS, we'll need to calulate return and ad spend as intermediate values, and possibly create staging tables to stitch these together.
As hinted at in the conversation, we can ground the 'return' derivation by pulling from <price_current> * <qty_on_hand> in inv_items_current,
while 'ad spend' should combine <budget_spent> from mkt_campaigns_v2 and <discount_amt> from promo_discount_rules for the last quarter.
The time period can be determined by aligning <dt_start>/<dt_end> with <valid_from>/<valid_to>, but inv_items_current lacks a clear time indicator, so that still needs clarification.
It's possible to group by <campaign_id>, segment by <channel_type>, or apply the <segment_code>, so we should confirm if any of these groupings are necessary.
Many other factors remain unclear, such as whether to consider <discontinued_flag> for calculations or if <excluded_items> should be excluded, which may introduce branching logic.
Metric: Promotional Return on Ad Spend (ROAS)

_Output_
```json
{{
  "thought": "Calculating return involves multiplying price and quantity columns, which immediately rules out 'query'. Grouping by campaign or channel has not been confirmed, so it's not 'segment' yet. We are filtering to a single time period, rather than splitting by time, so this is a straightforward analysis.",
  "scope": "analysis"
}}
```

_Conversation History_
User: Show me the average processing time for all renders that were completed last month.
Agent: No problem, the average processing time for completed renders is mostly around 12 minutes. Please see the table for more.
User: There's big spike in processing time a few days ago, what happened?
Agent: There are a number of potential causes, such as server issues or high demand. Is there anything that you suspect might have caused the spike?
User: I would consider the processing time broken down by render type. We can also check if there's any correlation with the new features list and beta test results.

_Prior Thought_
To investigate processing time spikes, we can ground our analysis by joining <ProcessingTime> and <RenderType> from RenderActivity with ProductReleases using <EngineVersion>,
focusing on records where <RenderStatus> indicates completion and filtering to recent <StartTimestamp> values that align with the reported spike.
The user directly mentioned cross-referencing <ProcessingTime> against <NewFeaturesList> and <BetaTestResults> from ProductReleases, so these should be calculated and staged as intermediate metrics.
We can also consider <StabilityScore> as a potential indicator, and grouping by <ClientUUID> or <ProjectName> to identify specific clients or projects that may isolate the cause of the spike.
Several aspects need more specification: the exact date range of "a few days ago," whether to consider <QueuePriority> or <PlanTier>, or whether the <ErrorTrace> entries are worth investigating.
While filtering by <StartTimestamp> is likely, we should be making assumptions about the user's intent, so this could be a clarified as well.
Metric: Processing Time by Render Type (PTR)

_Output_
```json
{{
  "thought": "Average processing time is a metric, and splitting by render type is a segmentation, so we know the complexity is at least 'segment'. Checking against new features and beta tests would both entail calculating metrics as well. Given the multiple metrics, we are dealing with 'insight'.",
  "scope": "insight"
}}
```

_Conversation History_
User: so for example, we can see if those folks usually pay a lot, pay a little, or where they usually come from. Whatever you want to come up.
Agent: OK, I will check for any patterns among ticket issuers starting with payment amounts, pick-up locations, and customer satisfaction. Should we limit our analysis to repeat ticket issuers or some other minimum threshold of tickets?
User: Yea, sure why not. Only people who have filed tickets more than once.
Agent: Got it. My next question is, what is a reasonable baseline amount that you expect for a payment? When would you consider someone to be paying a lot or a little?
User: I guess 100 to 200 is normal, anything above $500 is over

_Prior Thought_
To analyze patterns among repeat ticket issuers, we'll ground our analysis by joining assistance_tickets_main to booking_requests_v3 through <customer_book_id>,
filtering where ticket count per <pax_uuid> is greater than one and focusing on their <payment_amt> patterns (with clear thresholds of $100-200 as normal, >$500 as high).
We also have not been given a specific time period for analysis, so we should clarify whether <created_ts> or <valid_from_dt> should be used to constrain the data.
For staging, we'll need to create intermediate aggregations that group by passenger to show their ticket frequency, average <payment_amt>, common <pickup_addr> locations,
and <satisfaction_rating> trends, possibly segmented by <issue_category_main>. Several aspects remain unclear: whether to include tickets where <medical_emergency_flag>
is true since these might skew the analysis, if <severity_level> should influence how we count tickets, whether to consider <compliance_review_flag> in our filtering.
Metric: Repeat Ticket Issuers (RTI)

_Output_
```json
{{
  "thought": "The goal is to understand patterns among repeat ticket issuers, broken down by payment amounts. Due to segmentation, this is segmentation analysis. Other metrics come from our own thoughts, rather than the user, so this is not 'insight'.",
  "scope": "segment"
}}
```

_Conversation History_
User: Does Amplitude offer geographic data to figure out where our users are located?
Agent: Yes, Amplitude does offer country segmentation. However, this is merely inferred from IP addresses and should only be used as a rough estimate.
User: What is the daily new subscriber count for people viewing on mobile broken down by country?

_Prior Thought_
To analyze daily new subscriber counts for mobile users by country, we'll ground our analysis using the subscribers column from the Video_stats table,
joining with the Amplitude_analytics table through the campaign_id column and filtering for only mobile viewers based on the device_type column.
We'll need to stage intermediate results by grouping the data by country and calculating a count of subscribers per day,
ensuring we handle potential outliers or data quality issues like null values or extreme size measurements, such as countries with zero or negative subscribers.
Disambiguation considerations include clarifying whether we want to include all video types or focus on specific categories (eg. tutorials, sizzle reels, etc.),
and whether the analysis should be limited to a specific time period given the date range in the view_timestamp column.
Metric: To Be Determined (N/A)

_Output_
```json
{{
  "thought": "Since our calculation only requires reading from available columns, this is either query or pivot. Operations include grouping by country, grouping by day, filtering for mobile users, and aggregating the count of subscribers, which is four operations, so it is a pivot.",
  "scope": "pivot"
}}
```

_Conversation History_
User: I don't see as many people at the gym as before. How many active members are there in the last three months?
Agent: There are currently 574 people who have a date_joined in the last three months and do not have an expiration_date.
User: Interesting, how many people have actually enrolled in classes in that time?
Agent: According to the class_dates from the last three months, there are 1,023 people who have enrolled in classes.
User: How is that possible when there are only 574 active members?
Agent: Apologies for the confusion. I see that there are 1,023 enrollments, but many of those are double counting the same person. I will need to find unique enrollments somehow, any ideas?
User: Let's go a different route, let's find the members who joined in the past quarter, but have not enrolled in any classes.

_Prior Thought_
To find members who joined in the past quarter but have not enrolled in any classes, we'll need to join the members table with the classes table.
Since there doesn't seem to be a <member_id> column in the classes table, we'll need to go through the <packages_bought> to connect with the <package_id> in the packages table.
Then, we can use the <included_classes> column to connect with the <class_id> or <class_name> in the classes table. Then we'll filter for members who joined in the past quarter using <date_joined> and have no corresponding classes.
This assumes that buying a package automatically enrolls them in the included classes, which may not be the case so we should clarify with the user before proceeding.
We might also want to consider whether members who have not enrolled in any classes are still considered active, or if they should be excluded from the analysis.
Metric: Active Members without Enrollments (Active)

_Output_
```json
{{
  "thought": "Active members can be found directly by using the date_joined column, and enrollments status can be determined by checking the packages_bought column. Therefore, this is a straightforward query.",
  "scope": "query"
}}
```

_Conversation History_
User: How many different SKUs do we have for skin care products?
Agent: The store currently holds 83 different SKUs for skin care products.
User: What's our inventory turnover rate for these products?

_Prior Thought_
I need to calculate the turnover rate for skin care products in our inventory, so I'll focus on key columns that can help derive this metric.
The <CurrentQuantity> and <InitialQuantity> columns from the inventory table seem useful, and I'll filter specifically for the 'Skin Care' Category. The metric will likely involve comparing the initial stock to how much has been sold, which means I'll need to join with the Purchases table to calculate total units sold.
I'll need to be careful about the time period for this calculation, as inventory turnover is typically an annual metric. Since no specific year was mentioned, I'll default to the current year of 2025.
A potential complication is ensuring I'm capturing all skin care product sales and accurately tracking their initial and current inventory levels. The <ProductID> will be crucial for joining the inventory and purchases tables to make this calculation precise.
The goal is to determine how efficiently the skin care product inventory is being managed by measuring how quickly products are being sold and replaced.
Metric: Inventory Turnover Rate (ITR)

_Output_
```json
{{
  "thought": "Inventory Turnover Rate (ITR) is a metric, and we are calculating it by comparing initial and current inventory levels. I am not segmenting the data, so this is a straightforward analysis.",
  "scope": "analysis"
}}
```
---
Now it's your turn! Please determine the scope of the analysis presented in the user's request after thinking carefully about the situation presented in the conversation history.

_Conversation History_
{history}

_Prior Thought_
{thought}
Metric: {metric}

_Output_
"""

metric_verification_prompt = """Given the conversation history and the {metric} metric, please review which variables (if any) have been verified by the user.
Concretely, metrics are constructed as a tree of variable objects, where each variable is either an Expression or a Clause.
The root of the tree is an Expression, which branches out into other variables by combining or comparing other Expressions, until we reach the leaf nodes represented as Clauses.
Whereas Expressions have other variables as children, Clauses contain no children and instead reference specific columns in a table.

Each Expression is composed of a name, verification, variables, and relation, which conveys the relationship between the variables.
Valid relations include: add (+), subtract (-), multiply (*), divide (/), exponent (^), and (&), or (|), less_than (<), greater_than (>), equals (=), conditional (?).
Each Clause is composed of a name, verification (ver), aggregation (agg), table (tab), row, and column (col). They represent the variables that are grounded to actual values in the data.
Valid aggregations include: sum, count, average, top, bottom, min, max, greater_than, less_than, not, equals, empty, filled, constant, all.
For the aggregations of [top, bottom, greater_than, less_than, not, equals, constant], the 'row' field is used to specify the value of N.

While all the fields are important, our focus is currently on verification, which indicates whether the user has confirmed the veracity of the variable.
Specifically, you should be carefully considering whether any of the variables have been implicitly or explicitly verified by the user.
Please start by considering which variables are mentioned in the conversation and their relation to the overall calculation of the metric.
Both the Expression and Clause variables have a name field, which can be used to identify the variable.
Note though that these variable names are not directly accessible to the user, so the user will often reference them by their column names or user-generated names instead.
When a user confirms a variable or if the variable name directly matches a column name, add its internal variable name to the list of verified variables. Otherwise, leave the list empty.
Your entire response should be written in well-formatted JSON including keys for thought (string) and verified (list), with no further explanations after the JSON output.

For example,
---
## Scenario 1
* Tables: amp_events, orders_acct, monthly_inventory
* Columns: event_id, event_type, event_timestamp, source_link, viewed_url, user_id, session_id, device_type, browser_type, ip_address, user_agent in amp_events;
order_id, purchase_item, page_views, payment_amt, payment_method, purchase_timestamp, shipping_addr, billing_addr, is_deleted, last_modified_dt in orders_acct;
item_sk, item_code, sku, cat_id, cat_name, sub_cat_id, price_msrp, price_current, qty_on_hand, qty_allocated, qty_backordered, supplier_id, supplier_name, date_added, last_restock_dt, reorder_point, is_active, discontinued_flag in monthly_inventory;

_Conversation History_
User: Yea, go for it.
Agent: Ok, I will remove all the blank rows. What would you like to do next?
User: What is the CTR for the last 30 days?

_Metric Formula_
{{
  "name": "Click-Through Rate", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "CTR", "verified": false, "relation": "divide",
      "variables": [
        {{"name": "click_event", "ver": false, "agg": "equals", "tab": "amp_events", "col": "event_type", "row": "visit_site"}},
        {{"name": "impression_event", "ver": false, "agg": "filled", "tab": "orders_acct", "col": "page_views"}}
      ]
    }}, {{
      "name": "last_month", "ver": false, "agg": "greater_than", "tab": "amp_events", "col": "event_timestamp", "row": "30 days ago"
    }}
  ]
}}

_Output_
```json
{{
  "thought": "The user has requested a CTR calculation for the past month, but has not provided details about specific columns to use.",
  "verified": []
}}
```

_Conversation History_
User: What is the CTR for the last 30 days?
Agent: Should I consider 'visit_site' as the click event? I can then use the page_views in the orders table to determine the impressions.
User: You can use 'view_product' event for clicks and 'visit_site' event type for impressions. There is no need to join with the orders table.

_Metric Formula_
{{
  "name": "Click-Through Rate", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "CTR", "verified": false, "relation": "divide",
      "variables": [
        {{"name": "click_event", "ver": false, "agg": "equals", "tab": "amp_events", "col": "event_type", "row": "view_product"}},
        {{"name": "impression_event", "ver": false, "agg": "equals", "tab": "amp_events", "col": "event_type", "row": "visit_site"}}
      ]
    }}, {{
      "name": "last_month", "ver": false, "agg": "greater_than", "tab": "amp_events", "col": "event_timestamp", "row": "30 days ago"
    }}
  ]
}}

_Output_
```json
{{
  "thought": "The event_type column is used to filter for both clicks and impressions using different values. The time range Clause has not been explicitly referenced, so we cannot verify it yet.",
  "verified": ["click_event", "impression_event", "CTR"]
}}
```

## Scenario 2
* Tables: CodePathCourses, StudentProgress, LearnerSuccess, MarketingCampaigns
* Columns: CourseID, TrackName, LeadMentorID, TechStackJSON, CohortStartDate, CohortEndDate, WeeklyCommitmentHrs, DeliveryFormat, DifficultyLevel, CurrentEnrollment, MaxCapacity, PreReqSkills, GitHubTemplateURL in CodePathCourses;
ProgressID, CourseID, LearnerUUID, EnrollmentDate, MilestoneStatus, LastSubmissionURL, PeerReviewScore, TuitionPlanType, EmploymentStatus, MentorNotes, ActiveStatus in StudentProgress;
SuccessID, LearnerUUID, CourseID, JobPlacement, SalaryIncrease, TestimonialBody, DateSubmitted, NPSScore, CompanyPlaced, ShowcasePermission in LearnerSuccess;
CampaignID, InitiativeName, CareerSegment, LinkedInAudience, ConversionMetrics, PartnershipType, BudgetAllocated, LeadSource in MarketingCampaigns

_Conversation History_
User: Ok, taking a step back, what are our monthly actives?
Agent: I can take the count of unique students based on LearnerUUID from the StudentProgress table and filter active students. Does that work?
User: Yes, that sounds fine. You should filter for students from the April cohort though.
Agent: Sure, should I be using a CohortStartDate or CohortEndDate?
User: Actually, the right way is to look for the correct UTM parameters in the LastSubmissionURL. The student must also have ActiveStatus listed as true.

_Metric Formula_
{{
  "name": "Monthly Active Users", "verified": false, "relation": "and",
  "variables": [
    {{"name": "unique_users", "ver": true, "agg": "count", "tab": "StudentProgress", "col": "LearnerUUID"}},
    {{"name": "cohort_filter", "ver": false, "agg": "equals", "tab": "StudentProgress", "col": "LastSubmissionURL", "row": "cohort=April"}},
    {{"name": "active_filter", "ver": false, "agg": "equals", "tab": "StudentProgress", "col": "ActiveStatus", "row": true}}
  ]
}}

_Output_
```json
{{
  "thought": "The active filter is described in full, but the cohort filter still has ambiguity since we don't know which UTM parameters to look for, so we will hold off on verifying it.",
  "verified": ["active_filter"]
}}
```

## Scenario 3
* Tables: tbl_customer_master, mkt_campaigns_v2, inv_items_current, promo_discount_rules
* Columns: cust_id, user_uuid, f_name, l_name, customer_email, registration_ts, loyalty_pts_current, loyalty_pts_lifetime, loyalty_tier, shipping_addr, billing_addr, is_deleted, newsletter_flag, last_modified_dt in tbl_customer_master;
campaign_id, campaign_code, promo_name, dt_start, dt_end, segment_code, channel_type, budget_allocated, budget_spent, response_pct, asset_url, is_active, created_by, modified_by, created_dt, modified_dt in mkt_campaigns_v2;
item_sk, item_code, sku, cat_id, cat_name, sub_cat_id, price_msrp, price_current, qty_on_hand, qty_allocated, qty_backordered, supplier_id, supplier_name, date_added, last_restock_dt, reorder_point, is_active, discontinued_flag in inv_items_current;
promo_sk, legacy_promo_id, promo_desc, valid_from, valid_to, discount_pct, discount_amt, min_purchase_amt, excluded_items, coupon_code, times_used, max_uses, single_use_flag, stackable_flag, created_ts, modified_ts in promo_discount_rules

_Conversation History_
User: Actually, total ad spend includes discount amount in addition to budget spent.
Agent: It seems that the promo_name from mkt_campaigns_v2 does not line up with the promo_sk from promo_discount_rules, any ideas on how to join the two tables together?
User: Same as before, if it's running at the same time, they are the same promotion.
Agent: Is the date added column from inv_items_current the right one to use for time, or should I use the valid_from and valid_to columns?
User: I think the valid from and to are more accurate. Anything from March to June should be fine.

_Metric Formula_
{{
  "name": "Return on Ad Spend", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "All_ROAS", "verified": true, "relation": "divide",
      "variables": [
        {{
          "name": "total return", "verified": true, "relation": "multiply",
          "variables": [
            {{"name": "current_price", "ver": true, "agg": "all", "tab": "inv_items_current", "col": "price_current"}},
            {{"name": "current_quantity", "ver": true, "agg": "all", "tab": "inv_items_current", "col": "qty_on_hand"}}
          ]
        }}, {{
          "name": "ad spend", "verified": true, "relation": "add",
          "variables": [
            {{"name": "discount_amount", "ver": true,  "agg": "sum", "tab": "mkt_campaigns_v2", "col": "discount_amt"}},
            {{"name": "budget_spent", "ver": true, "agg": "sum", "tab": "mkt_campaigns_v2", "col": "budget_spent"}},
          ]
        }}
      ]
    }}, {{
      "name": "recent_promos", "verified": false, "relation": "and",
      "variables": [
        {{"name": "after_march", "ver": false, "agg": "greater_than", "tab": "promo_discount_rules", "col": "valid_from", "row": "March"}},
        {{"name": "before_june", "ver": false, "agg": "less_than", "tab": "promo_discount_rules", "col": "valid_to", "row": "June"}}
      ]
    }}
  ]
}}

_Output_
```json
{{
  "thought": "valid_from and valid_to columns are both explicitly mentioned, so recent promos is verified. Since All_ROAS is already verified, this means Return on Ad Spend can now be considered fully verified.",
  "verified": ["after_march", "before_june", "recent_promos", "Return on Ad Spend"]
}}
```

Let's suppose we use the same tables and columns as before for a different metric:
_Conversation History_
User: Do you think people have been using our promo codes at all?
Agent: That's an interesting question. I can count the number of people who used the promo code by looking at the times_used column in the promo_discount_rules table. How does that sound?
User: Yes, let's focus on categories of either 'home appliances' or 'kitchenware'.

_Metric Formula_
{{
  "name": "Targeted Promo Usage", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "Promotion Usage Rate", "verified": false, "relation": "divide",
      "variables": [
        {{"name": "promo_used", "ver": false, "agg": "greater_than", "tab": "promo_discount_rules", "col": "times_used", "row": 0}},
        {{"name": "promo_offered", "ver": false, "agg": "count", "tab": "promo_discount_rules", "col": "promo_sk"}}
      ]
    }}, {{
      "name": "time_frame", "ver": false, "agg": "greater_than", "tab": "promo_discount_rules", "col": "modified_ts", "row": "30 days ago"
    }}, {{
      "name": "category_filter", "verified": false, "relation": "or",
      "variables": [
        {{"name": "home_appliance_filter", "ver": false, "agg": "equals", "tab": "promo_discount_rules", "col": "excluded_items", "row": "home appliances"}},
        {{"name": "kitchenware_filter", "ver": false, "agg": "equals", "tab": "promo_discount_rules", "col": "excluded_items", "row": "kitchenware"}}
      ]
    }}
  ]
}}

_Output_
```json
{{
  "thought": "The user has specified the categories to filter for, and has also approved the method of measuring the whether a promo code was used.",
  "verified": ["home_appliance_filter", "kitchenware_filter", "category_filter", "promo_used"]
}}
```

## Scenario 4
* Tables: booking_requests_v3, discount_programs_active, assistance_tickets_main
* Columns: booking_ref_id, pax_uuid, caregiver_uuid, journey_start_ts, journey_end_ts, payment_amt, pickup_addr, pickup_access_notes, mobility_equipment, booking_status_cd, payment_status_cd, special_instructions_txt in booking_requests_v3;
promo_sk, program_name, partner_org_id, discount_pct, eligible_vehicle_types, requires_medical_docs, docs_required_txt, total_claims, valid_from_dt, valid_to_dt in discount_programs_active;
ticket_sk, customer_book_id, caregiver_uuid, created_ts, severity_level, issue_category_main, medical_emergency_flag, issue_details_encrypted, assigned_agent_id, resolution_code, satisfaction_rating, compliance_review_flag in assistance_tickets_main

_Conversation History_
User: so for example, we can see if those folks usually pay a lot, pay a little, or where they usually come from. Whatever you want to come up.
Agent: OK, I will check for any patterns among ticket issuers starting with payment amounts, pick-up locations, and customer satisfaction. Should we limit our analysis to repeat ticket issuers or some other minimum threshold of tickets?
User: Yea, sure why not. Only people who have filed tickets more than once.
Agent: Got it. To calculate that, I will count the number of tickets per customer and group by customer_book_id. Then, I will filter for customers who have more than one ticket. Does that work?
User: Sounds good.

_Metric Formula_
{{
  "name": "Repeat Ticket Issuers", "verified": false, "relation": "greater_than",
  "variables": [
    {{"name": "tickets_per_customer", "ver": false, "agg": "count", "tab": "assistance_tickets_main", "col": "customer_book_id", "row": "group by customer_book_id"}},
    {{"name": "customer_threshold", "ver": false, "agg": "greater_than", "tab": "assistance_tickets_main", "col": "pax_uuid", "row": "group by pax_uuid"}},
    {{"name": "repeat_threshold", "ver": false, "agg": "constant", "tab": "N/A", "col": "N/A", "row": "1"}}
  ]
}}

_Output_
```json
{{
  "thought": "The user has specified the conditions for repeat ticket issuers, but did not mention pax_uuid. We can verify tickets_per_customer and repeat_threshold, but due to the ambiguity surrounding customer_threshold, we cannot verify Repeat Ticket Issuers.",
  "verified": ["tickets_per_customer", "repeat_threshold"]
}}
```

_Conversation History_
User: What about conversion rate then? Can we look at that for the past week?
Agent: Sure, I can calculate the conversion rate by checking whether payment status is filled and dividing it by the number of unique booking ids. I will look at the past 7 days according to the journey start timestamp. How does that sound?
User: Not really, conversions are when the booking status is paid and the total possible is any non-null booking status. Journey timestamp is good though.

_Metric Formula_
{{
  "name": "Conversion Rate", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "CVR", "verified": false, "relation": "divide",
      "variables": [
        {{"name": "conversion", "ver": false, "agg": "equals", "tab": "booking_requests_v3", "col": "booking_status_cd", "row": "paid"}},
        {{"name": "total_possible", "ver": false, "agg": "filled", "tab": "booking_requests_v3", "col": "booking_status_cd"}}
      ]
    }}, {{
      "name": "time_frame", "ver": false, "agg": "greater_than", "tab": "assistance_tickets_main", "col": "created_ts", "row": "7 days ago"
    }}
  ]
}}

_Output_
```json
{{
  "thought": "The user has specified the CVR conditions in fairly clear detail. It seems like we're done, since the time frame has also been accepted, but the column is incorrect, so we cannot verify it.",
  "verified": ["conversion", "total_possible", "CVR"]
}}
```
---
Now it's your turn to deal with the real case! Please generate a consise thought followed by a JSON-formatted dicts of verified and excluded clauses. For reference, the available data includes:
{valid_tab_col}

_Conversation History_
{history}

_Metric Formula_
{formula}

_Output_
"""

time_period_prompt = """Given the conversation history, we need to determine the time period being referenced for data filtering or updates.
Start by thinking about how the time period is specified in the conversation, then identify the type and corresponding details.
The available types are:
  * range: characterized by a specific start and stop period, possibly includes length and unit (e.g., '04/03/2024 to 04/15/2024')
  * duration: requires the length and unit, even when it is merely implied (e.g., 'last 10 days', '8 hours ago')
  * lookback: time range from some point to present, the length is set to 0.5, while the unit is often inferred (e.g., 'month-to-date', 'start of week')

If the type is unknown due to ambiguity, set it as 'unsure' rather than making any assumptions.
Lengths are written in integers, except for lookbacks where the length is 0.5. When unknown, please set the length to 0. When all time is allowed, set it to -1.
Valid units include: [minute, hour, day, week, month, quarter, year]. Note that these are written as singular.
Your entire response should be in well-formatted JSON with keys for thought (string), type (string), start (string), stop (string), length (int), and unit (string).
There should be no further text or explanations after the JSON output.

For example,
---
_Conversation History_
User: There's clearly some errors with the transactions last Friday.
Agent: I see. What type of errors are you referring to?
User: Something happened with the bounce rate starting from 3 PM and going all the way to 4:30.

_Output_
```json
{{
  "thought": "Errors occurred between 3 PM and 4:30 PM",
  "type": "range",
  "start": "15:00",
  "stop": "16:30",
  "length": 90,
  "unit": "minute"
}}
```

_Conversation History_
User: Our signup numbers seem low.
Agent: Would you like me to check recent signup trends?
User: Yes, show me the last four months.

_Output_
```json
{{
  "thought": "We are looking for signup numbers from the last four months",
  "type": "duration",
  "start": "",
  "stop": "",
  "length": 4,
  "unit": "month"
}}
```

_Conversation History_
User: What's the CTR been doing recently after we started the promotion?
Agent: No problem, I can break this down by channel and age group.
User: There's a new pattern every month, it's hard to keep track of it all

_Output_
```json
{{
  "thought": "The word 'month' is mentioned, but the user is not referring to a specific time period",
  "type": "unsure",
  "start": "",
  "stop": "",
  "length": 0,
  "unit": ""
}}
```

_Conversation History_
User: Let's dive into the campaign performance recently.
Agent: Sure, did you want to look back for the last 14 days?
User: I'd like to see total clicks and impressions since the Black Friday sale was launched.

_Output_
```json
{{
  "thought": "We are looking back from Black Friday till today",
  "type": "lookback",
  "start": "Black Friday",
  "stop": "present",
  "length": 0.5,
  "unit": "day"
}}
```

_Conversation History_
User: Can you pull the latest CPCs broken down for each platform?
Agent: Which time period would you like to see?
User: Just show me last week's data.

_Output_
```json
{{
  "thought": "User wants to look back at the CPCs for the last week, where the length of 1 is implied",
  "type": "duration",
  "start": "",
  "stop": "",
  "length": 1,
  "unit": "week"
}}
```

_Conversation History_
User: Can you tell me the CPAs for anyone who was a part of the 'Midnight Madness' campaign?
Agent: Sure, I see the platformSpend column as a way to calculate the cost, is that right? Also, is there a specific time range to consider?
User: Platform spend sounds right, let's use all the data we have.

_Output_
```json
{{
  "thought": "No time limit should be applied since we are looking at all the data",
  "type": "duration",
  "start": "",
  "stop": "",
  "length": -1,
  "unit": "all"
}}
```

_Conversation History_
User: We have had a lot of visitors to our website recently.
Agent: Yes, there have been 31,677 visitors in the past month. Is this what you're looking for?
User: Actually, can we drill down to 7/14 and 7/28.

_Output_
```json
{{
  "thought": "We are focused on vistors from 7/14 to 7/28 of the current year, this is a period of 2 weeks",
  "type": "range",
  "start": "2025-07-14",
  "stop": "2025-07-28",
  "length": 2,
  "unit": "week"
}}
```

_Conversation History_
User: Janet told me there were some issues with the August orders from 2023.
Agent: Is there a specific date range or set of issues you want to focus on?
User: There was supposed to be a bunch of chargebacks from the 12th through the 21st.

_Output_
```json
{{
  "thought": "We are looking for issues with the August orders from 2023, which is a period of 9 days",
  "type": "range",
  "start": "August 12, 2023",
  "stop": "August 21, 2023",
  "length": 9,
  "unit": "day"
}}
```

_Conversation History_
User: So are we on track to meet the annual revenue projections?
Agent: Are you asking about year-over-year performance or year-to-date performance?
User: Show me the year-to-date performance.

_Output_
```json
{{
  "thought": "User wants to compare actual revenue to projected revenue at the start of the year",
  "type": "lookback",
  "start": "start of year",
  "stop": "present",
  "length": 0.5,
  "unit": "year"
}}
```

---
Now it's your turn! Please determine the time period based on the conversation. For additional context, the names of time-related columns are:
{time_columns}

_Conversation History_
{history}

_Output_
"""

pivot_backfill_prompt = """Given the conversation history and supporting details, determine the mapping of columns from the source table to the target table.
We have queried the source table to derive the target table. For a subset of columns in the source, please find the corresponding column in the target.
Each column in the source table must be mapped to exactly one column in the target table, meaning no source should be left unmapped.

Please start by examining the source and target columns, and then decide how they may be mapped together.
Your entire response should be in well-formatted JSON including keys for thought (string) and mapping (list of dicts), with no further explanations after the JSON output.

For example,
---
Suppose the source and target columns are as follows:
  * Source: ['7-day']
  * Target: ['daily', 'weekly', 'monthly', 'views', 'clicks', 'conversions']

_Output_
```json
{{
  "thought": "The closest match for 7-day is weekly.",
  "mapping": [
    {{"old": "7-day", "new": "weekly"}}
  ]
}}
```

In this next example, suppose the columns are:
  * Source: ['first_name', 'last_name']
  * Target: ['full_name', 'email', 'shopping_cart']

_Output_
```json
{{
  "thought": "first_name and last_name can be combined into full_name.",
  "mapping": [
    {{"old": "first_name", "new": "full_name"}},
    {{"old": "last_name", "new": "full_name"}}
  ]
}}
```

For the third example, suppose the columns are:
  * Source: ['Organization (reviewed)', 'Number of Employees']
  * Target: ['company address', 'contact name', 'company name', 'headcount']

_Output_
```json
{{
  "thought": "Organization most closely aligns with the company name. Number of Employees aligns with headcount.",
  "mapping": [
    {{"old": "Organization (reviewed)", "new": "company name"}},
    {{"old": "Number of Employees", "new": "headcount"}}
  ]
}}
```

For our final example, suppose the columns are:
  * Source: ['Date']
  * Target: ['signup_date', 'signup_count', 'signup_email']

_Output_
```json
{{
  "thought": "The Date column corresponds to the signup_date.",
  "mapping": [
    {{"old": "Date", "new": "signup_date"}}
  ]
}}
```
---
Now it's your turn! Please generate a consise thought followed by the mapping of columns from the source table to the target table.
Remember, the length of the mapping should be equal to the length of the source columns, and be sure to only choose from the target columns.
  * Source: {previous}
  * Target: {target}

For a bit more context, the conversation history is:
{history}

_Output_
"""

sufficient_info_prompt = """Given the conversation history, please think carefully about whether we have enough information to answer the user's request.
Your entire response should be in well-formatted JSON including keys for thought (string) and enough (boolean), with no further explanations after the JSON output.

The different levels of confidence are:
  * 5 - completely certain, we simply need to aggregate the results from one or two columns to get our answer
  * 4 - pretty certain, we can definitely get the answer within a single query that involves joining some tables together
  * 3 - somewhat likely, we would have a write a complex query to get the answer, but it seems like a reasonable task
  * 2 - unlikely, we would have to write a complex query to get the answer, and even then it may not be yield the right answer
  * 1 - not possible, we can point to a missing piece of information that is required to properly answer the question
"""

identify_segmentation_prompt = """Given the conversation history and valid columns, please decide on the most appropriate method of segmenting the data.
Concretely, we are trying to measure {metric} and break it down further by some dimension to aid our analysis.
To do so, we will need a staging column that categorizes the data into the appropriate buckets based on some existing attributes.

Please start by thinking about the goal of the analysis and then the segmentation which would be most helpful to achieve that goal.
The segmentation details include the dimension and type: either numeric (number), temporal (datetime), categorical (text) or proximity (location).
After determining the desired these details, choose the relevant source column(s) used to derive the segmentation column.
If there is not enough information to determine the desired dimension, then output 'unsure' and leave the source list empty.
Your entire response should be in well-formatted JSON including keys for thought (string), dimension (string), type (token), and source (list of tab-col dicts), with no further explanations after the JSON output.

For example,
---
## Scenario 1
* Tables: AdobeAnalytics_final; SalesRecord_Shopify_0812; Klaviyo_Customer_Segments
* Columns: event_time, channel_name, campaign_id, impressions, clicks, paid_spend, landing_page_views, add_to_carts, checkout_starts in AdobeAnalytics_final;
order_id, date_created, customer_id, utm_source, utm_medium, utm_campaign, order_total, items_purchased, shipping_cost, shipping_date, discount_code in SalesRecord_Shopify_0812;
customer_id, email, first_purchase_date, last_purchase_date, total_orders, avg_order_value, campaign_analytics_id, segment_tags, demographic_tag, email_engagement_score, subscription_status in Klaviyo_Customer_Segments

_Conversation History_
User: Which channels actually cost us the most?
Agent: Are you asking about the total ad spend per channel?
User: Not exactly, I want to know the channels with the highest ad spend in terms of customer acquisition

_Output_
```json
{{
  "thought": "The channel is the most appropriate dimension for segmentation, which is a categorical type. The source column is either utm_source or channel_name.",
  "dimension": "channel",
  "type": "categorical",
  "source": [
    {{"tab": "SalesRecord_Shopify_0812", "col": "utm_source"}},
    {{"tab": "AdobeAnalytics_final", "col": "channel_name"}}
  ]
}}
```

_Conversation History_
User: yea, the paid spend from adobe analytics sounds correct
Agent: Facebook Ads has the highest customer acquisition cost, mainly due to a of low number of sales. Please see the table for more.
User: But maybe the sales are high value items? Maybe each buyer has high LTV?
Agent: Certainly, that could be the case. Should I calculate the LTV for customers from Facebook?
User: Not sure how we would even do that, can we just get the ROAS for Facebook instead? I want this broken down by month for the past 12 months.

_Output_
```json
{{
  "thought": "The user directly told us to use month as the dimension, which is temporal. There are multiple columns related to time, but 'event_time' is most appropriate because we are already working with the Adobe Analytics table.",
  "dimension": "month",
  "type": "temporal",
  "source": [
    {{"tab": "AdobeAnalytics_final", "col": "event_time"}}
  ]
}}
```

## Scenario 2
* Tables: booking_requests_v3; discount_programs_active; assistance_tickets_main
* Columns: booking_ref_id, pax_uuid, caregiver_uuid, journey_start_ts, journey_end_ts, payment_amt, pickup_addr, pickup_access_notes, mobility_equipment, booking_status_cd, payment_status_cd, special_instructions_txt in booking_requests_v3;
promo_sk, program_name, partner_org_id, discount_pct, eligible_vehicle_types, requires_medical_docs, docs_required_txt, total_claims, valid_from_dt, valid_to_dt in discount_programs_active;
ticket_sk, customer_book_id, caregiver_uuid, created_ts, severity_level, issue_category_main, medical_emergency_flag, issue_details_encrypted, assigned_agent_id, resolution_code, satisfaction_rating, compliance_review_flag in assistance_tickets_main

_Conversation History_
User: we should try to figure out where our most problematic customers are coming from based on their pick-up locations.
Agent: Just to be clear, what do you mean by problematic customers?
User: so problematic customers are the ones who cause the most trouble, like filing lots of customer support tickets.
Agent: OK, let's look for repeat ticket issuers and then segment by pick-up locations. Repeat here means more than once, or should I use some other threshold?
User: Yea, sure why not. More than once is fine.

_Output_
```json
{{
  "thought": "The user wants to segment by pick-up locations, which is a proximity type. The source column is pickup_addr.",
  "dimension": "pickup_location",
  "type": "proximity",
  "source": [
    {{"tab": "booking_requests_v3", "col": "pickup_addr"}}
  ]
}}
```

## Scenario 3
* Tables: FFC Membership; ClassSchedule; Flexfit Fitness Profile
* Columns: MemberID, FirstName, LastName, Email, Phone, Address, City, State, PostalCode, MembershipType, RemainingClasses, PaymentMethod, LastPaymentDate, EmergencyContactName, EmergencyContactPhone, DateOfBirth, JoinDate, ReferredBy in FFC Membership;
ClassID, ClassName, Instructor, Room, DayOfWeek, StartTime, EndTime, MaxCapacity, CurrentEnrollment, Description, DifficultyLevel, EquipmentNeeded, IsSpecialEvent, IsCancelled, SubstituteInstructor in ClassSchedule;
ProfileID, MemberID, Height, Weight, BodyFatPercentage, BMI, FitnessGoals, HealthConditions, Injuries, PreferredWorkout, AssessmentDate, StepsAverage, RestingHeartRate, MaxHeartRate, VO2Max, StrengthLevel, CardioLevel, MobilityLevel in Flexfit Fitness Profile

_Conversation History_
User: For any given class type, how many people convert from free trial into full time membership
Agent: It sounds like you want to calculate the conversion rate of free trial memberships, segmented by class type, is that right? I will count free trials as the '1-Week Free Trial' membership, and the full time memberships as everything else?
User: Actually, you should exclude one-time passes from the paid memberships. They aren't expensive enough to be considered as full-time. In fact, let's ignore the yoga-only passes as well.

_Output_
```json
{{
  "thought": "The user wants to segment by class type, which is categorical. We can probably derive the class type from the data in the ClassName column.",
  "dimension": "class_type",
  "type": "categorical",
  "source": [
    {{"tab": "ClassSchedule", "col": "ClassName"}}
  ]
}}
```

_Conversation History_
User: Yea, that sounds good. Go ahead.
Agent: it seems our average member has a resting heart rate of 63.2 bpm, a max heart rate of 175.6 bpm, and a VO2Max of 45.6 ml/kg/min.
User: How would this look if we broke it down by age groups?

_Output_
```json
{{
  "thought": "The user wants to segment by age groups, which is numeric. We can probably derive the age groups from the data in the DateOfBirth column.",
  "dimension": "age_group",
  "type": "numeric",
  "source": [
    {{"tab": "FFC Membership", "col": "DateOfBirth"}}
  ]
}}
```

## Scenario 4
* Tables: MarketingQualifiedLeads; Subscriptions; RenderActivity
* Columns: LeadID, CompanyName, ContactEmail, IndustryType, AnnualAdSpend, TeamSize, LeadSource, DemoScheduled, PocStatus, SalesRepID, ClientUUID, LastTouchpoint in MarketingQualifiedLeads;
SubscriptionID, ClientUUID, PlanTier, GPUAllocation, MonthlyQuota, BillingCycle, PaymentStatus, OverageRate, ApiKeyHash, ContractStatus in Subscriptions;
ReleaseVersion, StartTimestamp, MaxResolution, ClientUUID, ProjectName, RenderType, FeaturesList, ProcessingTime, OutputFormat, ComputeUnits, QueuePriority, RenderStatus, ErrorTrace in RenderActivity

_Conversation History_
User: I want to extract the Sales Qualified Leads out of the MQLs, which are basically the most high value prospects to go after.
Agent: Sure, how do you want to define high value prospects?
User: Ok, so there are three main criteria: (1) they should be have annual revenue > 100K and team size > 50 (2) they should have a demo scheduled or completed (3) they should have an active pilot going on
Agent: No problem, these are the Sales Qualified Leads based on your criteria.  Does this look right to you?
User: Yea, but can we break this down into industry type? Also, we should include the contact email in the results as well.

_Output_
```json
{{
  "thought": "The user wants to segment by industry type, which is categorical. This obviously comes from the IndustryType column.",
  "dimension": "industry_type",
  "type": "categorical",
  "source": [
    {{"tab": "MarketingQualifiedLeads", "col": "IndustryType"}}
  ]
}}
```

_Conversation History_
User: There's big spike in processing time a few days ago, what happened?
Agent: There are a number of potential causes, such as server issues or high demand. Is there anything that you suspect might have caused the spike?
User: I would start by grabbing the average processing time per day. We can then check if there's any correlation with the new features list and error trace.
Agent: Sure, in order to form the comparison to the new features, I will need to extract them into a new column, does that sound right? Similarly, is there any specific message I should be checking within the ErrorTrace?
User: Let's go with the errors first. We should bucket them into either: critical errors (anything with the word 'downtime'), configuration errors (invalid settings, incompatible render types), or memory errors (quota exceeded, out of memory)

_Output_
```json
{{
  "thought": "We are bucketing errors into three categories: critical, configuration, and memory. The source column is ErrorTrace.",
  "dimension": "error_type",
  "type": "categorical",
  "source": [
    {{"tab": "RenderActivity", "col": "ErrorTrace"}}
  ]
}}
```
---
Now it's your turn! Please think carefully about the segmentation dimension that is most appropriate for grouping the {metric} metric.
For our real case, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

determine_buckets_prompt = """Given the conversation history and supporting details, please decide on the most appropriate method of segmenting the data.
Concretely, we are trying to break down the {metric} metric by {dimension} to aid our analysis.
To do so, we will first create a staging column that categorizes the data into the appropriate buckets so our query can easily group by this dimension.

Please start by thinking about the key factors that should be considered during segmention:
  * how many segments to create and how to name them
  * should we be filtering out any rows that don't fit into any of the segments
  * what conversions or calculations are necessary to derive the segmentation column
  * how do we want to handle edge cases or outliers, such as null or missing values

Next, devise a clear and concise name for the segmentation column that matches the naming conventions of the other columns in the table:
  * Should be as short as possible, without repeating the name of any existing column
  * Do other columns use underscores, spaces, or camelCase? Titlecase or lowercase? Follow the same pattern.
  * Prefer intuitive names that hint at the column's purpose (ie. pricing_tiers) rather than literal names describing the contents (ie. dollar_amount_greater_than_1000)
  * If the column is a boolean, then it should be named as a question (ie. is_promising_lead) rather than a statement (ie. promising_leads)

Finally, outline the steps to creating the segmentation column by highlighting the most important points from your thought process.
If there is not enough information to determine the desired segmentation, then set the table and column names as 'unsure' and leave the list of steps empty.
If the segmentation can be directly pulled from an existing column, then set that as the column name and create only a single step of 'none'.
Your entire response should be in well-formatted JSON including keys for thought (string), table (string), column (string), and steps (list of strings), with no further explanations after the JSON output.

For example,
---
_Conversation History_
User: Basically, when the user stays on a product page for more than a minute, we show them a pop-up asking them to sign up for our newsletter. So there's a sign-up rate associated with that.
Agent: Got it, I will use a sign-up as a conversion event and the number of times the pop-up is shown as the number of visits. Does that sound right?
User: yea, but we also want to check if (a) did they have anything in their shopping cart and (b) what variation they saw based on the split test assignment

_Supporting Details_
Table: Newsletter_signup_popup
Source columns: items_in_cart, split_test_variation
Segment type: categorical
Metric: Conversion Rate (CVR)

Suppose for this example that the column data showed:
  * items_in_cart holds a number
  * split_test_variation as a string describing the headline of the popup
  * the headlines we care about are: "Please sign up for our newsletter to get exclusive deals!", "Our new Duo Mascara makes a bold statement", and "Save 15% on your next purchase when you sign up now!"

_Output_
```json
{{
  "thought": "The existing columns are lowercase with underscores, so a good name could be cart_split_segments.",
  "table": "Newsletter_signup_popup",
  "column": "cart_split_segments",
  "steps": [
    "divide into 2 x 3 = 6 segments",
    "first dimension: items_in_cart > 0 or items_in_cart == 0",
    "second dimension: split_test_variation = please, mascara, or discount",
    "category names can be: hasItem_please, hasItem_mascara, hasItem_discount, emptyCart_please, emptyCart_mascara, emptyCart_discount"
  ]
}}
```

_Conversation History_
User: Customers have been complaining about that our 3D printers break down a lot recently. Can you help me find out when the most errors happen?
Agent: Sure, I can look at the error logs and check when the most errors happen. Does that sound right?
User: That sounds good. Please break it down by hour.

_Supporting Details_
Table: bambu_labs_printer_logs
Source columns: error type, fab timestamp
Segment type: temporal
Metric: Error Rate (ER)

_Output_
```json
{{
  "thought": "The existing columns are lowercase with spaces, so a good name could be 'error hour'.",
  "table": "bambu_labs_printer_logs",
  "column": "error hour",
  "steps": [
    "bucket into hourly segments",
    "filter for error_type is not null",
    "convert each fab_timestamp to a format which rounds to the nearest hour"
  ]
}}
```

_Conversation History_
User: That's not necessarily true, older people spend less time on the app, but they have more money to spend.
Agent: You make a good point, should I segment the average order value by age to find out?
User: Let's focus on just the in-game purchase amount rather than total purchase value

_Supporting Details_
Table: SteamAccountPurchases
Source columns: Age, PurchaseAmount, PurchaseType
Segment type: numeric
Metric: Average Order Value (AOV)

_Output_
```json
{{
  "thought": "The existing columns are titlecase with no spaces, so a good name could be 'AgeGroup'.",
  "table": "SteamAccountPurchases",
  "column": "AgeGroup",
  "steps": [
    "bucket into 6 segments: child, teen, young_adult, adult, middle_age, senior",
    "child is anyone under 13",
    "teen ranges from 13 to 17",
    "young_adult ranges from 18 to 25",
    "adult ranges from 26 to 34",
    "middle_age ranges from 35 to 49",
    "senior is anyone 50 and older"
  ]
}}
```

_Conversation History_
User: What is the performance of our advertising lately?
Agent: Just to be clear, are you looking for the ROAS, CTR, or some other metric? Also, is there a specific time period you want to focus on?
User: I'd like to drill down into the ROAS for any campaigns that ran in October.

_Supporting Details_
Table: GA4_Report_-_From_Dave
Source columns: Ad_Campaign, Paid_Spend_USD
Segment type: categorical
Metric: Return on Ad Spend (ROAS)

_Output_
```json
{{
  "thought": "The existing columns are capitalized with underscores, so a good name could be 'Ad_Campaign_Segments'.",
  "table": "GA4_Report_-_From_Dave",
  "column": "Ad_Campaign_Segments",
  "steps": [
    "focus on five campaigns: FrostySeas_Q3_2024, HolidayEscape_Premium_Push, Cruise-XMAS-Blitz-2024, WinterWaves_LastMin_Deal, Nov_Luxury_Cruise_Flash_Sale ",
    "category names can be: frosty_seas, holiday_escape, christmas_blitz, winter_waves, nov_luxury_cruise",
    "anything else should be mapped to null"
  ]
}}
```

_Conversation History_
User: I wonder if the kiosks near our stores are generating more revenue. Can you figure out the revenue per kiosk split by whether they are near a store or not?
Agent: Sure, I can sum up all the DailyPaymentsReceived for each kiosk location. I can use KioskLocation to determine whether the kiosk is located, but I'll need to know where the stores are located.
User: Our stores are located in Bellevue, Seattle, and Tacoma.

_Supporting Details_
Table: electronicRepairRevenue
Source columns: kioskLocation, dailyPaymentsReceived
Segment type: proximity
Metric: Average Revenue Per Kiosk (ARPK)

_Output_
```json
{{
  "thought": "The existing columns are capitalized as camelCase without spaces, so a good name could be 'isStoreNearby'.",
  "table": "electronicRepairRevenue",
  "column": "isStoreNearby",
  "steps": [
    "bucket the kiosks into two segments, either near a store or not nearby a store",
    "our stores are located in Bellevue, Seattle, and Tacoma",
    "if the kiosk is in one of these cities, it is near_store, otherwise it is not_nearby"
  ]
}}
```

_Conversation History_
User: Do you think it's maybe a seasonality issue? I'm surprised that everyone is getting fewer calls all at once.
Agent: No problem, I can calculate the number of calls scheduled per sales rep and then group by month. Does that sound right?
User: Yes, what does that tell us?

_Supporting Details_
Table: Pardot Leads (reviewed)
Source columns: Call Scheduled Date
Segment type: temporal
Metric: Calls Per Sales Rep (CPSR)

_Output_
```json
{{
  "thought": "The existing columns are titlecase with spaces, so a good name could be 'Calls By Month'.",
  "table": "Pardot Leads (reviewed)",
  "column": "Calls By Month",
  "steps": [
    "bucket into monthly segments",
    "filter out any leads without a Call Scheduled Date",
    "convert each remaining Call Scheduled Date to the nearest month"
  ]
}}
```
---
Now it's your turn! Think carefully about analysis the user wants to perform and then outline the steps to create the segmentation column.
Make sure the name of the new column matches the naming conventions of the other columns in the table. The valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Supporting Details_
Table: {source_tab}
Source columns: {source_cols}

_Output_
"""

metric_completion_prompt = """You are an outstanding data analyst who is exceptionally skilled at writing SQL queries.
In accordance with the user's request in the conversation, we want to calculate the {metric} metric.
For now, {variable_phrase}. 

Given the SQL template containing a CTE, please come up with an alias for the calculated column representing the {variable_name}, then write the SQL query for deriving that value.
The only exception is if the columns to use are not clear, in which case the alias should be set to 'unsure' and the query should be replaced with a clarification question.
Such sitiations arise when there are no obviously matching columns (eg. a foreign key is missing for a join) or conversely, multiple conflicting columns that could be used (eg. many columns with similar names).
Additionally, pay close attention to the provided template since you may need to reference previously generated columns, which is especially common when calculating ratios.
In all cases, your entire response should be written as well-formatted SQL, with no further explanations before or after the SQL output.

For example,
---
## E-commerce Online Advertiser Scenario
* Tables: GoogleAds_Q3; SalesRecord_Shopify_0812; Product_Details
* Columns: gAd_ID, clickCount, campaignInitDate, campaignTermDate, userActivity, adBounceRate, adSpend, adContentCode, referrerURL in GoogleAds_Q3;
orderRef, prodSKU, saleDate, acquisitionCost, buyerID, gAdRef, revenueGenerated, unitsMoved, fulfillmentStatus, customerNotes in SalesRecord_Shopify_0812;
SKU, itemName, itemCategory, retailPrice, totalCost, stockLevel in Product_Details

_Example 1_
We want to calculate conversions as part of conversion rate using the template:
```sql
WITH LinkedInVisitors AS (
  SELECT DISTINCT buyerID
  FROM SalesRecord_Shopify_0812
  JOIN GoogleAds_Q3 ON SalesRecord_Shopify_0812.gAdRef = GoogleAds_Q3.gAd_ID
  WHERE GoogleAds_Q3.referrerURL LIKE '%linkedin%'
  AND GoogleAds_Q3.userActivity = 'clicks'
), <conversions_alias> AS (
  <conversions_SQL_code>,
)
SELECT
  <conversions_result>,
  <visits_result>,
  COALESCE(<conversions_result> * 1.0) / NULLIF(<visits_result>, 0), 0) AS CVR
FROM <CTE_alias>;
```
We previously determined that we should start by filtering for users coming from LinkedIn, likely by using the referrerURL column.
We also need to filter for the past week, which can be found by joining against the saleDate column.
Lastly, visits and conversions are both activity types in userActivity, using the the 'clicks' and 'purchase' values respectively.

_Output_
```sql
-- Alias: LinkedInPurchases
SELECT buyerID
FROM SalesRecord_Shopify_0812
JOIN GoogleAds_Q3 ON SalesRecord_Shopify_0812.gAdRef = GoogleAds_Q3.gAd_ID
WHERE GoogleAds_Q3.userActivity = 'purchase'
AND SalesRecord_Shopify_0812.saleDate >= CURRENT_DATE - INTERVAL '1 week'
AND SalesRecord_Shopify_0812.buyerID IN (SELECT buyerID FROM LinkedInVisitors)
```

_Example 2_
We want to calculate cost as part of cost-per-click using the template:
```sql
WITH ClickCounts AS (
  SELECT SUM(clickCount) AS totalClicks
  FROM GoogleAds_Q3
  WHERE userActivity = 'clicked_ad'
), <cost_alias> AS (
  <cost_SQL_code>,
)
SELECT
  <cost_result>,
  <clicks_result>,
  COALESCE(<cost_result> * 1.0 / NULLIF(<clicks_result>, 0), 0) AS CTR
FROM <CTE_alias>;
```
We previously determined that both adSpend and totalCost are plausible options for cost, but I will choose adSpend because it comes from the same table as clickCount, which is verified.
When performing calculations, we should limit cost to only those that are directly attributable to clicks.

_Output_
```sql
-- Alias: GoogleSpend
SELECT SUM(adSpend) AS totalSpend
FROM GoogleAds_Q3
WHERE userActivity = 'clicked_ad'
```

## Enterprise Data Security Scenario
* Tables: HubspotCRM; TransactionHistory; InteractionLogs
* Columns: cust_id, signup_date, cust_name, email, region, tracking_id, channel, acct_status in HubspotCRM;
trans_id, cust_id, trans_date, product_id, amount, trans_type, license_fee, service_charge, maintenance_income in the TransactionHistory;
interaction_id, cust_id, interact_date, interact_type, interact_duration, issue_resolved, expenses in the InteractionLogs

_Example 3_
We want to calculate total customers as part of retention rate using the template:
```sql
WITH <total_customers_alias> AS (
  <total_customers_SQL_code>,
), <active_customers_alias> AS (
  <active_customers_SQL_code>,
)
SELECT
  <active_customers_result>,
  <total_customers_result>,
  COALESCE(<active_customers_result> * 1.0 / NULLIF(<total_customers_result>, 0), 0) AS retention_rate
FROM <CTE_alias>;
```
We previously determined that we should start by filtering for the west coast, likely using the region column. There doesn't seem to be a retention column, so we will need to calculate the variables ourselves.
Signup date is likely the best way to determine if someone is a customer. Grouping by customer id will allow us to form the base of total customers.
We can then use the transaction date to determine whether that customer is active in the last month.

_Output_
```sql
-- Alias: WestCoastCustomers
SELECT COUNT(DISTINCT HubspotCRM.cust_id)
FROM HubspotCRM
WHERE HubspotCRM.region = 'West'
AND HubspotCRM.signup_date >= CURRENT_DATE - INTERVAL '1 year'
```

_Example 4_
We want to calculate revenue as part of return on investment using the template:
```sql
WITH <revenue_alias> AS (
  <revenue_SQL_code>,
), <cost_alias> AS (
  <cost_SQL_code>,
)
SELECT
  <revenue_result>,
  <cost_result>,
  COALESCE(<revenue_result> * 1.0 / NULLIF(<cost_result>, 0), 0) AS ROI
FROM <CTE_alias>;
```
We previously determined that nothing is verified, so we can go with sum of expenses as 'costs'.  However, many reasonable options exist for revenue, including license_fee, service_charge, or maintenance_income.
We also need to filter for last quarter, which was calculated using interact_date in the prior query, so I will use the same column this time as well.

_Output_
```sql
-- Alias: unsure
-- Question: Should I use license_fee, service_charge, or maintenance_income to calculate revenue?
```

_Example 5_
We want to calculate active customers as part of retention rate using the template:
```sql
WITH WestCoastCustomers AS (
  SELECT COUNT(DISTINCT HubspotCRM.cust_id)
  FROM HubspotCRM
  WHERE HubspotCRM.region = 'West'
  AND HubspotCRM.signup_date >= CURRENT_DATE - INTERVAL '1 year'
), <active_customers_alias> AS (
  <active_customers_SQL_code>,
)
SELECT
  <active_customers_result>,
  <total_customers_result>,
  COALESCE(<active_customers_result> * 1.0 / NULLIF(<total_customers_result>, 0), 0) AS retention_rate
FROM <CTE_alias>;
```
We previously determined that we should start by filtering for the west coast, likely using the region column. There doesn't seem to be a retention column, so we will need to calculate the variables ourselves.
Signup date is likely the best way to determine if someone is a customer. Grouping by customer id will allow us to form the base of total customers.
We can then use the transaction date to determine whether that customer is active in the last month.

_Output_
```sql
-- Alias: ActiveLastMonth
SELECT COUNT(WestCoastCustomers.cust_id) AS active_customers
FROM WestCoastCustomers
JOIN TransactionHistory ON TransactionHistory.cust_id = WestCoastCustomers.cust_id
AND TransactionHistory.trans_date >= CURRENT_DATE - INTERVAL '1 month'
```

## Mobile Workout Community Scenario
* Tables: AdobeAnalytics_final; SubscriptionMembership; Canva Content (revised); VendorExpenses
* Columns: campaign_id, ad_platform, ad_spend, ad_type, ad_copy, user_activity, view_count, cost_per_click in AdobeAnalytics_final;
member_id, subscription_date, renewal_date, subscription_tier, monthly_fee, activity, member_status in SubscriptionMembership;
video_id, trainer_id, video_campaign_id, creation_date, video_type, trainer_fee, impressions in Canva Content (revised);
vendor_id, service_provided, expense_date, expense_amount, vendor_category in VendorExpenses

_Example 6_
We want to calculate impressions as part of click-thru rate.
```sql
WITH GoogleImpressions AS (
  SELECT DISTINCT view_count
  FROM AdobeAnalytics_final
  WHERE ad_platform = 'Google'
), <clicks_alias> AS (
  <clicks_SQL_code>,
)
SELECT
  <clicks_result>,
  <impressions_result>,
  COALESCE(<clicks_result> * 1.0) / NULLIF(<impressions_result>, 0), 0) AS CTR
FROM <CTE_alias>;
```
We previously determined that we should start by filtering for Google using the ad_platform column, along with view_count to determine impressions.
I can't find a single column to represent cost, but the user has verified that we can use cost_per_click in conjunction with ad_spend to derive an approximate number of clicks.

_Output_
```sql
-- Alias: GoogleClicks
SELECT SUM(ad_spend) / cost_per_click AS total_clicks
FROM AdobeAnalytics_final
WHERE ad_platform = 'Google'
AND GoogleImpressions.view_count > 0
```

_Example 7_
We want to calculate costs are part of profit using the template:
```sql
WITH <costs_alias> AS (
  <costs_SQL_code>,
), <revenue_alias> AS (
  <revenue_SQL_code>,
)
SELECT
  <costs_result>,
  <revenue_result>,
  COALESCE(<revenue_result> - <costs_result>, 0) AS profit
FROM <CTE_alias>;
```
We have previously determined that revenue can be derived from monthly_fee. Possible costs include expense_amount and ad_apend, but I will choose expense_amount because we are discussing videos.
We also need to remember to filter for view count over 1000.

_Output_
```sql
-- Alias: VideoCosts
SELECT SUM(expense_amount) AS total_expenses
FROM VendorExpenses
WHERE vendor_category = 'video'
AND expense_date >= CURRENT_DATE - INTERVAL '1 month'
```

_Example 8_
We want to calculate impressions as part of click-thru rate.
```sql
WITH <impressions_alias> AS (
  <impressions_SQL_code>,
), <clicks_alias> AS (
  <clicks_SQL_code>,
)
SELECT
  <clicks_result>,
  <impressions_result>,
  COALESCE(<clicks_result> * 1.0) / NULLIF(<impressions_result>, 0), 0) AS CTR
FROM <CTE_alias>;
```
We previously determined that we should start by filtering for Google, likely using the ad_platform column AdobeAnalytics_final.
That table offers view_count to determine impressions, but there is also an impressions column in Canva Content (revised), so we should clarify which one to use.
There isn't a cost column, but cost_per_click and ad_spend can be used to approximate the number of clicks, which we should review with the user.

_Output_
```sql
-- Alias: unsure
-- Question: Should I use view_count or impressions to calculate impressions? Also, is it appropriate to use divide ad_spend by cost_per_click to calculate clicks?
```
---
## Current Scenario
Please think carefully about your answer, but remember to only respond with the alias and the SQL query with no further explanations.
If the thought mentions any uncertainty, please ask a clarification question rather than proceeding with code generation.
When writing the query or question, only select from the following valid columns:
{valid_tab_col}

For our current case, recall we want to calculate {variable_name} as part of {metric}. We are given the template:
{template}
We have already determined that {prior_thought}

_Output_
"""

segment_completion_prompt = """Given a Formula description, your task is to generate the SQL query for calculating the associated metric.
As context, a Formula is a tree of variable objects which can be either Expressions or Clauses, starting with a root Expression.
Each Expression is composed of a name, variables, and relation, which conveys the relationship between those variables.
On the other hand, Clauses are leaf nodes that contain no children and instead reference specific columns in a table. Each one is composed of a name, aggregation (agg), table (tab), row, and column (col).

Valid relations include: add (+), subtract (-), multiply (*), divide (/), exponent (^), and (&), or (|), less_than (<), greater_than (>), equals (=), conditional (?).
Valid aggregations include: sum, count, average, top, bottom, min, max, greater_than, less_than, not, equals, empty, filled, constant, all.
For the aggregations of [top, bottom, greater_than, less_than, not, equals, constant], the 'row' field is used to specify the value of N.
Whereas not/equals are used to filter the column by a specific value, the empty/filled relations are used to filter for null or non-null values, respectively.

For our primary case, we want to calculate the {metric} segmented by {dimension}. This means that each row in the output should represent a unique {dimension}.
To support this calculation, we have first created '{target_col}' as a staging column, which should be used at some point to group the data.
While the Formula has been verified by the user, it does not directly translate to SQL, so you may need to make some minor adjustments.
For example, the 'equals' aggregation does not have to strictly match the row value, but can also be used to filter for rows where the column contains a specific value.
Alternatively, the operations may need to be applied row-wise or column-wise depending on the situation. Use your best judgement as an experienced data analyst.

Please start by thinking about any tricky aspects of joining the tables or performing the operations, so that you can keep them in mind while writing the query.
These thoughts should be written as a SQL comment starting with '-- Thought:' at the top of the query.
Next, follow the Formula description and segmentation details to write the SQL query for the metric, including any necessary CTEs for intermediate calculations.
If the columns to use are not clear, then replace the query with a clarification question instead that starts with '-- Question:'.
Such sitiations arise when there are no obviously matching columns (eg. a foreign key is missing for a join) or conversely, multiple conflicting columns that could be used (eg. many columns with similar names).
In all cases, your entire response should be written as well-formatted SQL, with no further explanations before or after the SQL output.
Let's consider a few example scenarios, and then tackle the real case.

---
## Scenario 1 - E-commerce Online Advertiser
* Tables: AdobeAnalytics_final; SalesRecord_Shopify_0812; Klaviyo_Email_Lists
* Columns: event_date, channel, campaign_id, impressions, clicks, conversions, interstitial_displays, time_on_site, landing_page_views, added_to_cart, checkout_starts in AdobeAnalytics_final;
order_id, date_created, customer_id, utm_source, utm_medium, utm_campaign, order_total, items_purchased, shipping_cost, shipping_date, discount_code in SalesRecord_Shopify_0812;
customer_id, email, first_purchase_date, last_purchase_date, total_orders, avg_order_value, campaign_analytics_id, user_tags, join_channel, email_engagement_score, subscription_status in Klaviyo_Email_Lists

_Formula_
{{
  "name": "Conversion Rate", "relation": "divide",
  "variables": [
    {{
      "name": "sign_ups_from_website", "relation": "and",
      "variables": [
        {{"name": "email_signup", "agg": "count", "tab": "Klaviyo_Email_Lists", "col": "customer_id"}},
        {{"name": "from_website", "agg": "equals", "tab": "Klaviyo_Email_Lists", "col": "join_channel", "row": "popup_on_cart_page"}}
      ]
    }}, {{
      "name": "popup_views", "agg": "sum", "tab": "AdobeAnalytics_final", "col": "interstitial_displays"
    }}
  ]
}}
Segmentation: cart_split_segments in SalesRecord_Shopify_0812 (categorical)

_Conversation History_
User: Basically, when the user stays on a product page for more than a minute, we show them a pop-up asking them to sign up for our newsletter. So there's a sign-up rate associated with that.
Agent: Got it, I will use a sign-up as a conversion event and the number of times the pop-up is shown as the number of visits. Does that sound right?
User: yea, but we also want to check if (a) did they have anything in their shopping cart and (b) what variation they saw based on the split test assignment
Agent: I can calculate conversion rate based on conversions divided by the number of time the pop-up was displayed. Is that right?
User: For signups, we go with anyone who is on the mailing list, and then count those that have 'popup_on_cart_page' as the channel where they joined.

_Output_
```sql
-- Thought: There is no direct join from AdobeAnalytics to Shopify, so we will need to join through Klaviyo by first connecting Adobe campaign_id to Klaviyo campaign_analytics_id, then Klaviyo customer_id to Shopify customer_id.
WITH popup_view_counts AS (
  SELECT srs.cart_split_segments, SUM(aaf.interstitial_displays) AS popup_views
  FROM AdobeAnalytics_final aaf
  JOIN Klaviyo_Email_Lists kel ON aaf.campaign_id = kel.campaign_analytics_id
  JOIN SalesRecord_Shopify_0812 srs ON kel.customer_id = srs.customer_id
  GROUP BY srs.cart_split_segments
),
sign_ups_from_website AS (
  SELECT srs.cart_split_segments, COUNT(kel.customer_id) AS sign_ups
  FROM Klaviyo_Email_Lists kel
  JOIN SalesRecord_Shopify_0812 srs ON kel.customer_id = srs.customer_id
  WHERE kel.join_channel = 'popup_on_cart_page'
  GROUP BY srs.cart_split_segments
)
SELECT
  pvc.cart_split_segments,
  COALESCE(sfw.sign_ups, 0) AS sign_ups,
  pvc.popup_views,
  COALESCE(sfw.sign_ups * 1.0 / NULLIF(pvc.popup_views, 0), 0) AS conversion_rate
FROM popup_view_counts pvc
LEFT JOIN sign_ups_from_website sfw ON pvc.cart_split_segments = sfw.cart_split_segments
ORDER BY conversion_rate DESC;
```

_Formula_
{{
  "name": "Return on Ad Spend", "relation": "and",
  "variables": [
    {{
      "name": "ROAS", "relation": "divide",
      "variables": [
        {{
          "name": "total_return", "relation": "multiply",
          "variables": [
            {{"name": "num_orders", "agg": "all", "tab": "SalesRecord_Shopify_0812", "col": "total_orders"}},
            {{"name": "revenue_per_order", "agg": "all", "tab": "SalesRecord_Shopify_0812", "col": "avg_order_value"}}
          ]
        }}, {{
          "name": "ad_spend", "agg": "sum", "tab": "AdobeAnalytics_final", "col": "paid_spend"
        }}
      ]
    }}, {{
      "name": "facebook_filter", "agg": "equals", "tab": "AdobeAnalytics_final", "col": "channel", "row": "Facebook"
    }}, {{
      "name": "past_year", "agg": "greater_than", "tab": "AdobeAnalytics_final", "col": "event_date", "row": "12 months ago"
    }}
  ]
}}
Segmentation: event_month in AdobeAnalytics_final (temporal)

_Conversation History_
User: But maybe the sales are high value items? Maybe each buyer has high LTV?
Agent: Certainly, that could be the case. Should I calculate the LTV for customers from Facebook?
User: Not sure how we would even do that, can we just get the ROAS for Facebook instead? I want this broken down by month for the past 12 months.
Agent: I am planning to calculate ROAS by multiplying the number of orders by the average order value, then dividing by the ad spend. I will then split the results by month. Does that sound right?
User: Yes, make sure to focus on just the last year of Facebook data.

_Output_
```sql
-- Thought: We should make sure to limit to orders that were generated by Facebook ads in the past year, so that we don't overestimate the total return.
WITH monthly_data AS (
  SELECT
    DATE_TRUNC('month', aaf.event_date) AS event_month,
    SUM(kel.total_orders * kel.avg_order_value) AS total_return,
    SUM(aaf.paid_spend) AS total_ad_spend
  FROM AdobeAnalytics_final aaf
  JOIN Klaviyo_Email_Lists kel ON aaf.campaign_id = kel.campaign_analytics_id
  WHERE aaf.channel = 'Facebook'
    AND aaf.event_date >= DATEADD(month, -12, CURRENT_DATE)
  GROUP BY DATE_TRUNC('month', aaf.event_date)
)
SELECT event_month, total_return, total_ad_spend,
  COALESCE(total_return * 1.0 / NULLIF(total_ad_spend, 0), 0) AS ROAS
FROM monthly_data
ORDER BY event_month ASC;
```

## Scenario 2 - Electronic Gaming Retailer
* Tables: MS_Dynamics_365_Sales; MSD_GameMetrics; MSD_CustomerEngagement
* Columns: Transaction_ID, Kiosk_ID, Transaction_Datetime, Payment_Type, Transaction_Total, Promo_Code_Applied, Customer_Loyalty_ID, Product_Line_Items, Associate_ID, City_Location, State_Location in MS_Dynamics_365_Sales;
Product_ID, Title, Subtitle, Studio_Name, Original_Release, Launch_Date, Console_Type, Retail_Price, Quantity_Sold, Microtransaction_Revenue, Monthly_Player_Count in MSD_GameMetrics;
Customer_ID, Loyalty_Status, Customer_Age, Purchase_Date, Transaction_ID, Gameplay_Hours, Recent_Activity_Date, Levels_Completed, Trophies_Earned, Avg_Playtime_Minutes, Primary_Gaming_Platform in MSD_CustomerEngagement

_Formula_
{{
  "name": "Average Revenue Per Kiosk", "relation": "and",
  "variables": [
    {{"name": "kiosk_revenue", "agg": "sum", "tab": "MS_Dynamics_365_Sales", "col": "Transaction_Total"}},
    {{"name": "last_quarter", "agg": "greater_than", "tab": "MS_Dynamics_365_Sales", "col": "Transaction_Datetime", "row": "3 months ago"}}
  ]
}}
Segmentation: Is_Store_Nearby in MS_Dynamics_365_Sales (proximity)

_Conversation History_
User: I wonder if the kiosks near our stores are generating more revenue.
Agent: Sure, I can sum up the Transaction_Total for each kiosk location. I can use City_Location to determine whether the kiosk is located, but I'll need to know where the stores are located. Also, is there a time range we should focus on?
User: Let's go with the last quarter. I'd like to get the revenue per kiosk split by whether or not they are near a store or not.
Agent: Got it, I am planning to use City_Location to determine whether the kiosk is located, but I'll still need to know where the stores are located.
User: Our stores are located in Bellevue, Seattle, and Tacoma.

_Output_
```sql
-- Thought: The column names are capitalized with underscores, so we must be sure to follow the same style when performing the joins. DuckDB uses DATEADD to subtract time intervals.
WITH kiosk_revenue AS (
  SELECT Kiosk_ID, Is_Store_Nearby, SUM(Transaction_Total) AS total_revenue
  FROM MS_Dynamics_365_Sales
  WHERE Transaction_Datetime >= DATEADD(month, -3, CURRENT_DATE)
  GROUP BY Kiosk_ID, Is_Store_Nearby
),
kiosk_counts AS (
  SELECT Is_Store_Nearby, COUNT(DISTINCT Kiosk_ID) AS num_kiosks
  FROM MS_Dynamics_365_Sales
  WHERE Transaction_Datetime >= DATEADD(month, -3, CURRENT_DATE)
  GROUP BY Is_Store_Nearby
),
revenue_by_segment AS (
  SELECT kr.Is_Store_Nearby, SUM(kr.total_revenue) AS segment_revenue
  FROM kiosk_revenue kr
  GROUP BY kr.Is_Store_Nearby
)
SELECT rbs.Is_Store_Nearby, rbs.segment_revenue, kc.num_kiosks,
  COALESCE(rbs.segment_revenue * 1.0 / NULLIF(kc.num_kiosks, 0), 0) AS avg_revenue_per_kiosk
FROM revenue_by_segment rbs
JOIN kiosk_counts kc ON rbs.Is_Store_Nearby = kc.Is_Store_Nearby
ORDER BY rbs.Is_Store_Nearby;
```

_Formula_
{{
  "name": "Average Order Value", "relation": "multiply",
  "variables": [
    {{
      "name": "AOV", "relation": "multiply",
      "variables": [
        {{"name": "retail_price", "agg": "all", "tab": "MSD_GameMetrics", "col": "Retail_Price"}},
        {{"name": "quantity_sold", "agg": "all", "tab": "MSD_GameMetrics", "col": "Quantity_Sold"}}
      ]
    }}, {{
      "name": "tax", "agg": "constant", "tab": "N/A", "col": "N/A", "row": "0.92"
    }}      
  ]
}}
Segmentation: Age_Bracket in MSD_CustomerEngagement (numeric)

_Conversation History_
User: That's not necessarily true, older people spend less time on the app, but they have more money to spend.
Agent: You make a good point, should I segment the average order value by age to find out? Are there any discounts or filters to apply?
User: Let's focus on just the in-game purchase amount rather than total purchase value

_Output_
```sql
-- Thought: The conversation mentions in-game purchases, which is likely Microtransaction Revenue rather than Retail Price * Quantity Sold. Also, the ANY() function may require special handling when used in a join condition.
-- Question: Just to double check, should AOV for in-game purchases be based on Microtransaction_Revenue or Retail_Price? Also, I am planning to join GameMetrics with the Sales table by checking for Product IDs within Product_Line_Items. Is this correct?
```

## Scenario 3 - Security Monitoring SaaS Company
* Tables: MarketingQualifiedLeads; Subscriptions; RenderActivity
* Columns: LeadID, CompanyName, ContactEmail, IndustryType, AnnualAdSpend, TeamSize, LeadSource, DemoScheduled, PocStatus, SalesRepID, ClientUUID, LastTouchpoint in MarketingQualifiedLeads;
SubscriptionID, ClientUUID, PlanTier, GPUAllocation, MonthlyQuota, BillingCycle, PaymentStatus, OverageRate, ApiKeyHash, ContractStatus in Subscriptions;
ReleaseVersion, StartTimestamp, MaxResolution, ClientUUID, ProjectName, RenderType, FeaturesList, ProcessingTime, OutputFormat, ComputeUnits, QueuePriority, RenderStatus, ErrorTrace in RenderActivity

_Formula_
{{
  "name": "Sales Qualified Lead", "relation": "and",
  "variables": [
    {{
      "name": "high_value_prospect", "relation": "or",
      "variables": [
        {{"name": "enterprise_adspend", "agg": "greater_than", "tab": "MarketingQualifiedLeads", "col": "AnnualAdSpend", "row": "100000"}},
        {{"name": "large_team", "agg": "greater_than", "tab": "MarketingQualifiedLeads", "col": "TeamSize", "row": "50"}}
      ]
    }}, {{
      "name": "demo_scheduled", "agg": "equals", "tab": "MarketingQualifiedLeads", "col": "DemoScheduled", "row": true
    }}, {{
      "name": "active_pilot", "agg": "equals", "tab": "MarketingQualifiedLeads", "col": "PocStatus", "row": "Active"
    }}
  ]
}}
Segmentation: IndustrySegments in MarketingQualifiedLeads (categorical)

_Conversation History_
User: I want to extract the Sales Qualified Leads out of the MQLs, which are basically the most high value prospects to go after.
Agent: Sure, how do you want to define high value prospects?
User: Ok, so there are three main criteria: (1) they should be have annual revenue > 100K and team size > 50 (2) they should have a demo scheduled or completed (3) they should have an active pilot going on
Agent: No problem, I will pull Sales Qualified Leads based on AnnualAdSpend, TeamSize, and DemoScheduled. Is there a particular time frame you want to focus on?
User: All data is fine, but can we break this down into industry type? Also, we let's make sure that they have a pilot going on.

_Output_
```sql
-- Thought: Our new staging column is IndustrySegments, so we should use that for grouping rather than IndustryType. We can grab all the info we need from the MarketingQualifiedLeads table, so no joins are needed.
WITH qualified_leads AS (
  SELECT LeadID, CompanyName, ContactEmail, IndustrySegments, AnnualAdSpend, TeamSize, DemoScheduled, PocStatus
  FROM MarketingQualifiedLeads
  WHERE
    (AnnualAdSpend > 100000 AND TeamSize > 50)
    AND DemoScheduled = true
    AND PocStatus = 'Active'
)
SELECT
  IndustrySegments,
  COUNT(*) AS SalesQualifiedLeads,
  AVG(AnnualAdSpend) AS AvgAnnualSpend,
  AVG(TeamSize) AS AvgTeamSize
FROM qualified_leads
GROUP BY IndustrySegments
ORDER BY SalesQualifiedLeads DESC;
```

_Formula_
{{
  "name": "Hourly Processing Time", "relation": "and",
  "variables": [
    {{
      "name": "avg_processing_time", "relation": "and",
      "variables": [
        {{"name": "processing_time", "agg": "average", "tab": "RenderActivity", "col": "ProcessingTime"}},
        {{"name": "hourly_split", "agg": "all", "tab": "RenderActivity", "col": "StartTimestamp", "row": "group by hour"}}
      ]
    }}, {{
      "name": "recent_renders", "agg": "greater_than", "tab": "RenderActivity", "col": "StartTimestamp", "row": "5 days ago"
    }}
  ]
}}
Segmentation: ErrorType in RenderActivity (categorical)

_Conversation History_
User: I would start by grabbing the average processing time per team size. We can then check if there's any correlation with error trace as well.
Agent: Just to be clear, what do you mean by error trace? Is there a specific message you should be checking within the ErrorTrace?
User: We should bucket them into either: critical errors (anything with the word 'downtime'), configuration errors (invalid settings, incompatible render types), or memory errors (quota exceeded, out of memory)
Agent: Ok, I will calculate the average processing time per team size for the last 3 days. I will also bucket the errors into critical, configuration, and memory errors. Does that sound right?
User: We should go back 5 days instead. The rest sounds good.

_Output_
```sql
-- Thought: We should take advantage of the ErrorType staging column rather than deriving our own from the ErrorTrace column. The column names are capitalized without spaces, so we should follow the same convention.
WITH HourlyProcessing AS (
  SELECT 
    DATE_TRUNC('hour', StartTimestamp) AS Hour,
    ErrorType,
    AVG(ProcessingTime) AS AvgProcessingTime,
    COUNT(*) AS RenderCount
  FROM RenderActivity
  WHERE StartTimestamp >= DATEADD(day, -5, CURRENT_DATE)
  GROUP BY DATE_TRUNC('hour', StartTimestamp), ErrorType
)
SELECT Hour, ErrorType, AvgProcessingTime, RenderCount
FROM HourlyProcessing
ORDER BY Hour, ErrorType;
```
---
## Current Scenario
Please think carefully about your answer and be sure to take advantage of the provided staging column rather than deriving your own.
If the thought uncovers any uncertainty, please ask a clarification question rather than proceeding with code generation.
For our real scenario, the available data includes:
{valid_tab_col}

_Formula_
{formula}
Segmentation: {target_col} in {target_tab} ({segment_type})

_Conversation History_
{history}

_Output_
"""

create_segmentation_prompt = """As seen in the conversation history, we are calculating {metric} segmented by {dimension}.
To simplify the analysis, we will first create a staging column that buckets the data into appropriate dimensions based on some existing attributes.
Specifically, we want to create a '{target_col}' column in {target_tab} which breaks down the metric into segments, derived from data found in the {source_data}.
Your task is to write the Pandas code to create the new column and populate its content to support the analysis.

Please start by reviewing the conversation history and supporting details to determine the best way to create the new column.
The supporting details offers potential source columns to choose from, but you may decide to use more or less columns as needed to create the target column.
Focus on writing directly executable Python, which may contain comments to help with reasoning. If a request requires multiple steps, write each step on a new line.
When possible to do so easily, perform the operation in place rather than assigning to a dataframe. Keep it short since you only have 20 lines to work with.
You may assume that the environment already has access to NumPy as 'np', Pandas as 'pd', and the dataframe as 'db.table_name'.
Your final response should only contain well-formatted Python code, without any additional text or explanations after the output.

For example,
---
_Conversation History_
User: Basically, when the user stays on a product page for more than a minute, we show them a pop-up asking them to sign up for our newsletter. So there's a sign-up rate associated with that.
Agent: Got it, I will use a sign-up as a conversion event and the number of times the pop-up is shown as the number of visits. Does that sound right?
User: yea, but we also want to check if (a) did they have anything in their shopping cart and (b) what variation they saw based on the split test assignment

_Supporting Details_
Table: Newsletter_signup_popup / Source columns: items_in_cart, split_test_variation / Target: cart_split_segments
  * divide into 2 x 3 = 6 segments
  * first dimension: items_in_cart > 0 or items_in_cart == 0
  * second dimension: split_test_variation = please, mascara, or discount
  * category names can be: hasItem_please, hasItem_mascara, hasItem_discount, emptyCart_please, emptyCart_mascara, emptyCart_discount

_Output_
```python
# Define a helper function to map the combinations to segment names
def create_segment_name(row):
  # Dimension 1: Cart status
  cart_status = 'hasItem' if row['items_in_cart'] > 0 else 'emptyCart'
  # Dimension 2: Split test variation
  variation = row['split_test_variation']  
  # Combine dimensions to create the segment
  return f"{{cart_status}}_{{variation}}"
db.Newsletter_signup_popup['cart_split_segments'] = db.Newsletter_signup_popup.apply(create_segment_name, axis=1)
```

_Conversation History_
User: Our customers have been complaining about printers breaking down a lot recently. Can you help me find out when the most errors happen?
Agent: Sure, I can look at the error logs and check when the most errors happen. Does that sound right?
User: That sounds good. Please break it down by hour.

_Supporting Details_
Table: bambu_labs_printer_logs / Source columns: error_type, fab_timestamp / Target: error_hour
  * bucket into hourly segments
  * filter for error_type is not null
  * convert each fab_timestamp to a format which rounds to the nearest hour

_Output_
```python
# Create a mask for rows where error_type is not null
error_mask = db.bambu_labs_printer_logs['error_type'].notnull()
db.bambu_labs_printer_logs['error_hour'] = pd.NaT
# Fill in the error_hour column with the rounded hour
db.bambu_labs_printer_logs.loc[error_mask, 'error_hour'] = db.bambu_labs_printer_logs.loc[error_mask, 'fab_timestamp'].dt.round('H')
```

_Conversation History_
User: That's not necessarily true, older people spend less time on the app, but they have more money to spend.
Agent: You make a good point, should I segment the average order value by age to find out?
User: Let's focus on just the in-game purchase amount rather than total purchase value

_Supporting Details_
Table: SteamAccountPurchases / Source columns: Age / Target: AgeGroup
  * bucket into 6 segments: child, teen, young_adult, adult, middle_age, senior
  * child is anyone under 13
  * teen ranges from 13 to 17
  * young_adult ranges from 18 to 25
  * adult ranges from 26 to 34
  * middle_age ranges from 35 to 49
  * senior is anyone 50 and older

_Output_
```python
age_bins = [0, 13, 18, 26, 35, 50, 150]
age_labels = ['child', 'teen', 'young_adult', 'adult', 'middle_age', 'senior']
# Create the AgeGroup column using pd.cut to bucket ages into the defined segments
df['AgeGroup'] = pd.cut(df['Age'], bins=age_bins, labels=age_labels, right=False)
# Deal with null values in Age column
df['AgeGroup'] = df['AgeGroup'].cat.add_categories(['unknown'])
df.loc[df['Age'].isna(), 'AgeGroup'] = 'unknown'
```
_Conversation History_
User: What is the performance of our advertising lately?
Agent: Just to be clear, are you looking for the ROAS, CTR, or some other metric? Also, is there a specific time period you want to focus on?
User: I'd like to drill down into the ROAS for any campaigns that ran in October.

_Supporting Details_
Table: GA4_Report / Source columns: ad_campaign, launch_date / Target: ad_campaign_segment
  * find all unique campaign names that ran in October
  * create segments based on the first few tokens of the campaign name
  * set each campaign to its corresponding segment when applicable
  * set all other campaigns to null

_Output_
```python
# Find all unique campaign names that ran in October
october_campaigns = df.GA4_Report.loc[df.GA4_Report['launch_date'].dt.month == 10, 'ad_campaign'].unique()
# Create the ad_campaign_segment column
df.GA4_Report['ad_campaign_segment'] = None
for campaign in october_campaigns:
  # Process the campaign name to create the segment name
  cleaned_name = re.sub(r'[_-]', ' ', campaign)
  tokens = cleaned_name.split()
  segment_name = tokens[0].lower()
  if len(tokens) > 1:
    segment_name += f"_{{tokens[1].lower()}}"
  # Assign the segment name to the corresponding rows
  df.GA4_Report.loc[df.GA4_Report['ad_campaign'] == campaign, 'ad_campaign_segment'] = segment_name
```

_Conversation History_
User: I wonder if the kiosks near our stores are generating more revenue. Can you figure out the revenue per kiosk split by whether they are near a store or not?
Agent: Sure, I can sum up all the DailyPaymentsReceived for each kiosk location. I can use KioskLocation to determine whether the kiosk is located, but I'll need to know where the stores are located.
User: Our stores are located in Bellevue, Seattle, and Tacoma.

_Supporting Details_
Table: electronicRepairRevenue / Source columns: KioskLocation / Target: IsStoreNearby
  * bucket the kiosks into two segments, either near a store or not nearby a store
  * our stores are located in Bellevue, Seattle, and Tacoma
  # if the kiosk is in one of these cities, it is near_store, otherwise it is not_nearby

_Output_
```python
store_cities = ['Bellevue', 'Seattle', 'Tacoma']
# If KioskLocation is in one of the store cities, mark as 'near_store', otherwise 'not_nearby'
df['IsStoreNearby'] = df['KioskLocation'].apply(lambda location: 'near_store' if location in store_cities else 'not_nearby')
```

_Conversation History_
User: Do you think it's maybe a seasonality issue? I'm surprised that everyone is getting fewer calls all at once.
Agent: No problem, I can calculate the number of calls scheduled per sales rep and then group by month. Does that sound right?
User: Yes, what does that tell us?

_Supporting Details_
Table: Pardot Leads (reviewed) / Source columns: Call Scheduled Date / Target: Calls By Month
  * bucket into monthly segments
  * filter out any leads without a Call Scheduled Date
  * convert each remaining Call Scheduled Date to the nearest month

_Output_
```python
df_with_calls = df.dropna(subset=['Call Scheduled Date'])
# Ensure the Call Scheduled Date is in datetime format
if not pd.api.types.is_datetime64_any_dtype(df_with_calls['Call Scheduled Date']):
  df_with_calls['Call Scheduled Date'] = pd.to_datetime(df_with_calls['Call Scheduled Date'])
# Create a new column with the call scheduled date converted to the nearest month
df_with_calls['Calls By Month'] = df_with_calls['Call Scheduled Date'].dt.strftime('%Y-%m')
```
---
Now it's your turn! Please generate accurate, executable Python code to create the new column for segmenting the data.

_Conversation History_
{history}

_Supporting Details_
Table: {target_tab} / Source columns: {source_cols} / Target: {target_col}
{segment_steps}

_Output_
"""

metric_revision_prompt = """Following the user's instructions within the conversation history, we need to revise the formula for calculating the '{metric}' metric.
Concretely, formulas are constructed as a tree of variable objects, where each variable is either an Expression or a Clause.
The root of the tree is an Expression, which branches out into other variables by combining or comparing other Expressions, until we reach the leaf nodes represented as Clauses.
Whereas Expressions have other variables as children, Clauses contain no children and instead reference specific columns in a table.

Each Expression is composed of a name, verification, variables, and relation, which conveys the relationship between the variables.
Valid relations include: add (+), subtract (-), multiply (*), divide (/), exponent (^), and (&), or (|), less_than (<), greater_than (>), equals (=), conditional (?).
There is one last relationship called 'placeholder', which is used when we are unsure about how to properly structure the Expression at that level.
Our current aim is to leverage the responses in the conversation to fill in the placeholders, but if ambiguity still exists, prefer using the 'placeholder' relation rather than making assumptions.

Each Clause is composed of a name, aggregation (agg), table (tab), row, column (col), and verification (ver). They represent the variables that are grounded to actual values in the data.
Valid aggregations include: sum, count, average, top, bottom, min, max, greater_than, less_than, not, equals, empty, filled, constant, all.
For the aggregations of [top, bottom, greater_than, less_than, not, equals, constant], the 'row' field is used to specify the value of N.
The difference between top/bottom versus min/max is that top/bottom can be used to specify the number of values to return, while min/max only returns a single value.
Whereas not/equals are used to filter the column by a specific value, the empty/filled relations are used to filter for null or non-null values, respectively.

Please start by carefully reviewing the data preview and the prior formula, which should serve as your starting point.
Then, examine the conversation history to decide how the user's feedback should be incorporated to modify or revise the variables in the formula.
Variables that have been verified should not be changed, but all other variables can be considered for revision.
If the complexity of the request has changed drastically (ie. we now need to segment the metric, or calculate multiple metrics), do not try to shoe-horn the new request into the existing formula.
Instead, set the name of the root Expression to 'other', set the relation to 'placeholder', and set the variables to an empty list. This will trigger a restart of the formula generation process.
Your entire response should be in well-formatted JSON including keys for your thought (string) and the revised formula (dict), with no further explanations after the JSON output.

For example,
---
## Scenario 1
* Tables: amp_events; orders_acct; monthly_inventory
* Columns: event_id, event_type, event_timestamp, source_link, viewed_url, user_id, session_id, device_type, browser_type, ip_address, user_agent in amp_events;
order_id, purchase_item, page_views, payment_amt, payment_method, purchase_timestamp, shipping_addr, billing_addr, is_deleted, last_modified_dt in orders_acct;
item_sk, item_code, sku, cat_id, cat_name, sub_cat_id, price_msrp, price_current, qty_on_hand, qty_allocated, qty_backordered, supplier_id, supplier_name, date_added, last_restock_dt, reorder_point, is_active, discontinued_flag in monthly_inventory;

_Conversation History_
User: What is the CTR for the last 30 days?
Agent: Should I consider 'visit_site' as the click event? I can then use the page_views in the orders table to determine the impressions.
User: You can use 'view_product' event for clicks and 'visit_site' event type for impressions. There is no need to join with the orders table.

_Prior Formula_
{{
  "name": "Click-Through Rate", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "CTR", "verified": false, "relation": "divide",
      "variables": [
        {{"name": "click_event", "ver": false, "agg": "equals", "tab": "amp_events", "col": "event_type", "row": "visit_site"}},
        {{"name": "impression_event", "ver": false, "agg": "filled", "tab": "orders_acct", "col": "page_views"}}
      ]
    }}, {{
      "name": "last_month", "ver": false, "agg": "greater_than", "tab": "amp_events", "col": "event_timestamp", "row": "30 days ago"
    }}
  ]
}}

_Output_
```json
{{
  "thought": "We should use the event_type column to determine both clicks and impressions, rather than joining with the orders table.",
  "formula": {{
    "name": "Click-Through Rate", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "CTR", "relation": "divide",
        "variables": [
          {{"name": "click_event", "ver": false, "agg": "equals", "tab": "amp_events", "col": "event_type", "row": "view_product"}},
          {{"name": "impression_event", "ver": false, "agg": "equals", "tab": "amp_events", "col": "event_type", "row": "visit_site"}}
        ]
      }}, {{
        "name": "last_month", "ver": false, "agg": "greater_than", "tab": "amp_events", "col": "event_timestamp", "row": "30 days ago"
      }}
    ]
  }}
}}
```

## Scenario 2
* Tables: CodePathCourses; StudentProgress; LearnerSuccess; MarketingCampaigns
* Columns: CourseID, TrackName, LeadMentorID, TechStackJSON, CohortStartDate, CohortEndDate, WeeklyCommitmentHrs, DeliveryFormat, DifficultyLevel, CurrentEnrollment, MaxCapacity, PreReqSkills, GitHubTemplateURL in CodePathCourses;
ProgressID, CourseID, LearnerUUID, EnrollmentDate, MilestoneStatus, LastSubmissionURL, PeerReviewScore, TuitionPlanType, EmploymentStatus, MentorNotes, ActiveStatus in StudentProgress;
SuccessID, LearnerUUID, CourseID, JobPlacement, SalaryIncrease, TestimonialBody, DateSubmitted, NPSScore, CompanyPlaced, ShowcasePermission in LearnerSuccess;
CampaignID, InitiativeName, CareerSegment, LinkedInAudience, StudentSignups, PartnershipType, BudgetAllocated, BudgetSpent, LeadSource in MarketingCampaigns

_Conversation History_
User: Ok, taking a step back, what are our monthly actives?
Agent: I can take the count of unique students based on LearnerUUID from the StudentProgress table and filter active students. Does that work?
User: Yes, that sounds fine. I just want to focus on students from the April cohort though.
Agent: Sure, should I be using a CohortStartDate or CohortEndDate?
User: Actually, the right way is to look for the correct UTM parameters in the LastSubmissionURL. The student must also have ActiveStatus listed as true.

_Prior Formula_
{{
  "name": "Monthly Active Users", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "MAU", "verified": false, "relation": "and",
      "variables": [
        {{"name": "unique_users", "ver": true, "agg": "count", "tab": "StudentProgress", "col": "LearnerUUID"}},
        {{"name": "active_filter", "ver": false, "agg": "equals", "tab": "StudentProgress", "col": "ActiveStatus", "row": true}}
      ]
    }}, {{
      "name": "cohort_filter", "ver": false, "agg": "equals", "tab": "StudentProgress", "col": "CohortStartDate", "row": "April"
    }}
  ]
}}

_Output_
```json
{{
  "thought": "We should revise the cohort filter to use the LastSubmissionURL column instead of the CohortStartDate column. I can also simplify the formula by collapsing the MAU variable.",
  "formula": {{
    "name": "Monthly Active Users", "verified": false, "relation": "and",
    "variables": [
      {{"name": "unique_users", "ver": true, "agg": "count", "tab": "StudentProgress", "col": "LearnerUUID"}},
      {{"name": "cohort_filter", "ver": false, "agg": "equals", "tab": "StudentProgress", "col": "LastSubmissionURL", "row": "cohort=April"}},
      {{"name": "active_filter", "ver": false, "agg": "equals", "tab": "StudentProgress", "col": "ActiveStatus", "row": true}}
    ]
  }}
}}
```

_Conversation History_
User: Which channels actually cost us the most in order to get a student to sign up?
Agent: Are you asking about the return on ad spend segmented by channel? Also, is there a specific time period you are interested in?
User: Not exactly, I want to know the channels with the highest ad spend in terms of customer acquisition.

_Prior Formula_
{{
  "name": "Return on Ad Spend", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "return", "verified": false, "relation": "multiply",
      "variables": [
        {{"name": "course_signups", "ver": false, "agg": "count", "tab": "MarketingCampaigns", "col": "StudentSignups"}},
        {{"name": "price_per_course", "ver": false, "agg": "all", "tab": "CodePathCourses", "col": "CoursePrice"}}
      ]
    }}, {{
      "name": "ad_spend", "ver": false, "agg": "sum", "tab": "MarketingCampaigns", "col": "BudgetSpent"
    }},
  ]
}}

_Output_
```json
{{
  "thought": "It turns out we actually want to calculate CAC rather than ROAS. Each customer in this case is a student, and an acquistition is a student signup. We also need to segment by channel, which we will deal with separately.",
  "formula": {{
    "name": "Customer Acquisition Cost",
    "relation": "divide",
    "variables": [
      {{"name": "acquisition_cost", "agg": "sum", "tab": "MarketingCampaigns", "col": "BudgetSpent"}},
      {{"name": "new_students", "agg": "count", "tab": "MarketingCampaigns", "col": "StudentSignups"}}
    ]
  }}
}}
```

## Scenario 3
* Tables: tbl_customer_master; mkt_campaigns_v2; inv_items_current; promo_discount_rules
* Columns: cust_id, user_uuid, f_name, l_name, customer_email, registration_ts, loyalty_pts_current, loyalty_pts_lifetime, loyalty_tier, shipping_addr, billing_addr, is_deleted, newsletter_flag, last_modified_dt in tbl_customer_master;
campaign_id, campaign_code, promo_name, dt_start, dt_end, segment_code, channel_type, budget_allocated, budget_spent, response_pct, asset_url, is_active, created_by, modified_by, created_dt, modified_dt in mkt_campaigns_v2;
item_sk, item_code, sku, cat_id, cat_name, sub_cat_id, price_msrp, price_current, qty_on_hand, qty_allocated, qty_backordered, supplier_id, supplier_name, date_added, last_restock_dt, reorder_point, is_active, discontinued_flag in inv_items_current;
promo_sk, legacy_promo_id, promo_desc, valid_from, valid_to, discount_pct, discount_amt, min_purchase_amt, excluded_items, coupon_code, times_used, max_uses, single_use_flag, stackable_flag, created_ts, modified_ts in promo_discount_rules

_Conversation History_
User: Actually, total ad spend includes discount amount in addition to budget spent.
Agent: It seems that the promo_name from mkt_campaigns_v2 does not line up with the promo_sk from promo_discount_rules, any ideas on how to join the two tables together?
User: Same as before, if it's running at the same time, they are the same promotion.
Agent: Is the date added column from inv_items_current the right one to use for time, or should I use the valid_from and valid_to columns?
User: I think the valid from and to are more accurate. Anything from March to June should be fine.

_Prior Formula_
{{
  "name": "Return on Ad Spend", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "All_ROAS", "verified": true, "relation": "divide",
      "variables": [
        {{
          "name": "total return", "verified": true, "relation": "multiply",
          "variables": [
            {{"name": "current_price", "ver": true, "agg": "all", "tab": "inv_items_current", "col": "price_current"}},
            {{"name": "current_quantity", "ver": true, "agg": "all", "tab": "inv_items_current", "col": "qty_on_hand"}}
          ]
        }}, {{
          "name": "ad spend", "verified": true, "relation": "add",
          "variables": [
            {{"name": "discount_amount", "ver": true,  "agg": "sum", "tab": "mkt_campaigns_v2", "col": "discount_amt"}},
            {{"name": "budget_spent", "ver": true, "agg": "sum", "tab": "mkt_campaigns_v2", "col": "budget_spent"}},
          ]
        }}
      ]
    }}, {{
      "name": "recent_promos", "verified": false, "relation": "placeholder", "variables": []
    }}
  ]
}}

_Output_
```json
{{
  "thought": "The correct method to filter for recent promos is to use the valid_from and valid_to columns to specify the date range.",
  "formula": {{
    "name": "Return on Ad Spend", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "All_ROAS", "verified": true, "relation": "divide",
        "variables": [
          {{
            "name": "total return", "verified": true, "relation": "multiply",
            "variables": [
              {{"name": "current_price", "ver": true, "agg": "all", "tab": "inv_items_current", "col": "price_current"}},
              {{"name": "current_quantity", "ver": true, "agg": "all", "tab": "inv_items_current", "col": "qty_on_hand"}}
            ]
          }}, {{
            "name": "ad spend", "verified": true, "relation": "add",
            "variables": [
              {{"name": "discount_amount", "ver": true,  "agg": "sum", "tab": "mkt_campaigns_v2", "col": "discount_amt"}},
              {{"name": "budget_spent", "ver": true, "agg": "sum", "tab": "mkt_campaigns_v2", "col": "budget_spent"}},
            ]
          }}
        ]
      }}, {{
        "name": "recent_promos", "verified": false, "relation": "and",
        "variables": [
          {{"name": "after_march", "ver": false, "agg": "greater_than", "tab": "promo_discount_rules", "col": "valid_from", "row": "March"}},
          {{"name": "before_june", "ver": false, "agg": "less_than", "tab": "promo_discount_rules", "col": "valid_to", "row": "June"}}
        ]
      }}
    ]
  }}
}}
```

_Conversation History_
User: Do you think people have been using our promo codes at all?
Agent: That's an interesting question. I can count the number of people who used the promo code by looking at the times_used column in the promo_discount_rules table. How does that sound?
User: Yes, let's focus on categories of either 'home appliances' or 'kitchenware'.

_Prior Formula_
{{
  "name": "Targeted Promo Usage", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "Promotion Usage Rate", "verified": false, "relation": "divide",
      "variables": [
        {{"name": "promo_used", "ver": false, "agg": "greater_than", "tab": "promo_discount_rules", "col": "times_used", "row": 0}},
        {{"name": "promo_offered", "ver": false, "agg": "count", "tab": "promo_discount_rules", "col": "promo_sk"}}
      ]
    }}, {{
      "name": "time_frame", "ver": false, "agg": "greater_than", "tab": "promo_discount_rules", "col": "modified_ts", "row": "30 days ago"
    }}  
  ]
}}

_Output_
```json
{{
  "thought": "The user has approved the method of measuring the whether a promo code was used, but we should add a filter for the categories.",
  "formula": {{
    "name": "Targeted Promo Usage", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "Promotion Usage Rate", "verified": false, "relation": "divide",
        "variables": [
          {{"name": "promo_used", "ver": false, "agg": "greater_than", "tab": "promo_discount_rules", "col": "times_used", "row": 0}},
          {{"name": "promo_offered", "ver": false, "agg": "count", "tab": "promo_discount_rules", "col": "promo_sk"}}
        ]
      }}, {{
        "name": "time_frame", "ver": false, "agg": "greater_than", "tab": "promo_discount_rules", "col": "modified_ts", "row": "30 days ago"
      }}, {{
        "name": "category_filter", "verified": false, "relation": "or",
        "variables": [
          {{"name": "home_appliance_filter", "ver": false, "agg": "equals", "tab": "inv_items_current", "col": "cat_name", "row": "home appliances"}},
          {{"name": "kitchenware_filter", "ver": false, "agg": "equals", "tab": "inv_items_current", "col": "cat_name", "row": "kitchenware"}}
        ]
      }}
    ]
  }}
}}
```

## Scenario 4
* Tables: booking_requests_v3; discount_programs_active; assistance_tickets_main
* Columns: booking_ref_id, pax_uuid, caregiver_uuid, journey_start_ts, journey_end_ts, payment_amt, pickup_addr, pickup_access_notes, mobility_equipment, booking_status_cd, payment_status_cd, special_instructions_txt in booking_requests_v3;
promo_sk, program_name, partner_org_id, discount_pct, eligible_vehicle_types, requires_medical_docs, docs_required_txt, total_claims, valid_from_dt, valid_to_dt in discount_programs_active;
ticket_sk, customer_book_id, caregiver_uuid, created_ts, severity_level, issue_category_main, medical_emergency_flag, issue_details_encrypted, assigned_agent_id, resolution_code, satisfaction_rating, compliance_review_flag in assistance_tickets_main

_Conversation History_
User: so for example, we can see if those folks usually pay a lot, pay a little, or where they usually come from. Whatever you want to come up.
Agent: OK, I will check for any patterns among ticket issuers starting with payment amounts, pick-up locations, and customer satisfaction. Should we limit our analysis to repeat ticket issuers or some other minimum threshold of tickets?
User: Yea, sure why not. Only people who have filed tickets more than once.
Agent: Got it. To calculate that, I will count the number of tickets per customer and group by customer_book_id. Then, I will filter for customers who have more than one ticket. Does that work?
User: Sounds good.

_Prior Formula_
{{
  "name": "Repeat Ticket Issuers", "verified": false, "relation": "greater_than",
  "variables": [
    {{"name": "tickets_per_customer", "ver": false, "agg": "count", "tab": "assistance_tickets_main", "col": "customer_book_id", "row": "group by customer_book_id"}},
    {{"name": "repeat_threshold", "ver": false, "agg": "constant", "tab": "N/A", "col": "N/A", "row": "1"}}
  ]
}}

_Output_
```json
{{
  "thought": "The user has approved the method of calculating repeat ticket issuers without giving any additional details, so we keep the formula as is.",
  "formula": {{
    "name": "Repeat Ticket Issuers", "verified": false, "relation": "greater_than",
    "variables": [
      {{"name": "tickets_per_customer", "ver": false, "agg": "count", "tab": "assistance_tickets_main", "col": "customer_book_id", "row": "group by customer_book_id"}},
      {{"name": "repeat_threshold", "ver": false, "agg": "constant", "tab": "N/A", "col": "N/A", "row": "1"}}
    ]
  }}
}}
```

_Conversation History_
User: What about conversion rate then? Can we look at that for the past week?
Agent: Sure, I can calculate the conversion rate by checking whether payment status is filled and dividing it by the number of unique booking ids. I will look at the past 7 days according to the created timestamp. How does that sound?
User: Not really, conversions are when the booking status is paid and the total possible is any non-null booking status. Also, journey timestamp is the right place to look for time.

_Prior Formula_
{{
  "name": "Conversion Rate", "verified": false, "relation": "and",
  "variables": [
    {{
      "name": "CVR", "verified": false, "relation": "divide",
      "variables": [
        {{"name": "conversion", "ver": false, "agg": "filled", "tab": "booking_requests_v3", "col": "payment_status_cd"}},
        {{"name": "total_possible", "ver": false, "agg": "count", "tab": "booking_requests_v3", "col": "booking_ref_id"}}
      ]
    }}, {{
      "name": "time_frame", "ver": false, "agg": "greater_than", "tab": "assistance_tickets_main", "col": "created_ts", "row": "7 days ago"
    }}
  ]
}}

_Output_
```json
{{
  "thought": "The user has provided more specific details about the conversion and total possible conditions, so we should update the formula accordingly.",
  "formula": {{
    "name": "Conversion Rate", "verified": false, "relation": "and",
    "variables": [
      {{
        "name": "CVR", "verified": false, "relation": "divide",
        "variables": [
          {{"name": "conversion", "ver": false, "agg": "equals", "tab": "booking_requests_v3", "col": "booking_status_cd", "row": "paid"}},
          {{"name": "total_possible", "ver": false, "agg": "filled", "tab": "booking_requests_v3", "col": "booking_status_cd"}}
        ]
      }}, {{
        "name": "time_frame", "ver": false, "agg": "greater_than", "tab": "booking_requests_v3", "col": "journey_start_ts", "row": "7 days ago"
      }}
    ]
  }}
}}
```
---
Now it's your turn! Please review the conversation history and data preview to determine how the prior formula should be revised.
Then, share your thought process followed by the revised formula, without any text before or after the JSON output. The valid tables and columns are:
{valid_tab_col}

_Data Preview_
{preview}

_Conversation History_
{history}

_Prior Formula_
{expression}

_Output_
"""

segment_revision_prompt = """We are in the middle of a complex analysis where we are calculating the {metric} metric segmented by {dimension}.
Given the complexity, we break down the task into multiple steps by first creating a staging column for segmentation and then querying the data to retrieve the final answer.
At the moment, you can assume the appropriate segments have already been created within the {column} column, which is ready for grouping as needed.
Therefore, your task is to focus on revising the candidate formula to calculate the core metric based on feedback from the conversation history.

Concretely, formulas are constructed as a tree of variable objects, where each variable is either an Expression or a Clause.
The root of the tree is an Expression, which branches out into other variables by combining or comparing other Expressions, until we reach the leaf nodes represented as Clauses.
Whereas Expressions have other variables as children, Clauses contain no children and instead reference specific columns in a table.

Each Expression is composed of a name, verification, variables, and relation, which conveys the relationship between the variables.
Valid relations include: add (+), subtract (-), multiply (*), divide (/), exponent (^), and (&), or (|), less_than (<), greater_than (>), equals (=), conditional (?).
There is one last relationship called 'placeholder', which is used when we are unsure about how to properly structure the Expression at that level.
Our current aim is to leverage the responses in the conversation to fill in the placeholders, but if ambiguity still exists, prefer using the 'placeholder' relation rather than making assumptions.

Each Clause is composed of a name, aggregation (agg), table (tab), row, column (col), and verification (ver). They represent the variables that are grounded to actual values in the data.
Valid aggregations include: sum, count, average, top, bottom, min, max, greater_than, less_than, not, equals, empty, filled, constant, all.
For the aggregations of [top, bottom, greater_than, less_than, not, equals, constant], the 'row' field is used to specify the value of N.
The difference between top/bottom versus min/max is that top/bottom can be used to specify the number of values to return, while min/max only returns a single value.
Whereas not/equals are used to filter the column by a specific value, the empty/filled relations are used to filter for null or non-null values, respectively.

Please start by carefully reviewing the prior formula and segmentation dimension, which should serve as your starting point.
Then, examine the conversation history to decide how the user's feedback should be incorporated to modify or revise the variables in the formula.
Variables that have been verified should not be changed, but all other variables can be considered for revision.
If the user request has changed drastically (ie. the metric of interest has changed or we need to calculate multiple metrics), do not try to shoe-horn the new metric into the existing formula.
Instead, set the name of the root Expression to 'other', set the relation to 'placeholder', and set the variables to an empty list. This will trigger a restart of the formula generation process.
Your entire response should be in well-formatted JSON including keys for your thought (string) and the revised formula (dict), with no further explanations after the JSON output.

For example,
---
## E-commerce Online Advertiser Scenario
* Tables: AdobeAnalytics_final; SalesRecord_Shopify_0812; Klaviyo_Email_Lists
* Columns: event_date, channel, campaign_id, impressions, clicks, conversions, interstitial_displays, time_on_site, landing_page_views, added_to_cart, checkout_starts in AdobeAnalytics_final;
order_id, date_created, customer_id, utm_source, utm_medium, utm_campaign, order_total, items_purchased, shipping_cost, shipping_date, discount_code in SalesRecord_Shopify_0812;
customer_id, email, first_purchase_date, last_purchase_date, total_orders, avg_order_value, campaign_analytics_id, user_tags, join_channel, email_engagement_score, subscription_status in Klaviyo_Email_Lists

_Prior Formula_
{{
  "name": "Conversion Rate", "verified": false, "relation": "divide",
  "variables": [
    {{"name": "email_conversions", "agg": "count", "tab": "AdobeAnalytics_final", "col": "conversions", "ver": false}},
    {{"name": "popup_displays", "agg": "sum", "tab": "AdobeAnalytics_final", "col": "interstitial_displays", "ver": false}}
  ]
}}
Segmentation: cart_split_assignment (categorical)

_Conversation History_
User: Basically, when the user stays on a product page for more than a minute, we show them a pop-up asking them to sign up for our newsletter. So there's a sign-up rate associated with that.
Agent: Got it, I will use a sign-up as a conversion event and the number of times the pop-up is shown as the number of visits. Does that sound right?
User: yea, but we also want to check if (a) did they have anything in their shopping cart and (b) what variation they saw based on the split test assignment
Agent: I can calculate conversion rate based on conversions divided by the number of time the pop-up was displayed. Is that right?
User: For signups, we go with anyone who is on the mailing list, and then count those that have 'popup_on_cart_page' as the channel where they joined.

_Output_
```json
{{
  "thought": "The signup event should be modified to count unique customers in the Klaviyo mailing list and then filter by the join_channel column, rather than looking at the conversion column.",
  formula": {{
    "name": "Conversion Rate", "verified": false, "relation": "divide",
    "variables": [
      {{
        "name": "sign_ups_from_website", "verified": false, "relation": "and",
        "variables": [
          {{"name": "email_signup", "agg": "count", "tab": "Klaviyo_Email_Lists", "col": "customer_id", "ver": false}},
          {{"name": "from_website", "agg": "equals", "tab": "Klaviyo_Email_Lists", "col": "join_channel", "row": "popup_on_cart_page", "ver": false}}
        ]
      }},
      {{"name": "popup_views", "agg": "sum", "tab": "AdobeAnalytics_final", "col": "interstitial_displays", "ver": false}}
    ]
  }}
}}
```

_Prior Formula_
{{
  "name": "Return on Ad Spend", "verified": false,
  "relation": "and",
  "variables": [
    {{
      "name": "ROAS", "verified": false,
      "relation": "divide",
      "variables": [
        {{
          "name": "total_return", "verified": false,
          "relation": "multiply",
          "variables": [
            {{"name": "num_orders", "ver": false, "agg": "all", "tab": "SalesRecord_Shopify_0812", "col": "total_orders"}},
            {{"name": "revenue_per_order", "ver": false, "agg": "all", "tab": "SalesRecord_Shopify_0812", "col": "avg_order_value"}}
          ]
        }}, {{
          "name": "ad_spend", "ver": false, "agg": "sum", "tab": "AdobeAnalytics_final", "col": "paid_spend"
        }}
      ]
    }}, {{
      "name": "facebook_filter", "ver": false, "agg": "equals", "tab": "AdobeAnalytics_final", "col": "channel", "row": "Facebook"
    }}, {{
      "name": "past_year", "ver": false, "agg": "greater_than", "tab": "AdobeAnalytics_final", "col": "event_date", "row": "12 months ago"
    }}
  ]
}}
Segmentation: month (temporal)

_Conversation History_
User: But maybe the sales are high value items? Maybe each buyer has high LTV?
Agent: Certainly, that could be the case. Should I calculate the LTV for customers from Facebook?
User: Not sure how we would even do that, can we just get the ROAS for Facebook instead? I want this broken down by month for the past 12 months.
Agent: I am planning to calculate ROAS by multiplying the number of orders by the average order value, then dividing by the ad spend. I will then split the results by month. Does that sound right?
User: Yes, make sure to focus on just the last year of Facebook data.

_Output_
```json
{{
  "thought": "The user seems to agree with our line of thinking, so we can keep the formula as is.",
  "formula": {{
    "name": "Return on Ad Spend", "verified": false,
    "relation": "and",
    "variables": [
      {{
        "name": "ROAS", "verified": false,
        "relation": "divide",
        "variables": [
          {{
            "name": "total_return", "verified": false,
            "relation": "multiply",
            "variables": [
              {{"name": "num_orders", "ver": false, "agg": "all", "tab": "SalesRecord_Shopify_0812", "col": "total_orders"}},
              {{"name": "revenue_per_order", "ver": false, "agg": "all", "tab": "SalesRecord_Shopify_0812", "col": "avg_order_value"}}
            ]
          }}, {{
            "name": "ad_spend", "ver": false, "agg": "sum", "tab": "AdobeAnalytics_final", "col": "paid_spend"
          }}
        ]
      }}, {{
        "name": "facebook_filter", "ver": false, "agg": "equals", "tab": "AdobeAnalytics_final", "col": "channel", "row": "Facebook"
      }}, {{
        "name": "past_year", "ver": false, "agg": "greater_than", "tab": "AdobeAnalytics_final", "col": "event_date", "row": "12 months ago"
      }}
    ]
  }}
}}
```

## Scenario 2 - Electronic Gaming Retailer
* Tables: MS_Dynamics_365_Sales; MSD_GameMetrics; MSD_CustomerEngagement
* Columns: Transaction_ID, Kiosk_ID, Transaction_Datetime, Payment_Type, Transaction_Total, Promo_Code_Applied, Customer_Loyalty_ID, Product_Line_Items, Associate_ID, City_Location, State_Location in MS_Dynamics_365_Sales;
Product_ID, Title, Subtitle, Studio_Name, Original_Release, Launch_Date, Console_Type, Retail_Price, Quantity_Sold, Microtransaction_Revenue, Monthly_Player_Count in MSD_GameMetrics;
Customer_ID, Loyalty_Status, Customer_Age, Purchase_Date, Transaction_ID, Gameplay_Hours, Recent_Activity_Date, Levels_Completed, Trophies_Earned, Avg_Playtime_Minutes, Primary_Gaming_Platform in MSD_CustomerEngagement

_Prior Formula_
{{
  "name": "Average Revenue Per Kiosk", "verified": false,
  "relation": "and",
  "variables": [
    {{"name": "kiosk_revenue", "agg": "sum", "tab": "MS_Dynamics_365_Sales", "col": "Transaction_Total", "ver": false}},
    {{"name": "last_quarter", "agg": "greater_than", "tab": "MS_Dynamics_365_Sales", "col": "Transaction_Datetime", "row": "3 months ago", "ver": false}}
  ]
}}
Segmentation: Is_Store_Nearby (proximity)

_Conversation History_
User: I wonder if the kiosks near our stores are generating more revenue.
Agent: Sure, I can sum up the Transaction_Total for each kiosk location. I can use city_location to determine whether the kiosk is located, but I'll need to know where the stores are located. Also, is there a time range we should focus on?
User: Let's go with the last quarter. I'd like to get the revenue per kiosk split by whether or not they are near a store or not.
Agent: Got it, I am planning to use city_location to determine whether the kiosk is located, but I'll still need to know where the stores are located.
User: Our stores are located in Bellevue, Seattle, and Tacoma.

_Output_
```json
{{
  "thought": "Store location affects the segmentation, but does not change the formula, so we can keep the formula the same.",
  "formula": {{
    "name": "Average Revenue Per Kiosk", "verified": false,
    "relation": "and",
    "variables": [
      {{"name": "kiosk_revenue", "agg": "sum", "tab": "MS_Dynamics_365_Sales", "col": "Transaction_Total", "ver": false}},
      {{"name": "last_quarter", "agg": "greater_than", "tab": "MS_Dynamics_365_Sales", "col": "Transaction_Datetime", "row": "3 months ago", "ver": false}}
    ]
  }}
}}
```

_Prior Formula_
{{
  "name": "Average Order Value", "verified": false,
  "relation": "multiply",
  "variables": [
    {{"name": "retail_price", "agg": "all", "tab": "MSD_GameMetrics", "col": "retail_price", "ver": false}},
    {{"name": "quantity_sold", "agg": "all", "tab": "MSD_GameMetrics", "col": "quantity_sold", "ver": false}}
  ]
}}
Segmentation: age_bracket (numeric)

_Conversation History_
User: That's not necessarily true, older people spend less time on the app, but they have more money to spend.
Agent: You make a good point, should I segment the average order value by age to find out? Are there any discounts or filters to apply?
User: Let's focus on just the total purchase value and forget about any in-game transactions.
Agent: Certainly, I am going to use the retail_price and quantity_sold, while ignoring the microtransaction_revenue. Is there anything else that goes into calculating the purchase value?
User: Yes, you should remove 8% tax so that we can get the true value.

_Output_
```json
{{
  "thought": "We need to take tax into account after multiplying the retail price and quantity sold to get our final answer.",
  "formula": {{
    "name": "Average Order Value", "verified": false,
    "relation": "multiply",
    "variables": [
      {{
        "name": "AOV", "verified": false,
        "relation": "multiply",
        "variables": [
          {{"name": "retail_price", "agg": "all", "tab": "MSD_GameMetrics", "col": "retail_price", "ver": false}},
          {{"name": "quantity_sold", "agg": "all", "tab": "MSD_GameMetrics", "col": "quantity_sold", "ver": false}}
        ]
      }}, {{
        "name": "tax", "ver": false, "agg": "constant", "tab": "N/A", "col": "N/A", "row": "0.92"
      }}      
    ]
  }}
}}
```

## Scenario 3 - Flexfit Fitness Club
* Tables: FFC Membership; ClassSchedule; Flexfit Fitness Profile
* Columns: MemberID, FirstName, LastName, Email, Phone, CheckIns, MembershipType, RemainingClasses, PaymentMethod, FirstPaymentDate, LastPaymentDate, EmergencyContactName, EmergencyContactPhone, DateOfBirth, JoinDate, ReferredBy in FFC Membership;
ClassID, ClassName, Instructor, Room, DayOfWeek, StartTime, EndTime, MaxCapacity, CurrentEnrollment, Description, DifficultyLevel, EquipmentNeeded, IsSpecialEvent, IsCancelled, SubstituteInstructor in ClassSchedule;
ProfileID, MemberID, Height, Weight, BodyFatPercentage, BMI, FitnessGoals, HealthConditions, Injuries, AssessmentDate, StepsAverage, RestingHeartRate, MaxHeartRate, VO2Max, StrengthLevel, CardioLevel, MobilityLevel in Flexfit Fitness Profile

_Prior Formula_
{{
  "name": "Attendance Rate", "verified": false,
  "relation": "and",
  "variables": [
    {{
      "name": "attendance", "verified": false,
      "relation": "divide",
      "variables": [
        {{"name": "attend_class", "agg": "count", "tab": "FFC Membership", "col": "CheckIns", "ver": false}},
        {{"name": "signup_for_class", "agg": "count", "tab": "ClassSchedule", "col": "ClassName", "ver": false}}
      ]
    }}, {{
      "name": "special_event", "agg": "equals", "tab": "ClassSchedule", "col": "IsSpecialEvent", "row": true, "ver": false
    }}
  ]
}}
Segmentation: day_of_week (temporal)

_Conversation History_
User: How many people actually end up attending our special event classes? I wonder if weekends end up being more convenient for people.
Agent: It sounds like you want to calculate the attendance rate filtered to special events, segmented by weekday vs weekend. I will count attendance using the CheckIns column, is that right?
User: Actually, let's just drill down to day of week. I'd like a graph that shows signups and then actual attendance together.
Agent: No problem, I will first filter for special events, then count attendance using the CheckIns, and then count signups based on the count of unique class names. Does that sound correct?
User: Signups is the current enrollment, not the number of classes.

_Output_
```json
{{
  "thought": "We should update the formula to use the CurrentEnrollment column instead of the ClassName column to calculate signups. We will deal with the day of week segmentation separately.",
  "formula": {{
    "name": "Attendance Rate", "verified": false,
    "relation": "and",
    "variables": [
      {{
        "name": "attendance", "verified": false,
        "relation": "divide",
        "variables": [
          {{"name": "attend_class", "agg": "count", "tab": "FFC Membership", "col": "CheckIns", "ver": false}},
          {{"name": "signups", "agg": "sum", "tab": "ClassSchedule", "col": "CurrentEnrollment", "ver": false}}
        ]
      }}, {{
        "name": "special_event", "agg": "equals", "tab": "ClassSchedule", "col": "IsSpecialEvent", "row": true, "ver": false
      }}
    ]
  }}
}}
```

## Scenario 4 - Security Monitoring SaaS Company
* Tables: MarketingQualifiedLeads; Subscriptions; RenderActivity
* Columns: LeadID, CompanyName, ContactEmail, IndustryType, AnnualAdSpend, TeamSize, LeadSource, DemoScheduled, PocStatus, SalesRepID, ClientUUID, LastTouchpoint in MarketingQualifiedLeads;
SubscriptionID, ClientUUID, PlanTier, GPUAllocation, MonthlyQuota, BillingCycle, PaymentStatus, OverageRate, ApiKeyHash, ContractStatus in Subscriptions;
ReleaseVersion, StartTimestamp, MaxResolution, ClientUUID, ProjectName, RenderType, FeaturesList, ProcessingTime, OutputFormat, ComputeUnits, QueuePriority, RenderStatus, ErrorTrace in RenderActivity

_Prior Formula_
{{
  "name": "Sales Qualified Lead", "verified": false,
  "relation": "and",
  "variables": [
    {{
      "name": "high_value_prospect", "verified": false,
      "relation": "or",
      "variables": [
        {{"name": "enterprise_adspend", "agg": "greater_than", "tab": "MarketingQualifiedLeads", "col": "AnnualAdSpend", "row": "100000", "ver": false}},
        {{"name": "large_team", "agg": "greater_than", "tab": "MarketingQualifiedLeads", "col": "TeamSize", "row": "50", "ver": false}}
      ]
    }}, {{
      "name": "demo_scheduled", "agg": "equals", "tab": "MarketingQualifiedLeads", "col": "DemoScheduled", "row": true, "ver": false
    }}
  ]
}}
Segmentation: industry_type (categorical)

_Conversation History_
User: I want to extract the Sales Qualified Leads out of the MQLs, which are basically the most high value prospects to go after.
Agent: Sure, how do you want to define high value prospects?
User: Ok, so there are three main criteria: (1) they should be have annual revenue > 100K (2) team size > 50 and (3) they should have a demo scheduled or completed
Agent: No problem, I will pull Sales Qualified Leads based on AnnualAdSpend, TeamSize, and DemoScheduled. Is there a particular time frame you want to focus on?
User: All data is fine, but can we break this down into industry type? Also, we let's make sure that they have a pilot going on.

_Output_
```json
{{
  "thought": "The existing formula is mostly correct, but we should add a condition for an active pilot. We do not need to worry about industry type since that is a segmentation issue.",
  "formula": {{
    "name": "Sales Qualified Lead", "verified": false,
    "relation": "and",
    "variables": [
      {{
        "name": "high_value_prospect", "verified": false,
        "relation": "or",
        "variables": [
          {{"name": "enterprise_adspend", "agg": "greater_than", "tab": "MarketingQualifiedLeads", "col": "AnnualAdSpend", "row": "100000", "ver": false}},
          {{"name": "large_team", "agg": "greater_than", "tab": "MarketingQualifiedLeads", "col": "TeamSize", "row": "50", "ver": false}}
        ]
      }}, {{
        "name": "demo_scheduled", "agg": "equals", "tab": "MarketingQualifiedLeads", "col": "DemoScheduled", "row": true, "ver": false
      }}, {{
        "name": "active_pilot", "agg": "equals", "tab": "MarketingQualifiedLeads", "col": "PocStatus", "row": "Active", "ver": false
      }}
    ]
  }}
}}
```

_Prior Formula_
{{
  "name": "Hourly Processing Time", "verified": false,
  "relation": "and",
  "variables": [
    {{
      "name": "avg_processing_time", "verified": false,
      "relation": "and",
      "variables": [
        {{"name": "processing_time", "agg": "average", "tab": "RenderActivity", "col": "ProcessingTime", "ver": false}},
        {{"name": "hourly_split", "agg": "all", "tab": "RenderActivity", "col": "StartTimestamp", "row": "group by hour", "ver": false}}
      ]
    }}, {{
      "name": "recent_renders", "agg": "greater_than", "tab": "RenderActivity", "col": "StartTimestamp", "row": "3 days ago", "ver": false
    }}
  ]
}}
Segmentation: error_type (categorical)

_Conversation History_
User: I would start by grabbing the average processing time per team size. We can then check if there's any correlation with error trace as well.
Agent: Just to be clear, what do you mean by error trace? Is there a specific message you should be checking within the ErrorTrace?
User: We should bucket them into either: critical errors (anything with the word 'downtime'), configuration errors (invalid settings, incompatible render types), or memory errors (quota exceeded, out of memory)
Agent: Ok, I will calculate the average processing time per team size for the last 3 days. I will also bucket the errors into critical, configuration, and memory errors. Does that sound right?
User: We should go back 5 days instead. The rest sounds good.

_Output_
```json
{{
  "thought": "We want to calculate the average processing time for the last 5 days rather than the last 3. We will worry error type segmentation as a separate step.",
  "formula": {{
    "name": "Hourly Processing Time", "verified": false,
    "relation": "and",
    "variables": [
      {{
        "name": "avg_processing_time", "verified": false,
        "relation": "and",
        "variables": [
          {{"name": "processing_time", "agg": "average", "tab": "RenderActivity", "col": "ProcessingTime", "ver": false}},
          {{"name": "hourly_split", "agg": "all", "tab": "RenderActivity", "col": "StartTimestamp", "row": "group by hour", "ver": false}}
        ]
      }}, {{
        "name": "recent_renders", "agg": "greater_than", "tab": "RenderActivity", "col": "StartTimestamp", "row": "5 days ago", "ver": false
      }}
    ]
  }}
}}
```
---
Now it's your turn! Please review the conversation history and supporting details to determine how the prior formula should be revised.
For our real scenario, we have also provided a preview of the data to provide extra context.
Then, share your thought process followed by the revised formula, without any text before or after the JSON output.
{valid_tab_col}

_Prior Formula_
{expression}
Segmentation: {dimension} ({type})

_Data Preview_
{preview}

_Conversation History_
{history}

_Output_
"""