design_prompt = """Given the user request and supporting details, follow the thought process to generate Pandas code for updating the data.
Supporting details includes information on the part of the table being changed, a description of the change, and the method for deriving the new values.
Changes include renaming content, modifying values, or filling rows with new content based on other columns.
The dataframes you have are {df_tables}.
Please only output executable Python without any explanations. If a request requires multiple steps, write each step on a new line.
When possible to do so easily, perform the operation in place rather than assigning to a dataframe.

For example,
#############
<This is just a placeholder>

#############
User request: {utterance}
* Description: {description}
* Method: {method}
Thought: {thought}
Result:"""


carry_prompt = """Given the conversation history, decide whether or not it makes sense to carry over the previous SQL query and associated data into the current turn.
Start by thinking about the context of the conversation and the relevance of the previous query to the current turn.
Then decide whether the previous query is fully sufficient to answer the user's lastest request, by answering with 'yes' it is sufficient or 'no' we should generate an updated query.
Your entire response should be in well-formatted JSON including keys for thought (string) and carry (string), with no further explanations after the JSON output.

For example,
#############
Suppose we are working with the following query:
_Previous SQL Query_
```sql
SELECT ad_source, SUM(email_signups) AS conversions
FROM platform_aggregated
WHERE signup_timestamp >= DATE_SUB(CURRENT_DATE, INTERVAL 1 WEEK)
  AND signup_timestamp < CURRENT_DATE
GROUP BY ad_source
ORDER BY conversions DESC;
```

When the final user utterance asks for a visualization of the previous turn, then it makes sense to carry over the previously queried data.
_Conversation History_
User: How many conversions did we get from each of the platforms last week?
Agent: We had 241 conversions from Facebook, 189 from Google, and 102 from LinkedIn. See table for more details.
User: Can you show this as a bar chart?

_Output_
```json
{{
  "thought": "The user is simply asking for a visualization of the previous turn.",
  "carry": "yes"
}}
```

In contrast, if the user is building upon a previous analysis by slightly tweaking the parameters, then it does *not* make sense to carry.
_Conversation History_
User: How many conversions did we get from each of the platforms last week?
Agent: We had 241 conversions from Facebook, 189 from Google, and 102 from LinkedIn. See table for more details.
User: Can I get the number of clicks as well?

_Output_
```json
{{
  "thought": "The user is building upon a previous analysis by slightly tweaking the parameters.",
  "carry": "no"
}}
```

Additionally, if the request for a visual does not reference a previous query, then it does not make sense to carry.
_Conversation History_
User: How many conversions did we get from each of the platforms last week?
Agent: We had 241 conversions from Facebook, 189 from Google, and 102 from LinkedIn. See table for more details.
User: Show me a graph of conversions from Google, broken down by day.

_Output_
```json
{{
  "thought": "The requested visual is for a different breakdown than the previous query.",
  "carry": "no"
}}
```
#############
As another example, suppose the user is preparing a spreadsheet to perform EDA:
_Previous SQL Query_
```sql
SELECT available_units
FROM inventory 
WHERE supplier = 'warehouse - Colorado';
```

If the request alters or removes some rows, then it does not make sense to carry because the table contents have changed.
_Conversation History_
User: Can you remove all rows with missing values?
Agent: Sure, I have removed them. How does this look?
User: Also remove any rows that have negative values.

_Output_
```json
{{
  "thought": "The final turn may have deleted rows in the table, so we should not carry over the previous results.",
  "carry": "no"
}}
```

In contrast, if the current turn simply asks for a description of table statistics, then it does make sense to carry because the table contents have not changed.
_Conversation History_
User: Can you remove all rows with missing values?
Agent: Sure, I have removed them. How does this look?
User: How many rows are left in the table?

_Output_
```json
{{
  "thought": "The final turn simply asks for a description of table statistics.",
  "carry": "yes"
}}
```
#############
For our last scenario, suppose the user queried the database for all rows with a particular value.
_Previous SQL Query_
```sql
SELECT Manufacturer, Rating
FROM supplier_reviews
WHERE Rating >= 4;
```

_Conversation History_
User: Can you show me all the manufacturers which have a rating of 4 stars or higher?
Agent: Sure, Florence Unlimited, Lorraine, and Jasper Design all have a rating of at least 4 stars. See the table for more.
User: How many manufacturers is that?

_Output_
```json
{{
  "thought": "The user wants the count of rows in the result of the previous query, so it makes sense to carry.",
  "carry": "yes"
}}
```

In contrast, if the current turn asks for the count of all rows, then it does not make sense to carry because the scope has expanded.
_Conversation History_
User: Can you show me all the manufacturers which have a rating of 4 stars or higher?
Agent: Sure, Florence Unlimited, Lorraine, and Jasper Design all have a rating of at least 4 stars. See the table for more.
User: How many manufacturers are there in total?

_Output_
```json
{{
  "thought": "The user wants the count of all rows, not just those with a rating of 4 stars or higher.",
  "carry": "no"
}}
```
#############
Now it's your turn to decide! Based on the conversation history, decide whether carrying over the previous SQL query and data is enough to deal with the current turn.

_Previous SQL Query_
```sql
{sql_query}
```

_Conversation History_
{history}

_Output_
"""

validate_prompt = """The user wants to validate that all data in the {target_column} column belongs some predefined set of values.
In addition to the conversation history, you will be provided with a list of all unique values found in the column.
Your task is to group these values into the most likely valid terms, with remaining related terms following each valid term.
If certain values don't seem to fit into any group and can be seen as noise, then map them to a '<none>' category.

Please start by examining the unique values and then think out loud of about how you would group them.
Then, generate well-formatted JSON output for the groups where each key is a valid term and the corresponding value is a list of related terms.
Only terms found from the unique values can be considered a valid term key. Do not create new categories that are not present in the unique values.
Your final response should only contain valid JSON with parents keys of thought (string) and groups (dict), with no further explanations.

For example,
#############
_Conversation History_
User: We need to clean up the different plan types.
Agent: Sure, I can validate the data, remove duplicates, or standardize the format. What would you like?
User: We should make sure that they're either Free, Basic, or Pro.

_Unique Values_
['Free user', 'basic', 'standard', 'pro', 'Pro user', 'Free', 'Basic', 'free', 'free tier', 'Pro']

_Output_
```json
{{
  "thought": "User has told us the plan groupings: Free, Basic, and Pro. So we simply need to map the remaining values to these groups.",
  "groups": {{
    "Free": ["Free user", "free", "free tier"],
    "Basic": ["basic", "standard"],
    "Pro": ["pro", "Pro user"]
  }}
}}
```
#############
_Conversation History_
User: We need to take care of the different user locations so that they are consistent.
Agent: Sure, how would you like to clean up the addresses?
User: Let's focus on the different zip codes for now.

_Unique Values_
['94040', '94045', '91150', '94046', '94043', '940', '94 043', '94044', '91123', 'zip', '94047', '9112 8', '94041']

_Output_
```json
{{
  "thought": "We should group by valid zip codes. There might be some OCR errors or missing digits to deal with.",
  "groups": {{
    "94040": [],
    "94045": [],
    "91150": [],
    "94046": [],
    "94043": ["94 043"],
    "94044": [],
    "94047": [],
    "91123": ["9112 8"],
    "94041": [],
    "<none>": ["940", "zip"]
  }}
}}
```
#############
_Conversation History_
User: Let's take a look at where we run promotions.
Agent: Sure, what would you like to know?
User: We only ship to cities in California at the moment, so we need to validate the city names.

_Unique Values_
['Los Angeles', 'San Jose', 'San Diego', 'Sacramento', 'Fresno', 'SJ', 'San Bernardino', 'Bay Area', 'SF', 'Bakersfield', 'Anaheim', 'LOs Angeles',
  'LA', 'san bernardino', 'san jose', 'San Francisco', 'anaheim', 'Riverside', 'riverside', 'San Fran', 'Stockton', 'SanDiego', 'sacramento']

_Output_
```json
{{
  "thought": "We should group by city name. The most likely desired format is title case text.",
  "groups": {{
    "Los Angeles": ["LA", "LOs Angeles"],
    "San Jose": ["SJ", "san jose"],
    "San Diego": ["SanDiego"],
    "San Bernardino": ["san bernardino"],
    "San Francisco": ["San Fran", "SF", "Bay Area"],
    "Anaheim": ["anaheim"],
    "Riverside": ["riverside"],
    "Bakersfield": [],
    "Fresno": [],
    "Stockton": [],
    "Sacramento": ["sacramento"]
  }}
}}
```
#############
_Conversation History_
User: There should only be two valid options.
Agent: I'm sorry, could you clarify which options you're referring to?
User: Can we make sure the payment status is either paid or unpaid.

_Unique Values_
['paid', 'PAID', 'unpaid']

_Output_
```json
{{
  "thought": "The user has specified two valid options: paid and unpaid. We can easily group the remaining values accordingly.",
  "groups": {{
    "paid": ["PAID"],
    "unpaid": []
  }}
}}
```
#############
_Conversation History_
User: What are the different pages in the checkout process?
Agent: According to PageActivity, some options include Enter Credit Card, Submit Payment, and Review Order. See table for more details.
User: Yea, so Payment Submission should be under Submit Payment actually, we gotta group these together

_Unique Values_
['Enter Credit Card', 'Submit Payment', 'Add to Cart', 'Review Order', 'Payment Submission', 'enter payment', 'ReView Order', 'Submit payment', 'Review Cart', 'Order Confirmation', 'Add-to-Cart']

_Output_
```json
{{
  "thought": "We know 'Submit Payment' is one valid term. We select other pages as the most likely to belong to a checkout process.",
  "groups": {{
    "Add to Cart": ["Add-to-Cart"],
    "Enter Credit Card": ["enter payment"],
    "Review Order": ["ReView Order", "Review Cart"],
    "Submit Payment": ["Submit payment", "Payment Submission"],
    "Order Confirmation": []
  }}
}}
```
#############
Now it's your turn! Based on the conversation history and unique values, please group the values and decide on the most likely valid terms for the {target_column} column.

_Conversation History_
{history}

_Unique Values_
{uniques}

_Output_
"""

validation_grouping_prompt = """Given the set of valid terms within a column, we are trying to map all remaining invalid terms to its most likely valid one.
The valid terms are values the user has confirmed are correct, while the invalid terms represent typos, variations, or other noise.
Start by examining all the unique values and consider how to map the invalid terms to the valid ones.
Then, generate well-formatted JSON output where each valid term serves as a key in the 'groups' dictionary.

Do not add or remove any new keys from the valid terms list, only map the invalid terms to the existing valid ones.
If there isn't a clear match for a term, then map it to a '<none>' category.
Your final response should only contain valid JSON including a thought (string) and groups (dict), with no further explanations after the output.

For example,
#############
_Unique Values_
Valid: ['Free', 'Basic', 'Pro']
Invalid: ['Free user', 'basic', 'standard', 'pro', 'Pro user', 'free', 'free tier']

_Output_
```json
{{
  "thought": "Standard likely belongs to basic. All the others include a valid term, making them easy to match.",
  "groups": {{
    "Free": ["Free user", "free", "free tier"],
    "Basic": ["basic", "standard"],
    "Pro": ["pro", "Pro user"]
  }}
}}
```
#############
_Unique Values_
Valid: ['94040', '94045', '91150', '94046', '94043', '94044', '94047', '94041', '91123']
Invalid: ['940', '94 043', 'zip', '9112 8']

_Output_
```json
{{
  "thought": "Valid zip codes likely have five digits. Some entries have OCR errors or missing digits.",
  "groups": {{
    "94040": [],
    "94045": [],
    "91150": [],
    "94046": [],
    "94043": ["94 043"],
    "94044": [],
    "94047": [],
    "91123": ["9112 8"],
    "94041": [],
    "<none>": ["940", "zip"]
  }}
}}
```
#############
_Unique Values_
Valid: ['Los Angeles', 'San Jose', 'San Diego', 'San Bernardino', 'San Francisco', 'Anaheim', 'Riverside', 'Bakersfield', 'Fresno', 'Stockton', 'Sacramento']
Invalid: ['SJ', 'Bay Area', 'SF', 'LOs Angeles', 'LA', 'san bernardino', 'san jose', 'anaheim', 'riverside', 'San Fran', 'SanDiego', 'sacramento']

_Output_
```json
{{
  "thought": "The pattern is to group by city name with proper capitalization.",
  "groups": {{
    "Los Angeles": ["LA", "LOs Angeles"],
    "San Jose": ["SJ", "san jose"],
    "San Diego": ["SanDiego"],
    "San Bernardino": ["san bernardino"],
    "San Francisco": ["San Fran", "SF", "Bay Area"],
    "Anaheim": ["anaheim"],
    "Riverside": ["riverside"],
    "Bakersfield": [],
    "Fresno": [],
    "Stockton": [],
    "Sacramento": ["sacramento"]
  }}
}}
```
#############
_Unique Values_
Valid: ['SJC', 'SAN', 'LAX', 'SMF', 'SFO', 'FAT', 'BUR', 'LGB', 'OAK']
Invalid: ['Los Angeles', 'Sacramento', 'SJ', 'smf', 'Bay Area', 'LA', 'sjc', 'San Francisco', 'SAN Diego', 'lax', 'san']

_Output_
```json
{{
  "thought": "The pattern is to group by airport code in uppercase.",
  "groups": {{
    "SJC": ["SJ", "sjc"],
    "SAN": ["SAN Diego", "san"],
    "LAX": ["Los Angeles", "LA", "lax"],
    "SMF": ["Sacramento", "smf"],
    "SFO": ["Bay Area", "San Francisco"],
    "FAT": [],
    "BUR": [],
    "LGB": [],
    "OAK": []
  }}
}}
```
#############
_Unique Values_
Valid: ['Add to Cart', 'Enter Credit Card', 'Review Order', 'Submit Payment', 'Order Confirmation']
Invalid: ['Payment Submission', 'enter payment', 'ReView Order', 'Submit payment', 'Review Cart', 'Add-to-Cart']

_Output_
```json
{{
  "thought": "The pattern is to group the terms by the pages of a checkout process.",
  "groups": {{
    "Add to Cart": ["Add-to-Cart"],
    "Enter Credit Card": ["enter payment"],
    "Review Order": ["ReView Order", "Review Cart"],
    "Submit Payment": ["Submit payment", "Payment Submission"],
    "Order Confirmation": []
  }}
}}
```
#############
Now it's your turn! Based on the conversation history and unique values, please group the values and decide on the most likely valid terms for the {target_column} column.
For reference, here is the recent conversation history and unique values:
{history}

_Unique Values_
Valid: {valid_terms}
Invalid: {invalid_terms}

_Output_
"""

pattern_routing_prompt = """Given the conversation history and the current turn, decide whether the user is looking to fill a column using a cumulative pattern.
For our purposes, a cumulative pattern is when the formula for a given row depends on the values of previous rows above it.
In contrast, filling in a column with a constant value or a formula that only depends on values in its own row is considered an standard operation.

After reviewing the conversation and sample rows, please help determine how to proceed. Our options include:
  - standard: the user is making a standard operation in updating a column or inserting a new column
  - pattern: the user is filling a column using a cumulative pattern, but it is unclear which column they are referring to
  - ambiguous: the user's request requires clarification or the request might not actually be related to filling column contents at all

Your response should only be in well-formatted JSON including your thinking and the chosen option, without any additional text or explanations.

For example,
#############
_Conversation History_
User: I'm looking down the DeliveryDate column and I'm seeing a pattern of errors.
Agent: OK, we can look into that. What kind of pattern are you seeing?
User: Can you change every date to be one day later than what it currently is?

_Output_
```json
{{
  "thought": "Despite mentioned the word 'pattern', the user is updating date values based on the row itself, rather than the row above it.",
  "level": "standard"
}}
```

_Conversation History_
User: let's go with the outliers
Agent: I can remove the outliers, or update them to a new value. What would you like to do?
User: Let's update the outliers to be the average of the two surrounding values.

_Output_
```json
{{
  "thought": "The request is ambiguous because it does not specify where the surrounding values are located.",
  "level": "ambiguous"
}}
```
_Conversation History_
User: Let's make a new column for costs as well that sums up all the cumulative totals.
Agent: I see two columns related to costs: cost_per_click and cost_of_goods. Which one should I use?
User: I want it to hold the sum of all the costs up to that point.

_Output_
```json
{{
  "thought": "The user is looking to fill a column using a cumulative pattern, but the column to be updated is unclear.",
  "level": "pattern"
}}
```

_Conversation History_
User: You see how we have lots of blanks in the final price column?
Agent: Yes, I can fill those in with the average price. Is that what you want?
User: The taxes and discounts should be applied to every item in inventory.

_Output_
```json
{{
  "thought": "Calculating the final price depends on the taxes and discounts in its own row, rather than an accumulated price.",
  "level": "standard"
}}
```

_Conversation History_
User: I need to clean up the data in the campaign column.
Agent: What kind of cleaning are you looking to do?
User: Can we go down the list and remove any duplicate campaigns?

_Output_
```json
{{
  "thought": "The task is about removing duplicates rather than filling a column with a pattern.",
  "level": "ambiguous"
}}
```

#############
Now it's your turn to decide! Remember, the levels to choose from are 'standard', 'pattern', or 'ambiguous'.
For reference, the valid tables and columns are:
{valid_cols}

_Conversation History_
{history}

_Output_
"""