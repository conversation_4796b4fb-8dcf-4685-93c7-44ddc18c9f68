insert_flow_prompt = """Given the conversation history and supporting details, your task is to determine what column (or row) to insert and how to populate its contents.
The previous state is given as the table name, followed by a list of column names. These represent the recently referenced data, which should be useful context.

Start by thinking about the reason for creating the new column and whether its clear how the column can be filled by a Pandas operation.
Based on this thought, generate an appropriate column name for insertion, along with the table in which it resides. When deciding on a name, consider the following:
  * What is the formatting convention based on the existing columns: underscores, spaces, or camelCase? lowercase or uppercase?
  * Do the other columns follow some sort of pattern? If so, what is the common theme? Perhaps, they tend to use abbreviations or acronyms?
  * Prefer intuitive names that hint at the column's purpose (ie. is_promising_lead) rather than literal names describing the contents (ie. score_greater_than_70).
  * If the user has declared a specific column name, then use that as the target. Otherwise, generate a new name matching the style of the existing columns.

If the target column is unclear, then label as 'unsure'. If the target table is unclear, then you can default to 'current'.
Your entire response should be in well-formatted JSON, including keys for thought (string), table (string), and target (string) with no further explanations after the JSON output.
Let's consider a few examples, and then tackle the current case.

## 1. Connected Column for Tracking Status
A common use case is status or indicator columns whose value changes depending on other columns.

_Conversation History_
User: and how many have more than 1000 loyalty points?
Agent: There are 193 customers with more than 1000 loyalty points.
User: OK, so let's mark customers with more than 1000 points and 10 purchases as high value, 500 points and 5 purchases as medium value, and everyone else as low.

_Previous State_
Customers - [CustomerID, FirstName, LastName, LoyaltyPoints]

_Output_
```json
{{
  "thought": "The new column will assign customer status as either high, medium or low value. This is determined by a combination of loyalty points and purchase amount. The column names capitalize the first letter and do not contain spaces.",
  "table": "Customers",
  "target": "CustomerValue"
}}
```

## 2. Snapshot Column for Storing Values
Another use case is creating a static column equivalent to copy/paste values without formulas

_Conversation History_
User: How many different social media platforms are we advertising on?
Agent: We are advertising on Twitter, Instagram, and Yelp.
User: I want the usernames for all the social media channels together in one column. Can you do that?

_Previous State_
CustomerRecords - [Twitter_Paid, Instagram_Paid, Yelp_Local]

_Output_
```json
{{
  "thought": "The new column will copy usernames over from existing rows that are related to social media platforms. The format uses underscores and title case.",
  "table": "CustomerRecords",
  "target": "Social_Media_Usernames"
}}
```

## 3. Snapshot Column for Saving Results
Another use case is moving the grouped results from a temporary table into the permanent table

_Conversation History_
User: Let me get the CTR of each version as well
Agent: Certainly, I have calculated the CTR for each copy variation. See table for details.
User: ok, i want to save this to the main table

_Previous State_
temporary table - [Total Clicks, Total Impressions, Copy Variation, Is Active]

_Output_
```json
{{
  "thought": "The new column saves the CTR in the main table. There should be a row for each copy variation. The columns use spaces for separation, and written in title case.",
  "table": "current",
  "target": "Click Through Rate"
}}
```

## 4. Connected Column for Making Comparisons
Another use case is creating a column for making comparisons between different rows

_Conversation History_
User: how many did we get the previous month?
Agent: March had 2309 MQLs which is 12% lower than April.
User: Actually, can you add a column showing the change every month from last year to this year?

_Previous State_
QualifiedLeads - [channel_id, mq_leads, month]
ChannelsWithSpend - [channel_id, channel_name]

_Output_
```json
{{
  "thought": "The new column will track the change in MQLs on a month-to-month basis. The values will be calculated using the formula (current month - previous month) / previous month. The format uses underscores and lowercase.",
  "table": "QualifiedLeads",
  "target": "monthly_mql_change"
}}
```

## 5. Placeholder Column for Ambiguous Values
Placeholders columns are useful for holding unstructured customer feedback or other freeform notes. They are also great when the values to fill are unclear.

_Conversation History_
User: Do we have any data about customer contact information?
Agent: It seems we have the customer email available.
User: Can you also add something that we can fill with the customer's phone number?

_Previous State_
Bookings - [CustomerID, CustomerEmail]

_Output_
```json
{{
  "thought": "The new column will hold the customer's phone number. However, I do not see a way to fill it using existing columns. The column pattern is to use the word 'Customer' followed by other details.",
  "table": "Bookings",
  "target": "CustomerPhone"
}}
```

## 6. Connected Column for Calculating Formulas
Columns are often created as a result of some calculation, where the formula will be stored as a Pandas operation.

_Conversation History_
User: how many active members do we have right now?
Agent: We have 395 active members right now. See table for details.
User: What if I want to know how long someone has been a member with us at Sixby Fitness

_Previous State_
Members - [memberID, firstName, lastName, startDate, endDate]

_Output_
```json
{{
  "thought": "The new column will calculate the duration of membership. The values can be calculated using the formula endDate - startDate. The format uses camelCase without spaces.",
  "table": "Members",
  "target": "membershipDuration"
}}
```
---
## Current Scenario
Now, let's apply this logic to our current scenario. Please think about the reason for creating the new column and the name of the column.
When in doubt, the user's final utterance takes precedence over any other supporting information. As reference for naming style, the other tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Previous State_
{prior_state}

_Output_
"""

insert_methods = """ Finally, decide on the method for populating the target column. Note there are three main methods for creating columns:
 1. placeholder - only a name with empty contents, values are intended to be filled manually or are otherwise unclear
 2. snapshot - derived from other columns but standalone afterwards, values are static with regards to the original columns
 3. connected - creates a dependency on other columns, values change along with the source columns according to some formula

Just like Core Results, the operations prediction is currently terrible, so we don't rely on it yet
Operations: filter ad cost > 1000
Operations: insert a new column that is the joined date
Operations: insert a new column that is the exchange rate for China
Operations: insert a column to track new users
Operations: insert a new column that combines the review status and the approval status
Operations: calculate the revenue from Pro members in January
Operations: calculate the retention rate for each subscription plan
Operations: insert a new column that is the difference between the two URLs
Operations: insert a new column that adds together the price and the fee
"""

delete_flow_prompt = """Given the conversation history, your task is to determine what data the user wants to remove.
Start by thinking out loud about what Pandas operations are most applicable to the request and which tables, rows, and columns to target for removal.
If the user wants to remove rows based on some criteria, consider which columns are most relevant for filtering.
If we are deleting an entire row, then use '*' for the column to indicate all columns.

You are given all available table and column names, so only choose from those options, and matching the exact spelling and capitalization.
When in doubt, you should default to 'unsure' for the table or column names rather than making assumptions. If the entire target is unclear, then leave the list empty.
Your entire response should be in well-formatted JSON including keys for thought (string) and result (list), where each item is a dict with table (tab), column (col), and row (row) keys.
There should be no further explanations or comments after the JSON output.

For example,
---
_Available Data_
* Tables: DeliveryStatus, Product
* Columns: DeliveryID, DeliveryStatus, SendDate, ReceiveDate, RecipientName, RecipientAddress, ProductID, ProductName, ProductDescription

_Conversation History_
User: I want take a quick look at the delivery status
Agent: Certainly, here is the DeliveryStatus table.
User: We don't need the SendDate or ReceiveDate columns anymore

_Output_
```json
{{
  "thought": "I should drop the SendDate and ReceiveDate columns",
  "result": [
    {{"tab": "DeliveryStatus", "col": "SendDate", "row": "all"}},
    {{"tab": "DeliveryStatus", "col": "ReceiveDate", "row": "all"}}
  ]
}}
```

_Available Data_
* Tables: activities
* Columns: activity_type, count

_Conversation History_
User: What's the most common activity type?
Agent: The most common activity type is 'view_product', which occured 2,835 times.
User: can you drop all activities that have blank or null UserAgent?

_Output_
```json
{{
  "thought": "I can remove blank UserAgent by searching for an empty string. Then, I will remove UserAgent with null using dropna.",
  "result": [
    {{"tab": "activities", "col": "UserAgent", "row": "= null || ''"}}
  ]
}}
```

_Available Data_
* Tables: temporary table
* Columns: year, state, sales_by_state_in_2022

_Conversation History_
User: Can I see just the top 3 states from that list?
Agent: The top three states were CA: $1,103,000, NY: $849,000 and TX: $811,000. See the table for more.
User: Can you delete extra characters from the state names so they are all abbreviated correctly?

_Output_
```json
{{
  "thought": "Renaming states into their correct abbreviations often entails deleting characters, but this is actually an update operation rather than a deletion.",
  "result": [ ]
}}
```

_Available Data_
* Tables: orders
* Columns: OrderID, OrderStatus, PurchaseTime

_Conversation History_
User: Sure, let's take a look
Agent: The purchase time for order 20533B was 2022-01-01 00:00:00.
User: Got it, probabaly used that as a default date. We should just get rid of anything older than 2023.

_Output_
```json
{{
  "thought": "I should remove all rows with purchase times strictly less than 2023.",
  "result": [
    {{"tab": "orders", "col": "PurchaseTime", "row": "< 2023-01-01"}}
  ]
}}
```

_Available Data_
* Tables: Enrollment_Metrics
* Columns: EnrollmentStartDate, EnrollmentEndDate, NumCompletions, StudentCount, Tuition, CompletionTime in Enrollment_Metrics

_Conversation History_
User: what data do we have concerning course completions?
Agent: The Enrollment_BB table contains NumCompletions and CompletionTime. Is that what you are looking for?
User: I just realized the final row is just a summary. Can we get rid of it?

_Output_
```json
{{
  "thought": "We should remove the last row in the Enrollment_BB table.",
  "result": [
    {{"tab": "Enrollment_BB", "col": "*", "row": "last"}}
  ]
}}
```

_Available Data_
* Tables: Pardot
* Columns: UserSignups, SignUpDate, EmailAddress, Fees Paid, LastLogin

_Conversation History_
User: what's the campaign with the highest number of signups?
Agent: The Grand Total campaign has the highest number of signups with 366,431. See table for more.
User: Bro, that's just the total line. Can you just get rid of the last row?

_Output_
```json
{{
  "thought": "The user is asking to delete all rows where Fees Paid is 0 in the Pardot table.",
  "result": [
    {{"tab": "Pardot", "col": "Fees Paid", "row": "= 0"}}
  ]
}}
```

_Available Data_
* Tables: LinkedInSignupsFinal; MixpanelUserEvents
* Columns: SignUpDate, EmailAddress, ViewedStatus, LastLogin, Signup_old, Username_old in LinkedInSignupsFinal;
event_name, event_time, user_id, event_properties, time_spent_on_page, new_username, old_username, referral_source in MixpanelUserEvents

_Conversation History_
User: what data do we have concerning signups status?
Agent: The LinkedInSignupsFinal table contains SignUpDate, ViewedStatus, and LastLogin. See table for more.
User: We should just get rid of all columns ending with '_old'

_Output_
```json
{{
  "thought": "I should delete all columns ending with '_old' in LinkedInSignupsFinal. There 'old_username' column contains 'old', but it *starts* with old and is also in a different table, so that does not fit the pattern described.",
  "result": [
    {{"tab": "LinkedInSignupsFinal", "col": "Signup_old", "row": "header"}},
    {{"tab": "LinkedInSignupsFinal", "col": "Username_old", "row": "header"}}
  ]
}}
```

_Available Data_
* Tables: recent_rentals, locations
* Columns: square_feet, price, location_id, city

_Conversation History_
User: What are the cities with the most expensive rentals?
Agent: Los Angeles has the most expensive rentals at $4,230 per square foot. Seattle is next at $3,900 and Miami third at $3,500 per square foot. See table for more.
User: Could you remove the comma?

_Output_
```json
{{
  "thought": "The square_feet and price columns both have values in the thousands which contain commas. City and state are also commonly separated by commas, so the request is ambiguous.",
  "result": [
    {{"tab": "locations", "col": "unsure", "row": "all"}}
  ]
}}
```
---
Now it's your turn, start with a concise thought followed by a list of tabs, cols, and rows. 
The table and column output must exactly match the names listed under "_Available Data_" with no further explanations or text after the JSON output.

_Available Data_
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

transpose_flow_prompt = """Based on the conversation history, your task is decide what row or column(s) the user is trying to transpose, and in which direction.
Given the valid tables and columns, decide whether the entire table should be rotated or just specific rows or columns.
Also, decide if we are turning rows to columns or vice versa. If any part is unclear, mark as 'unsure' rather than making assumptions.

Start by constructing a concise thought concerning what is being transposed and the direction of the transformation.
Then, choosing only from valid tables and columns, output the source and direction of the data movement in well-formatted JSON.
Your entire response should cover the thought (string), table (string), column (list) and direction (string), with no further explanations after the JSON output.

For example,
---
## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

cut_and_paste_prompt = """Based on the conversation history, your task is to decide what row or column(s) the user is trying to move and where they are moving it to.
To help with this decision, you will be given the name and position of all the columns in the existing tables.
You should then output the source (dict) and destination (integer) of the data movement. If the source or destination is unclear, please output an empty list rather than making assumptions.

Start by constructing a concise thought concerning what is being moved and where it is being moved to.
Then, choosing only from valid tables and columns, generate the list of relevant moves needed to execute the user's request.
Each 'move' is a dict with keys for source (containing tab, row, col) and destination (containing the new position).
Moving something to the top or bottom can be referred to using '0' or '-1' respectively, just like with Python indexing.
If the destination cannot be cleaning described with an integer, then set it as '?' to indicate uncertainty.
Your entire response should be in well-formatted JSON including keys for thought (string) and moves (list), with no further explanations after the JSON output.

For example,
#############
Suppose the tables and columns are:
* Tables: CustomerContact, CustomerOrders, MarketingOffers
* Columns: CustomerID (1), CustName (2), FavCuisineType (3), ShippingAddress (4), ContactNumber (5), IsActive (6), Twitter (7), Instagram (8), Yelp (9), Google (10) in CustomerContact;
OrderID (1), CustomerID (2), RestaurantID (3), DeliveryStatus (4), TotalAmount (5), DeliveryAddress (6), OrderStatus (7), EstDeliveryTime (8), SpecialInstructions (9) in CustomerOrders;
OfferID (1), OfferTitle (2), OfferDescription (3), OrderKey (4), StartDate (5), EndDate (6), DiscountAmount (7), ApplicableRestaurants (8), RedemptionCode (9) in MarketingOffers

_Conversation History_
User: Do we have any data about when deliveries were made?
Agent: We have EstDeliveryTime and DeliveryStatus columns in the CustomerOrders table.
User: OK, can you move the delivery status to right after the order status?

_Output_
```json
{{
  "thought": "The users is moving the DeliveryStatus column to the 7th position in the CustomerOrders table.",
  "moves": [{{
    "source": {{"tab": "CustomerOrders", "row": "*", "col": "DeliveryStatus"}},
    "destination": 7
  }}],
}}
```

_Conversation History_
User: How many users to we have that are active and also have a Twitter account?
Agent: There are 6423 active users who also have a Twitter account.
User: Let's bring all customers who are active to the top of the table.

_Output_
```json
{{
  "thought": "The user is moving rows where IsActive is true within the CustomerContact table.",
  "moves": [{{
    "source": {{"tab": "CustomerContact", "row": "IsActive == True", "col": "*"}},
    "destination": 0
  }}],
}}
```
#############
For this next scenario, suppose we have:
* Tables: sales_leads, data_sources, subscriptions, user_activity
* Columns: lead_id (1), email (4), first_name (2), last_name (3), organization (5), lead_source (6), contact_date (7), phone_number (8), notes (9), follow_up_date (10) in sales_leads;
launch_id (1), is_secure (2), provenance (3), version (4), features (5), documentation_link (6) in data_sources;
subscription_id (1), user_id (2), plan_name (3), sub_timestamp (4), billing_cycle (5), payment_status (6), renewal_notice (7) in subscriptions;
activity_id (1), user_id (2), activity_type (3), timestamp (4), duration (5), data_source (6), outcome (7), error_log (8) in user_activity

_Conversation History_
User: How many leads are we working with here?
Agent: There are 461 leads in the sales_leads table.
User: I'd like to see the first and last name columns up front, followed by email and phone.

_Output_
```json
{{
  "thought": "We are moving multiple rows in the sales_leads table to the front of the table.",
  "moves": [{{
    "source": {{"tab": "sales_leads", "row": "*", "col": "first_name"}},
    "destination": 1
  }}, {{
    "source": {{"tab": "sales_leads", "row": "*", "col": "last_name"}},
    "destination": 2
  }}, {{
    "source": {{"tab": "sales_leads", "row": "*", "col": "email"}},
    "destination": 3
  }}, {{
    "source": {{"tab": "sales_leads", "row": "*", "col": "phone_number"}},
    "destination": 4
  }}],
}}
```

_Conversation History_
User: What is the user_id about? Is that connected to anything?
Agent: The structure seems similar to the lead_id in the sales_leads table, but I'm not sure.
User: Anyways, can we move them to the end of the subs and activity?

_Output_
```json
{{
  "thought": "The user wants to move the user_id column to the end of the subscriptions and user_activity tables.",
  "moves": [{{
    "source": {{"tab": "subscriptions", "row": "*", "col": "user_id"}},
    "destination": -1
  }}, {{
    "source": {{"tab": "user_activity", "row": "*", "col": "user_id"}},
    "destination": -1
  }}],
}}
```
#############
As a final scenario, suppose the tables and columns are:
* Tables: Customers, Campaigns, Promotions
* Columns: CustomerID (1), FirstName (2), LastName (3), Username (4), Rating (5), PurchaseHistory (6), PurchaseAmount (7), PointAccumulated (8), Address (9), Registered (10) in Customers;
CampaignID (1), CampaignName (2), StartDate (3), EndDate (4), Channel (5), TargetAudience (6), Budget (7), ResponseRate (8), CombinedChannel (9) in Campaigns;
PromoID (1), PromoName (2), LaunchDate (3), PromoChannel (4), DiscountPercentage (5), ApplicableProducts (6), PromoCode (7), RedemptionCount (8) in Promotions

_Conversation History_
User: Can you make a new column that combines the campaign name and the channel?
Agent: Sure, I have added a new CombinedChannel column. How does it look?
User: I want it right next to the current channel column

_Output_
```json
{{
  "thought": "We should move the CombinedChannel column to the 5th position in the Campaigns table.",
  "moves": [{{
    "source": {{"tab": "Campaigns", "row": "*", "col": "CombinedChannel"}},
    "destination": 5
  }}],
}}
```

_Conversation History_
User: Sort the promos by the discount rate
Agent: Done. Just so you know this can also be done directly in the table by clicking on the column header.
User: yea got it. Now swap the order of the discount rate and the applicable product columns

_Output_
```json
{{
  "thought": "The user is requesting to swap the DiscountPercentage and ApplicableProducts columns in the Promotions table.",
  "moves": [{{
    "source": {{"tab": "Promotions", "row": "*", "col": "DiscountPercentage"}},
    "destination": 6
  }}, {{
    "source": {{"tab": "Promotions", "row": "*", "col": "ApplicableProducts"}},
    "destination": 5
  }}],
}}
```
#############
Now it's your turn to decide where to move the data. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

split_column_prompt = """We are helping a user perform a text-to-columns operation within a dataframe.
Given the conversation history and available columns, determine which source column and table the user is trying to split and the delimiter used for the split.
We also want to identify the target columns that the user wants to create. If source or delimiter is unclear, then label as 'unsure'. If the target is unclear, then leave the list empty.
Your entire response should be in well-formatted JSON with keys for thought (string), source (string), table (string), delimiter (string) and targets (list).
Avoid including any extra explanations or comments after the JSON output.

For example,
---
_Conversation History_
User: What are the usernames for people with a lead score above 80?
Agent: I'm sorry, I couldn't find a username column in any of the tables. What should I do?
User: The usernames are in the email column. Split it into username and domain.

_Valid Columns_
* Tables: PardotAutomation; TransactionHistory; InteractionLogs
* Columns: cust_id, signup_date, cust_name, email, delivered, region, lead_score, channel, acct_status in PardotAutomation;
trans_id, cust_id, trans_date, product_id, amount, trans_type, license_fee, service_charge, maintenance_income in the TransactionHistory;
interaction_id, cust_id, interact_date, interact_type, interact_duration, issue_resolved, expenses in the InteractionLogs

_Output_
```json
{{
  "thought": "I should split the email column in PardotAutomation.",
  "source": "email",
  "table": "PardotAutomation",
  "delimiter": "@",
  "targets": ["username", "domain"]
}}
```

_Conversation History_
User: Are there any users with activity in the last month where the payment status is 'overdue'? Btw, we should exclude anything from the Pilot Tier since those were early test accounts.
Agent: I tried to join the subscriptions and user_activity tables based on the user_id and user_plan columns, but received an error. Any ideas?
User: Go with plan_name instead, which contains the subscription tier and type. user plan is just the tier part.

_Valid Columns_
* Tables: mq_leads, product_launches, subscriptions, user_activity
* Columns: lead_id, first_name, last_name, email, organization, lead_source, contact_date, status, notes, follow_up_date in mq_leads;
launch_id, is_secure, provenance, version, features, documentation_link in data_sources;
subscription_id, user_id, plan_name, sub_timestamp, billing_cycle, payment_status, renewal_notice in subscriptions;
activity_id, activity_type, user_plan, timestamp, duration, data_source, outcome, error_log in user_activity

_Output_
```json
{{
  "thought": "In order to join the subscriptions and user_activity tables, I will first need to extract the subscription tier from the plan name.",
  "source": "plan_name",
  "table": "subscriptions",
  "delimiter": "unsure",
  "targets": ["subscription_tier", "subscription_type"]
}}
```

_Conversation History_
User: See how we have start dates and termination dates for the campaigns?
Agent: Yes, there is campaignInitDate and campaignTermDate in the GoogleAds_Q3 table. What would you like to know?
User: Can we divide the init date into separate columns?

_Valid Columns_
* Tables: GoogleAds_Q3, SalesRecord_Shopify_0812, Product_Details
* Columns: gAd_ID, clickCount, campaignInitDate, campaignTermDate, userActivity, adBounceRate, adSpend, adContentCode, referrerURL in GoogleAds_Q3;
orderRef, prodSKU, appointmentDate, acquisitionCost, buyerID, gAdRef, revenueGenerated, unitsMoved, fulfillmentStatus, customerNotes in SalesRecord_Shopify_0812;
SKU, itemName, itemCategory, retailPrice, totalCost, stockLevel in Product_Details

_Output_
```json
{{
  "thought": "I should split the campaignInitDate column in GoogleAds_Q3.",
  "source": "campaignInitDate",
  "table": "GoogleAds_Q3",
  "delimiter": "unsure",
  "targets": ["initYear", "initMonth", "initDay"]
}}
```

_Conversation History_
User: Which ad platform resulted in the highest view counts?
Agent: FacebookAds resulted in the highest view counts, followed by Google and LinkedIn.
User: Let's break apart the different user activities into their own columns

_Valid Columns_
* Tables: AdobeAnalytics_final; SubscriptionMembership; Canva Content (revised); VendorExpenses
* Columns: video_id, trainer_id, video_campaign_id, creation_date, video_type, trainer_fee, impressions in Canva Content (revised);
member_id, subscription_date, renewal_date, subscription_tier, monthly_fee, activity, member_status in SubscriptionMembership;
vendor_id, service_provided, expense_date, expense_amount, vendor_category in VendorExpenses
campaign_id, ad_platform, ad_spend, ad_type, ad_copy, user_activity, view_count, cost_per_click in AdobeAnalytics_final;

_Output_
```json
{{
  "thought": "I should split the user_activity column in AdobeAnalytics_final.",
  "source": "user_activity",
  "table": "AdobeAnalytics_final",
  "delimiter": "unsure",
  "targets": []
}}
```

_Conversation History_
User: I'm going to create a campaign where we show content based on their favorite cusine type.
Agent: Sure, how would you like to proceed?
User: So favorite cuisine is a comma-separated list, we should start by splitting it into individual columns

_Valid Columns_
* Tables: CustContact, CustOrders, MarketingOffers
* Columns: CustomerID, CustName, FavCuisineType, ShippingAddress, ContactNumber, IsActive, Twitter, Instagram, Yelp in CustContact;
OrderID, CustomerID, RestaurantID, OrderDate, TotalAmount, DeliveryAddress, OrderStatus, EstDeliveryTime, SpecialInstructions in CustOrders;
OfferID, OfferTitle, OfferDescription, OrderKey, StartDate, EndDate, DiscountAmount, ApplicableRestaurants, RedemptionCode in MarketingOffers

_Output_
```json
{{
  "thought": "I should split the FavCuisineType column in CustContact.",
  "source": "FavCuisineType",
  "table": "CustContact",
  "delimiter": ",",
  "targets": []
}}
```
---
_Conversation History_
{history}

_Valid Columns_
{valid_tab_col}

_Output_
"""

materialize_view_prompt = """Given the conversation history, your task is to decide what table the user is trying to save and where to store it.
Storage locations include:
  * memory - materialize a temporary view as a permanent view which allows for further direct processing
  * disk - export a table to the user's computer or cloud storage as a CSV for external use

If the user is not trying to save a table, then mark the location as 'unsure'.
To assist in this decision, you will also be given a preview of the temporary view that the user is working with.
Sometimes, a temporary view is not available, in which case the storage location is surely 'disk' or 'unsure'.

Start by thinking about the user's request and the most appropriate storage method.
Then, give the name of the table to save. If the temporary table is being saved to memory, you will need to compose a new table name.
When constructing a table name, ensure it is formatted according to the conventions (ie. capitalization) found in existing tables.
The new table name should aim to be less than three tokens, and should never be more than four tokens.
Your entire response should be well-formatted JSON including keys for thought (string), storage (string), and table (string), with no further explanations after the JSON output.

For example,
---
## Scenario 1
Valid tables: NVDA, AAPL, GOOG, NFLX
| month_year | high | low  | close_price | monthly_volume |
|------------|------|------|-------------|----------------|
| 2023-09    | 385  | 360  | 375         | 1360000        |
| 2023-10    | 395  | 370  | 386         | 1270000        |
| 2023-11    | 420  | 385  | 410         | 1450000        |
| 2023-12    | 415  | 380  | 395         | 1320000        |
| 2024-01    | 412  | 388  | 400         | 1410000        |
| 2024-02    | 435  | 400  | 422         | 1350000        |
| 2024-03    | 442  | 415  | 430         | 1330000        |
| 2024-04    | 435  | 405  | 414         | 1390000        |

_Conversation History_
User: Can I get that on a monthly basis?
Agent: Sure, here you go
User: And show it on a graph again
Agent: No problem, here is the monthly price for Netflix.
User: Save that for me please

_Output_
```json
{{
  "thought": "By default, saving results go to memory. Table names seem to primarily be capitalized abbreviations.",
  "storage": "memory",
  "table": "MONTHLY_NLFX"
}}
```

## Scenario 2
Valid tables: Orders, CustomerContact, MarketingOffers
| Email                            | TotalBought |
|----------------------------------|-------------|
| <EMAIL>            | 511         |
| <EMAIL>            | 683         |
| <EMAIL>         | 912         |
| <EMAIL>     | 246         |
| <EMAIL>        | 776         |
| <EMAIL>              | 350         |
| <EMAIL> | 702         |
| <EMAIL>     | 507         |

_Conversation History_
User: How much did each of them buy in the last week? 
Agent: The top customers bought 912, 776, and 702 rolls last week.
User: Can we get rid of the converted currency column?
Agent: Sure, I have removed the converted currency column from MarketingOffers.
User: Yea, can we just save the updated table?

_Output_
```json
{{
  "thought": "The most recently discussed table is MarketingOffers, so I will go with saving that table.",
  "storage": "disk",
  "table": "MarketingOffers"
}}
```

## Scenario 3
Valid tables: startup_engine, angel_investors, venture_capital, accelerators
| investor_name | investment_amount | investment_date |
|---------------|-------------------|-----------------|
| Elad Gil      | 50000             | early 2023      |
| Sarah Wheatley| 75000             | 2023-01-15      |
| Chris Sacca   | unknown           |                 |
| Vincent Han   | 30000             | 2023-02-15      |
| TingYue Wang  | 25000             | 2023-03-01      |

_Conversation History_
User: who were the angel investors on the Capella deal?
Agent: Their seed round included Elad Gil, Sarah Wheatley, Chris Sacca, and 2 others. See table for details.
User: How come I can't edit the table?
Agent: Temporary tables are read-only. To make them editable, we need to first convert it to a permanent table.
User: Ok, do it then

_Output_
```json
{{
  "thought": "The user is asking to save the temporary table as a permanent table. Table names are in snake_case format.",
  "storage": "memory",
  "table": "capella_angels"
}}
```

## Scenario 4
Valid tables: Purchases Made, Customer Info, Loyalty Details
| Channel   | Points Redeemed |
|:----------|-----------------|
| Radio     | 22500           |
| Outdoor   | 29700           |
| Magazine  | 28200           |
| TV        | 15500           |
| Social    | 27000           |
| Online    | 31500           |
| Direct    | 20500           |

_Conversation History_
User: What if we grouped it by each channel?
Agent: Radio led to 22500 points redeemed, Outdoor let to 29700, and Magazine led to 28200. See table for more details.
User: So it seems like television is underperforming.
Agent: Yes, TV only had 15500 points redeemed.
User: Can we store this as a standalone table?

_Output_
```json
{{
  "thought": "The user convert the temporary table to a standalone table. Table names are proper case with spaces.",
  "storage": "memory",
  "table": "Points Redeemed by Channel"
}}
```
---
Remember, start with a concise thought followed by a storage location and table name. There should be no explanations or lessons after the JSON output.
This is very important: if saving to disk, choose only from valid tables, and if saving to memory, the new table name should *not* be a duplicate of an existing table.

## Current Scenario
Valid tables: {tables}
{preview}

_Conversation History_
{history}

_Output_
"""

join_tables_prompt = """As seen in the conversation history, our situation requires joining two tables together to form a new table.
Given the context and the valid table and columns to choose from, your task is to determine which ones are relevant to the conversation.
We are mainly concerned with the columns that are being joined together, but we should also capture any other columns that are mentioned.
The different join methods are:
  * person (PER) - join based on the customer or user; sometimes represented as a username, email, or phone number
  * organization (ORG) - join based on the company or business, such as a website, department, or company name
  * location (LOC) - join based on the geography or location, such as city, state, country, or address
  * date (DATE) - join based on the time such as a date, month, week, quarter, or timestamp
  * unique id (ID) - join based on an exact match or a direct lookup, such a foreign key pointing to a primary key
  * other (O) - any data that does not fit into the above categories, but are still part of the conversation

Please start by generating a concise thought concerning the potential columns and tables mentioned in the dialogue.
At this stage, prefer to include any columns that may be useful, rather than excluding them. If either table or column is unclear, then leave the result dict empty.
Remember that primary keys should be matched with a foreign key. Two primary keys make bad candidates for joining columns.
Your entire response should be in well-formatted JSON, including keys for thought (string) and result (dict).
The keys of the result dict is the join method, and the values are a list of table and column dicts. There should be no further explanations or comments after the JSON output.

For example,
---
_Conversation History_
User: We ran an updated user survey with new attributes. I want to figure out which promos those users saw to see if it affected their responses.
Agent: Sure, I'm trying to join the Promotions table with the SurveyMonkey table, but I hit some errors. Can you help me figure out which columns to use?
User: I'm not sure either, what columns do we have?

_Current Data_
PromoID, PromoName, StartDate, EndDate, DiscountPercentage, ApplicableProducts, PromoCode, RedeemedBy, RedemptionCount in activePromos;
SurveyID, ContactEmail, Date, SatisfactionScore, FreeFormComments, SurveyType, Duration, Location, LikelyToRecommend in surveyMonkey

_Output_
```json
{{
  "thought": "Possible methods include joining by email or date. We ignore the IDs, since joining based on two primary keys does not make sense. We also want to include data related which promos users saw.",
  "result": {{
    "PER": [ {{"tab": "activePromos", "col": "RedeemedBy"}}, {{"tab": "surveyMonkey", "col": "ContactEmail"}} ],
    "DATE": [ {{"tab": "activePromos", "col": "StartDate"}}, {{"tab": "activePromos", "col": "EndDate"}},  {{"tab": "surveyMonkey", "col": "Date"}} ],
    "O": [ {{"tab": "activePromos", "col": "ApplicableProducts"}}, {{"tab": "activePromos", "col": "PromoName"}} ]
  }}
}}
```

_Conversation History_
User: See how we have different status for each order?
Agent: Yes, the orders table has a DeliveryStatus column. What would you like to know?
User: Cross reference against recent purchases table to see if there is any sign of fraud.

_Current Data_
PurchaseID, Username, ProductID, PurchaseTime, Quantity, DiscountRate, Taxes, UnitPrice, PaymentMethod, ShippingAddress in Stripe_Updated;
OrderID, CustomerID, PurchaseKey, OrderDate, TotalAmount, Address, City, State, Zip, OrderStatus, Carrier, DeliveryDate, DeliveryStatus in JuneOrders;
ProductID, Rating, Review, Date, Username, ProductName, Price, Category, Description in Product Ratings

_Output_
```json
{{
  "thought": "We can possibly join the two tables based on purchase time, shipping address, or foreign key relationships. I also need to find columns relevant for detecting fraud.",
  "result": {{
    "DATE": [ {{"tab": "Stripe_Updated", "col": "PurchaseTime"}}, {{"tab": "JuneOrders", "col": "OrderDate"}}, {{"tab": "JuneOrders", "col": "DeliveryDate"}} ],
    "LOC": [ {{"tab": "Stripe_Updated", "col": "ShippingAddress"}}, {{"tab": "JuneOrders", "col": "Address"}}, {{"tab": "JuneOrders", "col": "City"}}, {{"tab": "JuneOrders", "col": "State"}}, {{"tab": "JuneOrders", "col": "Zip"}} ],
    "ID": [ {{"tab": "Stripe_Updated", "col": "PurchaseID"}}, {{"tab": "JuneOrders", "col": "PurchaseKey"}} ],
    "O": [ {{"tab": "Stripe_Updated", "col": "PaymentMethod"}}, {{"tab": "JuneOrders", "col": "TotalAmount"}}, {{"tab": "JuneOrders", "col": "OrderStatus"}}, {{"tab": "JuneOrders", "col": "DeliveryStatus"}} ]
  }}
}}
```

_Conversation History_
User: Can you sort the unresolved tickets based on their deal size? That should help us prioritize them.
Agent: I am trying to join the Zendesk and Salesforce tables to sort the tickets, but I can't find a common column. Can you help me figure out which columns to use?
User: We should create a new table that combines the data from both of them then

_Current Data_
TicketID, DateOpened, DateClosed, TicketType, Status, Priority, TicketSource, Requester, Category, Subject, Description, Resolution, Agent, TicketGroup, Tags, Comments in Zendesk;
OpportunityID, AccountID, ContactName, CloseDate, Amount, Stage, Probability, LeadSource, Type, Description, Owner, CreatedDate, LastModifiedDate, IsClosed, IsWon in Salesforce

_Output_
```json
{{
  "thought": "A customer support ticket is not the same as an account or opportunity, so the IDs are unlikely to be aligned. Instead, we can look at the dates and requester. I should also include columns related to deal size and resolution status.",
  "result": {{
    "DATE": [ {{"tab": "Zendesk", "col": "DateOpened"}}, {{"tab": "Salesforce", "col": "LastModifiedDate"}} ],
    "PER": [ {{"tab": "Zendesk", "col": "Requester"}}, {{"tab": "Salesforce", "col": "ContactName"}} ],
    "O": [ {{"tab": "Zendesk", "col": "Resolution"}}, {{"tab": "Zendesk", "col": "Status"}}, {{"tab": "Salesforce", "col": "Amount"}} ]
  }}
}}
```

_Conversation History_
User: Please create a joint table with just the member activities for those folks expiring this month.
Agent: I can use member_id and user_id to join the members and activities tables. What do you think?
User: So members are those who bought a premium package, which is listed as product_id 4316

_Current Data_
member_id, full_name, email_address, phone_number, date_joined, membership_type, membership_fee, expiration_date, packages_bought, emergency_contact in members;
activity_id, user_id, product_id, activity, send_timestamp, ad_cost, num_displays, num_views, num_clicks, payment_method, shipping_address, estimated_revenue in activities

_Output_
```json
{{
  "thought": "Members are identified through premium packages rather than member_id or user_id, so I will use packages_bought and product_id. I should also include columns related to the activities and expiration.",
  "result": {{
    "PER": [ {{"tab": "members", "col": "packages_bought"}}, {{"tab": "activities", "col": "product_id"}} ],
    "O": [ {{"tab": "members", "col": "expiration_date"}}, {{"tab": "activities", "col": "activity"}} ]
  }}
}}
```

_Conversation History_
User: You see how we have the analytics and spend data?
Agent: Yes, what would you like to do with them?
User: Does having more clicks leads to more revenue later on?

_Current Data_
EventTimestamp, Sessions, Users, Pageviews, PagesPerSession, AvgSessionDuration, BounceRate, Revenue, Transactions, EcommerceConversionRate in ga_08122003_08132003;
Campaign, AdGroup, Impressions, Clicks, Cost, AvgCPC, Conversions, ConversionRate, CostPerConversion, Month, Day, Year in adspend_week_19

_Output_
```json
{{
  "thought": "Joining data from two ad platforms is often only possible based on the time. I should also include columns related to clicks and revenue.
  "result": {{
    "DATE": [ {{"tab": "ga_08122003_08132003", "col": "EventTimestamp"}}, {{"tab": "adspend_week_19", "col": "Month"}}, {{"tab": "adspend_week_19", "col": "Day"}}, {{"tab": "adspend_week_19", "col": "Year"}} ],
    "O": [ {{"tab": "ga_08122003_08132003", "col": "Revenue"}}, {{"tab": "adspend_week_19", "col": "Clicks"}} ]
  }}
}}
```
---
Now let's tackle the current case. Think carefully about the relevant columns and tables, and then output them as a dict of results, with the join method as a key.

_Conversation History_
{history}

_Current Data_
{valid_tab_col}

_Output_
"""

append_flow_prompt = """Given the valid tables along with the conversation history, our goal is to determine which table(s) the user wants to connect together.
Start by thinking carefully about what type of connection should be used. Specifically, we define two types:
  * join - connect the source and target tables based on a common column; a foreign key relationship is often used
  * append - adding data from the source to target table directly, since most or all columns are shared

Furthermore, consider the order of the tables being connected. If the user is trying to join two tables, then the order does not matter.
However, when the user is trying to append data from one table to another, then the order is important.
Ideally, we would identify a semantic relationship between the tables, such that one naturally follows the other (ie. data from 2023 is appended to 2022)
As a backup, we can consider the alphabetical order of the table names. In the absence of any relationships, just list the tables in the order they were mentioned.

If the connection type is ambiguous, then default to 'append'. If the source tables are ambiguous, then output an empty list.
Your entire response should be in well-formatted JSON including keys for thought (string), method (string), and sources (list), with no further explanations after the JSON output.

For example,
---
## Time Period Scenario
* Tables: AugustEmails, OctoberEmails, SeptemberEmails, NovemberEmails
* Columns: Email ID, Campaign Name, Total Sends, Total Opens, Total Clicks, Total Unsubscribes, Total Bounces in AugustEmails;
Email ID, Campaign Name, Total Sends, Total Opens, Total Clicks, Total Unsubscribes, Total Bounces in OctoberEmails;
Email ID, Campaign Name, Total Sends, Total Opens, Total Clicks, Total Unsubscribes, Total Bounces in SeptemberEmails;
Email ID, Campaign Name, Total Sends, Total Opens, Total Clicks, Total Unsubscribes, Total Bounces in NovemberEmails;

_Conversation History_
User: Can you delete all the rows with 0 clicks?
Agent: No problem, I have removed them. There are now 624 rows left for August.
User: OK, now let's join all the data together into one table.

_Output_
```json
{{
  "thought": "Since all the tables have the same columns, we can append them together. A natural order would go from August to November.",
  "method": "append",
  "sources": ["AugustEmails", "SeptemberEmails", "OctoberEmails", "NovemberEmails"]
}}
```

## Conference Prospects Scenario
* Tables: GPU_Computing_Day1, Model_Evaluation_Day2, ML_Ops_Day3, Survey_Notes
* Columns: full name, company, role, email address, interest level, main product line in GPU_Computing_Day1;
full name, company, role, email address, interest level, main product line in Model_Evaluation_Day2;
full name, company, role, email address, interest level, main product line in ML_Ops_Day3;
prospect, contact_info, question, feedback, notes in Survey_Notes;

_Conversation History_
User: So I just uploaded the data from the AI Developer Conference
Agent: Yes, I see that. What would you like to do with it?
User: I'd like to combine all the data from each day into one table. Is that possible?

_Output_
```json
{{
  "thought": "The tables represent data collected from a conference, where three of them share the same columns. The Survey_Notes table would be excluded. We can order the tables from Day 1 to Day 3.",
  "method": "append",
  "sources": ["GPU_Computing_Day1", "Model_Evaluation_Day2", "ML_Ops_Day3"]
}}
```

## Social Media Scenario
* Tables: Weekly Report (social), Weekly Report (email), Weekly Report (paid), LinkedIn_download, Twitter_download, Tiktok_download
* Columns: report_date, total_engagement, total_impressions, total_clicks, high_clicks, platform_spend, conversion_rate, follower_growth, content_count in Weekly Report (social);
report_date, open_rate, click_rate, bounce_rate, total_sends, unsubscribe_rate, revenue_generated, campaign_count in Weekly Report (email);
report_date, ad_spend, cost_per_click, total_clicks, return_on_spend, impression_count, conversion_value, campaign_active in Weekly Report (paid);
post_date, sponsored_content, organic_content, company_followers, engagement_rate, click_through_rate, job_applies, impressions_delivered in LinkedIn_download;
tweet_datetime, retweet_count, like_count, impression_count, profile_clicks, url_clicks, reply_count, media_engagements in Twitter_download;
video_date, view_count, share_count, comment_count, play_duration, total_likes, follower_growth, engagement_rate in Tiktok_download

_Conversation History_
User: Now let's add a column which signifies whenever the number of clicks is greater than 1000.
Agent: Sure, do you know what you would like to name this column?
User: Let's call it 'high_clicks'
Agent: OK, I have added the column.
User: I'd like to append the LinkedIn data to the social media report as well.

_Output_
```json
{{
  "thought": "Despite the request to 'append' LinkedIn data, the columns between the tables are clearly not compatible, so a join is more appropriate.",
  "method": "join",
  "sources": ["Weekly Report (social)", "LinkedIn_download"]
}}
```

## Campaign Optimization Scenario
* Tables: FamilyProducts, SeasonalPromotions, BrandedTerms
* Columns: fp_id, campaign_name, ad_group, impressions, interactions, allowed_budget, spent, total_clicks in FamilyProducts;
sp_id, campaign_name, ad_group, impr., interactions, allowed_budget, spent, total_clicks in SeasonalPromotions;
bt_id, campaign_name, ad_group, impr., interactions, allowed_budget, spent, total_clicks in BrandedTerms;

_Conversation History_
User: Let's rename it to just 'impressions' to keep it simple.
Agent: OK, I have renamed the column to 'impressions'. How does this look?
User: Cool, now can I merge the three campaigns together?

_Output_
```json
{{
  "thought": "The columns are not exactly the same, but the column names are aligned in meaning, so we are appending. The campaigns are categorical, so there is no clear order - we can just list them alphabetically.",
  "method": "append",
  "sources": ["BrandedTerms", "FamilyProducts", "SeasonalPromotions"]
}}
```

## CRM Scenario
* Tables: ms_dynamics_all, account_info, customers_list, qualified_leads, opportunities_cold, opportunities_warm
* Columns: email_address, first_name, last_name, company_name, job_title, phone_number, lead_source, last_contact_date, lead_score, status, lead_owner in ms_dynamics_all;
account_id, company_name, industry, annual_revenue, account_owner, territory, customer_since, payment_terms, account_status, primary_contact_email in account_info;
customer_id, business_name, contact_person, email, subscription_tier, signup_date, billing_cycle, last_purchase_date, lifetime_value, account_manager, active_status in customers_list;
Email_Address, First_Name, Last_Name, Company_Name, Job_Title, Phone_Number, Lead_Source, Last_Contact_Date, Lead_Score, Status, Lead_Owner in qualified_leads;
email_address, first_name, last_name, company, job_title, phone_number, lead_source, last_contacted, lead_score, lead_status, lead_owner in opportunities_cold;
email_address, first_name, last_name, company, job_title, phone_number, lead_source, last_contacted, lead_score, lead_status, lead_owner in opportunities_warm;

_Conversation History_
User: I need to full list of emails so I can blast them all with our latest offer, can you pull that for me?
Agent: There are multiple tables with email addresses, which one would you like?
User: Ok, let's start by extending the CRM data with the latest batch of leads from the AEs

_Output_
```json
{{
  "thought": "The CRM table most likely refers to ms_dynamics_all, so that should go first, while the leads tables can be appended afterwards. The account and customers tables are missing key columns (ie. first and last names), so they are excluded.",
  "method": "append",
  "sources": ["ms_dynamics_all", "qualified_leads", "opportunities_cold", "opportunities_warm"]
}}
```
---
Now it's your turn! Please think carefully about the relevant tables for connection, and then output the method along with the source tables in the appropriate order.
When choosing the sources, copy the table names exactly from the valid options, making sure to preserve any formatting, capitalization, or spelling.

## Real Scenario
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

merge_columns_prompt = """Given the conversation history and valid columns, your task is to determine which source columns the user is pulling from to create a new target column.
Please start by generating a concise thought concerning the relevant columns and tables mentioned in the final turn of dialogue.
Then, output a list of table and column dicts. If either field is unclear, then label as 'unsure'.
Your entire response should be in well-formatted JSON, including keys for thought (string) and result (list). There should be no further explanations or comments after the JSON output.

For example,
---
_Conversation History_
User: You see how we have the cost and the revenue columns?
Agent: Yes, what would you like to do with them?
User: I want to create a new column that is true when the total return is positive, and false otherwise.

_Valid Columns_
activity_id, user_id, product_id, send_timestamp, ad_cost, num_displays, num_views, num_clicks, payment_method, shipping_address, estimated_revenue in UserActivities;
order_id, customer_id, order_date, total_amount, address, city, state, zip, order_status, carrier, delivery_date, delivery_status in JuneOrders;
product_id, description, rating, review, date, username, product_name, retail_price, category, sub_category, supplier in Product_Ratings

_Output_
```json
{{
  "thought": "The user wants to calculate the total return based on the ad_cost and estimated_revenue columns.",
  "result": [
    {{"tab": "UserActivities", "col": "ad_cost"}},
    {{"tab": "UserActivities", "col": "estimated_revenue"}}
  ]
}}
```

_Conversation History_
User: I'd like to merge the duplicate columns together
Agent: Which columns are you referring to?
User: The food liked is actually pretty much the same as the favorite cuisine type

_Valid Columns_
CustomerID, CustName, FavCuisineType, ShippingAddress, ContactNumber, FoodLiked, IsActive, Twitter, Instagram, Yelp in CustomerContact;
PurchaseID, Username, RestaurantID, PurchaseTime, Quantity, DiscountRate, Taxes, DishName, PaymentMethod, BankAuthorization in Stripe_Updated;

_Output_
```json
{{
  "thought": "The user wants to merge the FoodLiked and FavCuisineType columns together.",
  "result": [
    {{"tab": "CustomerContact", "col": "FoodLiked"}},
    {{"tab": "CustomerContact", "col": "FavCuisineType"}}
  ]
}}
```

_Conversation History_
User: I want to make a new event type by combining the event name, time, and device used.
Agent: Is this created by just joining the values together with a space?
User: Yea that works. And also add the browser type too.

_Valid Columns_
transaction_id, items_purchased, se_id, transaction_timestamp, paid_amount, tax_amount, shipping_cost, order_status in customer_transactions;
user_id, event_name, event_timestamp, session_id, device_type, os_version, browser, page_url, time_spent_on_page, cart_value, items_in_cart, referral_source in MixpanelActivity;
feedback_id, user_id, submission_date, overall_rating, ease_of_use, product_quality_rating, shipping_experience, score, comments, survey_completion_time, feedback_category, resolved_status, agent_assigned in Intercom

_Output_
```json
{{
  "thought": "The user wants to create a new column by combining the event name, timestamp, device type, and browser.",
  "result": [
    {{"tab": "MixpanelActivity", "col": "event_name"}},
    {{"tab": "MixpanelActivity", "col": "event_timestamp"}},
    {{"tab": "MixpanelActivity", "col": "device_type"}},
    {{"tab": "MixpanelActivity", "col": "browser"}}
  ]
}}
```

_Conversation History_
User: What is the most common activity type?
Agent: The most common activity type is 'pageview', which occured 2,835 times.
User: Let's make a new entry that measures the average time spent on each activity.

_Valid Columns_
EventID, UserID, ActivityType, ActivityTime, DeviceType, BrowserType, IP_Address, UserAgent in AmplitudeEvents;
ccid, profile_type, email, phone, first_name, last_name, address, country, company, title, age in rudderstack_enriched;
GA_Key, event_name, clicK_timestamp, ip_address, page_url, page_title, author, category, header, content, ad_group, terms, keywords in May2024_GA4_reviewed

_Output_
```json
{{
  "thought": "In order to calculate the average time spent on each activity, the most relevant columns are likely ActivityType and ActivityTime.",
  "result": [
    {{"tab": "AmplitudeEvents", "col": "ActivityType"}},
    {{"tab": "AmplitudeEvents", "col": "ActivityTime"}}
  ]
}}
```
---
Now let's tackle the current case. Think carefully about the relevant columns and tables, and then output them as a list.

_Conversation History_
{history}

_Valid Columns_
{valid_tab_col}

_Output_
"""

stage_table_prompt = """Given the valid tables and columns along with the conversation history, your task is to stage data in a direct table for further processing.
Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.

Your entire response should be in well-formatted JSON including keys for thought (string) and result (list), with no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Placeholder Scenario
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach

## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

call_external_api_prompt = """Given the valid tables and columns along with the conversation history, your task is to call an external API to retrieve a new table.
Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.

Your entire response should be in well-formatted JSON including keys for thought (string) and result (list), with no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Placeholder Scenario
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach

## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""
