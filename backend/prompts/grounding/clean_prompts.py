update_flow_prompt = """Given the conversation history and previous state, your task is to determine what part of the table the user wants to clean and how to update it.
The previous state is given as the table name, followed by a list of column names. These represent the recently referenced data, which should be useful context.

Please start by thinking out loud about the how we can clean the data and what specific operations are needed to achieve this.
Your entire response should be in well-formatted JSON including keys for thought (string), source (list), and target (list). There should be no further explanations after the JSON output.
Both the source and target should be a list of dicts with keys for tab (string), col (string), and row (string).
Note that columns are specified by their names while rows are often specified by their values. Rows with formulas should be preceded by an equals sign.

For example,
---
## 1. Update Value in Column
_Conversation History_
User: Which anomalies?
Agent: The impressions column contains blank values such as N/A, Unknown, and Unknown.
User: Change all of those into just zero.

_Previous State_
Landing Page - [impressions]

_Output_
```json
{{
  "thought": "I should replace 'Unknown' and 'N/A' into 0 in the impressions column.",
  "source": [
    {{"tab": "Landing Page", "col": "impressions", "row": "Unknown"}},
    {{"tab": "Landing Page", "col": "impressions", "row": "N/A"}}
  ],
  "target": [
    {{"tab": "Landing Page", "col": "impressions", "row": "0"}}
  ]
}}
```

## 2. Update Multiple Columns
_Conversation History_
User: Can you please rewrite both shopify and Shopifi into Shopify.
Agent: Sure, I have rewritten both shopify and Shopifi into Shopify. I also found Stopify, should I change that as well?
User: Yes, that's a good find.

_Previous State_
Inventory - [OnlineVendors, ActiveVendors]

_Output_
```json
{{
  "thought": "Both OnlineVendors and ActiveVendors columns should be updated by replacing all instances of 'Stopify' with 'Shopify'.",
  "source": [
    {{"tab": "Inventory", "col": "OnlineVendors", "row": "Stopify"}},
    {{"tab": "Inventory", "col": "ActiveVendors", "row": "Stopify"}}
  ],
  "target": [
    {{"tab": "Inventory", "col": "OnlineVendors", "row": "Shopify"}},
    {{"tab": "Inventory", "col": "ActiveVendors", "row": "Shopify"}}
  ]
}}
```

## 3. Update Multiple Rows
_Conversation History_
User: How do determine when AccountsPayable is overdue?
Agent: AccountsPayable is overdue if the DueDate is before June 1st.
User: Can you update this to September 1st instead?

_Previous State_
OrderAgreements - [AccountsPayable, DueDate, OrderID]
Vendors - [VendorID, VendorName]

_Output_
```json
{{
  "thought": "I should update the formula in the AccountsPayable column to use DueDate < September 1st, instead of June 1st.",
  "source": [{{"tab": "OrderAgreements", "col": "DueDate", "row": "all"}}],
  "target": [{{"tab": "OrderAgreements", "col": "AccountsPayable", "row": "= DueDate < September 1st"}}]
}}
```

## 4. Update Header Row
_Conversation History_
User: Can you please change all headers with BB_ to Blackboard_ please?
Agent: There are multiple columns that start with BB_, including BB_CourseID, BB_CourseTitle, and BB_CourseDescription. Would you like me to change them all?
User: Yes, please.

_Previous State_
blackboard_courses - [BB_CourseID, BB_CourseTitle, BB_CourseDescription]

_Output_
```json
{{
  "thought": "The user wants to rename the rows in the blackboard_courses table to have 'Blackboard_' prefix instead of 'BB_'.
  "source": [
    {{"tab": "blackboard_courses", "col": "BB_CourseID", "row": "header"}},
    {{"tab": "blackboard_courses", "col": "BB_CourseTitle", "row": "header"}},
    {{"tab": "blackboard_courses", "col": "BB_CourseDescription", "row": "header"}}
  ],
  "target": [
    {{"tab": "blackboard_courses", "col": "Blackboard_CourseID", "row": "header"}},
    {{"tab": "blackboard_courses", "col": "Blackboard_CourseTitle", "row": "header"}},
    {{"tab": "blackboard_courses", "col": "Blackboard_CourseDescription", "row": "header"}}
  ]
}}
```

## 5. Update Cells Referencing Itself
_Conversation History_
User: Are there any outliers in the price column?
Agent: There are is $53800 for Emma Europe - Queen Size that is significantly more expensive than the other prices.
User: Oh that's not in dollars! It should be divided by 100.

_Previous State_
AmplitudeAnalytics - [amp_id, product_name, product_type, bed_size, price]

_Output_
```json
{{
  "thought": "The price of 'Brooklinen - Queen Size' should be divided by 100.",
  "source": [{{"tab": "AmplitudeAnalytics", "col": "product_name", "row": "Brooklinen - Queen Size"}}],
  "target": [{{"tab": "AmplitudeAnalytics", "col": "price", "row": "= price / 100"}}]
}}
```

## 6. Update Cells Referencing Other Rows
_Conversation History_
User: What is the return on ad spend for the clicks in September?
Agent: The average Cost per Click for campaigns in September was $0.39. Is that what you wanted to know?
User: OK, please rename the 'Page View Conversions' column to 'Click Through Rate' for all three tables now.

_Previous State_
FB_Ads_Report_Sept - [Date, Total Clicks, Total Ad Spend]
FB_Ads_Report_Oct - [Date, Total Clicks, Total Ad Spend]
FB_Ads_Report_Nov - [Date, Total Clicks, Total Ad Spend]

_Output_
```json
{{
  "thought": "The column 'Page View Conversions' should be renamed to 'Click Through Rate' in all FB Ads Report tables.",
  "source": [
    {{"tab": "FB_Ads_Report_Sept", "col": "Page View Conversions", "row": "header"}},
    {{"tab": "FB_Ads_Report_Oct", "col": "Page View Conversions", "row": "header"}},
    {{"tab": "FB_Ads_Report_Nov", "col": "Page View Conversions", "row": "header"}}
  ],
  "target": [
    {{"tab": "FB_Ads_Report_Sept", "col": "Click Through Rate", "row": "header"}},
    {{"tab": "FB_Ads_Report_Oct", "col": "Click Through Rate", "row": "header"}},
    {{"tab": "FB_Ads_Report_Nov", "col": "Click Through Rate", "row": "header"}}
  ]
}}
```

## 7. Update Cells with Specific Term
_Conversation History_
User: What is the lowest spend that we have for any ad group?
Agent: The lowest spend ad group was New Balance Men's Arrow (1120972) with a spend of -$52.99
User: Oh that's a typo! It should just be $52.99

_Previous State_
campaignAnalytics - [ad_group_id, ad_group, channel_provider, spend]
emailMetrics - [email_id, email_name, email_subject, email_open_rate, email_click_rate]

_Output_
```json
{{
  "thought": "The spend of ad group with ID 1120972 should be updated to $52.99.",
  "source": [
    {{"tab": "campaignAnalytics", "col": "ad_group_id", "row": "1120972"}},
    {{"tab": "campaignAnalytics", "col": "ad_group", "row": "New Balance Men's Arrow"}}
  ],
  "target": [
    {{"tab": "campaignAnalytics", "col": "spend", "row": "52.99"}}
  ]
}}
```
---
## Current Scenario
Follow the same format above to determine the source of changes and the target outcome. There should be no text before nor any explanations after the JSON output.
As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Previous State_
{prior_state}

_Output_
"""

validate_flow_prompt = """According to the conversation history, your task is to validate the data within a column as belonging to a predefined set or within a specific range.
Of course, this requires first knowing which table and column the user is referring to.
Given the valid tables and columns, start by constructing a concise thought about whether there is sufficient information to proceed.
Then, decide on the appropriate course of action by choosing from one of three options:
  1. 'clear' - the table and column are clear based on the conversation or inferring from context
  2. 'peek' - either the table or column is unclear, but peeking at the first few rows can help clarify the situation
  3. 'unsure' - the user doesn't actually seem to want to validate any data, or the table and column are completely ambiguous

After choosing the action, output the appropriate table and columns to consider:
  1. If 'clear', output the table and column name as a single dict in the list
  2. If 'peek', output a list of table and candidate columns to consider
  3. If 'unsure', output an empty list as the target

Your entire response should be in well-formatted JSON inclduing keys for thought (string), action (string), and target (list of dicts). There should be no further explanations after the JSON output.

For example,
---
Suppose the valid tables and columns are:
* Tables: CustomersApril, Subscriptions, BoxDetails
* Columns: CustomerID, FirstName, LastName, Email, Phone, Address, City, State, ZipCode, DateJoined, PreferenceProfile, Referred in CustomersApril;
SubscriptionID, CustomerID, PlanType, StartDate, EndDate, RenewalDate, Status, BillingFrequency, Price, PaymentMethod in Subscriptions;
BoxID, OrderID, BoxTheme, ShipDate, TotalItems, BoxWeight, SpecialNote, TotalAmount, isDelivered, SurveyResponseID in BoxDetails

_Conversation History_
User: We need to clean up the different plan types.
Agent: Sure, I can validate the data, remove duplicates, or standardize the format. What would you like?
User: We should make sure that they're either free, basic, pro or enterprise.

_Output_
```json
{{
  "thought": "The user wants to validate the plan types, which is very likely in the Subscriptions table under PlanType.",
  "action": "clear",
  "target": [ {{"tab": "Subscriptions", "col": "PlanType"}} ]
}}
```

_Conversation History_
User: Let' just ignore those for now
Agent: Ok, I'll focus on the other columns. What would you like to do next?
User: Can you make sure that weight of all boxes are between 1 and 10 lbs?

_Output_
```json
{{
  "thought": "The weight of boxes is clearly referencing the BoxWeight column.",
  "action": "clear",
  "target": [ {{"tab": "BoxDetails", "col": "BoxWeight"}} ]
}}
```

---
For another scenario, consider the following:
* Tables: PurchaseInfo, Campaigns, Inventory, Promotions
* Columns: CustomerID, FirstName, LastName, Email, PhoneNumber, JoinTime, PurchaseHistory, LoyaltyPoints, IsPaid, PreferredBrand in PurchaseInfo;
CampaignID, CampaignName, LaunchPeriod, TargetAudience, Channel, BudgetStatus, ResponseRate, CreativeAsset, LastPageVisited in Campaigns;
ItemID, BrandName, Category, Price, StockQuantity, ArrivalWindow, Supplier, AccountsPayable, ReorderLevel in Inventory;
PromoID, PromoName, RegistrationDeadline, DiscountPercentage, ApplicableCity, ApplicableProducts, PromoCode, RedemptionCount in Promotions

_Conversation History_
User: Let's take a look at where we run promotions.
Agent: Sure, what would you like to know?
User: We only ship to cities in California at the moment, so we need to validate the city names.

_Output_
```json
{{
  "thought": "Since we have already narrowed down to the Promotions table, the city names clearly matches the ApplicableCity column.",
  "action": "clear",
  "target": [ {{"tab": "Promotions", "col": "ApplicableCity"}} ]
}}
```

_Conversation History_
User: There should only be two valid options.
Agent: I'm sorry, could you clarify which options you're referring to?
User: Can we make sure the payment status is either paid or unpaid.

_Output_
```json
{{
  "thought": "There are multiple columns that could refer to payment status, taking a peek at the values will give a better idea which column is being referenced.",
  "action": "peek",
  "target": [
    {{"tab": "PurchaseInfo", "col": "IsPaid"}},
    {{"tab": "Campaigns", "col": "BudgetStatus"}},
    {{"tab": "Inventory", "col": "AccountsPayable"}}
  ]
}}
```

_Conversation History_
User: That doesn't look quite right.
Agent: Sorry about that. Is there something specific you'd like to change?
User: Can we verify that all the pages in the checkout flow are correct?

_Output_
```json
{{
  "thought": "While the user didn't specify a column, the only option related to pages is LastPageVisited in the Campaigns table.",
  "action": "clear",
  "target": [ {{"tab": "Campaigns", "col": "LastPageVisited"}} ]
}}
```
---
Now it's your turn to tackle the current case. Remember to generate a thought first and then output the appropriate action and target. For reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

format_flow_prompt = """According to the conversation history, we want to standardize the data within a column to conform to a specific format.
Of course, this requires first knowing which table and column the user is referring to.
Given the valid tables and columns, start by constructing a concise thought about whether there is sufficient information to proceed.
Then, decide on the appropriate course of action by choosing from one of three options:
  1. 'clear' - the table and column are clear based on the conversation or inferring from context
  2. 'peek' - either the table or column is unclear, but peeking at the first few rows can help clarify the situation
  3. 'unsure' - the user doesn't actually seem to want to format any data, or the table and column are completely ambiguous

After choosing the action, output the appropriate table and columns to consider:
  1. If 'clear', output the table and column name as a single dict in the list
  2. If 'peek', output a list of table and candidate columns to consider
  3. If 'unsure', output an empty list as the target

Your entire response should be in well-formatted JSON including keys for thought (string), action (string), and target (list of dicts). There should be no further explanations after the JSON output.

For example,
---
Suppose the valid tables and columns are:
* Tables: CustomerContact, CustomerOrders, MarketingOffers
* Columns: CustomerID, CustomerName, FavCuisineType, ShippingAddress, ContactNumber, IsActive, Twitter, Instagram, Yelp in CustomerContact;
OrderID, CustomerID, RestaurantID, OrderDate, TotalAmount, DeliveryAddress, OrderStatus, EstDeliveryTime, SpecialInstructions in CustomerOrders;
OfferID, OfferTitle, OfferDescription, OrderKey, UserName, StartDate, EndDate, DiscountAmount, ApplicableRestaurants, RedemptionCode in MarketingOffers

_Conversation History_
User: Can you please sort the orders by date?
Agent: Sorry, there seems to be an issue with the dates in OrderDate that prevents me from sorting.
User: Oh, I see. Can you format all the dates as MM/DD/YYYY?

_Output_
```json
{{
  "thought": "The user wants to format the dates within the orders table as MM/DD/YYYY. The column is clearly OrderDate.",
  "action": "clear",
  "target": [ {{"tab": "CustomerOrders", "col": "OrderDate"}} ]
}}
```

_Conversation History_
User: Do you see how the names are all over the place?
Agent: Sure, do you want to clean them up somehow?
User: The names should be in 'Last, First' format.

_Output_
```json
{{
  "thought": "There are multiple columns that could refer to names, so it is unclear which one the user is referring to.",
  "action": "peek",
  "target": [
    {{"tab": "CustomerContact", "col": "CustomerName"}},
    {{"tab": "MarketingOffers", "col": "UserName"}}
  ]
}}
```

---
For another scenario, consider the following:
* Tables: Customers, Campaigns, Inventory, Promotions
* Columns: CustomerID, FirstName, LastName, Email, PhoneNumber, JoinTime, PurchaseHistory, LoyaltyPoints, Address, PreferredBrand in Customers;
CampaignID, CampaignName, LaunchPeriod, TargetAudience, Channel, Budget, ResponseRate, CreativeAsset in Campaigns;
ItemID, BrandName, Category, Price, StockQuantity, ArrivalWindow, Supplier, Location, ReorderLevel in Inventory;
PromoID, PromoName, RegistrationDeadline, DiscountPercentage, ApplicableProducts, PromoCode, RedemptionCount in Promotions

_Conversation History_
User: Can you make it bigger as well?
Agent: The title has been updated to a larger font size. Anything else you'd like to change?
User: I think we need to format the x-axis to be every 5 years.

_Output_
```json
{{
  "thought": "The user seems to be formatting a chart, rather than data in a table. Therefore, it is unclear which table or column they are referring to.",
  "action": "unsure",
  "target": []
}}
```

_Conversation History_
User: Ok, let's move onto cleaning the dates next.
Agent: Sure, what would you like to do with the dates?
User: Can we make them into the yyyy-mm-dd format?

_Output_
```json
{{
  "thought": "The user wants to format the date, but it unclear which column is correct since multiple columns refer to dates.",
  "action": "peek",
  "target": [
    {{"tab": "Customers", "col": "JoinTime"}},
    {{"tab": "Campaigns", "col": "LaunchPeriod"}},
    {{"tab": "Inventory", "col": "ArrivalWindow"}},
    {{"tab": "Promotions", "col": "RegistrationDeadline"}}
  ]
}}
```

_Conversation History_
User: We're thinking about calling up some customers to get feedback on the new release.
Agent: Good idea, it's always good to get direct feedback.
User: We need to standardize the numbers first though so we can automate the process.

_Output_
```json
{{
  "thought": "While the user didn't specify a column, there is only one column related to phone numbers so the answer is clear.",
  "action": "clear",
  "target": [ {{"tab": "Customers", "col": "PhoneNumber"}} ]
}}
```
---
Now it's your turn to tackle the current case. Remember to generate a thought first and then output the appropriate action and target. For reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

pattern_fill_prompt = """Given the valid tables and columns along with the conversation history, your task is to determine the relevant table and column that needs to be filled.
Start by constructing a concise thought about the pattern observed in the data or being described by the user.
Then, decide on the target table and column that needs to be filled. If it is unclear what should be filled, output 'unsure' as the target column.
If this column is not part of the available columns, then generate an appropriate new name that matches the style (ie. capitalization) of the existing columns.
Finally, find supporting columns that could be referenced to fill in the target column. When in doubt, prefer to include more supporting columns rather than less.

Your entire response should be well-formatted JSON including keys for thought (string), table (string), target (string), and support (list). There should be no further explanations after the JSON output.

For example,
---
Suppose the valid tables and columns are:
* Tables: Stripe_Updated, JuneOrders, Product Ratings
PurchaseID, Username, ProductID, PurchaseTime, Quantity, DiscountRate, Taxes, UnitPrice, PaymentFees, ShippingAddress in Stripe_Updated;
OrderID, CustomerID, OrderDate, TotalAmount, Address, City, State, Zip, OrderStatus, Carrier, DeliveryDate, DeliveryStatus in JuneOrders;
ProductID, Rating, Review, Date, Username, ProductName, Price, Category, Description in Product Ratings

_Conversation History_
User: Let's make a new column for total payments as well.
Agent: Sure, how should we populate this column?
User: I want it to hold the total amount of all the payments made so far.

_Output_
```json
{{
  "thought": "Calculating the total payments made likely requires summing the Payment Fees from Stripe. PurchaseTime may also be useful for providing context.",
  "table": "Stripe_Updated",
  "target": "TotalPayments",
  "support": ["PaymentFees", "PurchaseTime"]
}}
```

Now, suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach
* Columns: CourseID, CourseTitle, InstructorID, CourseDescription, StudentName, PresentationDate, Duration, CourseFormat, EnrollmentCount in BB_courses;
EnrollmentID, CourseID, StudentID, EnrollmentDate, CompletionStatus, Feedback, CertificateLink, PaymentStatus, ReferralSource in BB_enrollments;
TestimonialID, StudentID, CourseID, TestimonialText, DateProvided, Rating, Featured, ApprovalStatus, PhotoLink in Testimonials;
OutreachID, CampaignName, TargetAudience, Platform, ResponseRate, Collaborators in CanvasOutreach

_Conversation History_
User: Yup, let's get rid of them
Agent: No problem, those values have been removed
User: Now, can you fill in the missing presentation dates?

_Output_
```json
{{
  "thought": "presentation dates are found in BB_courses. Student names and Duration may be useful for context.",
  "table": "BB_courses",
  "target": "PresentationDate",
  "support": ["StudentName", "Duration"]
}}
```

For this scenario, suppose the valid tables and columns are:
* Tables: HubspotCRM; TransactionHistory; InteractionLogs
* Columns: customer_id, signup_date, cust_name, email, region, tracking_id, channel, acct_status in HubspotCRM;
interaction_id, customer_id, agent_id, interact_date, interact_type, interact_text, interact_duration, issue_resolved in the InteractionLogs

_Conversation History_
User: So what I want is to fill the dialogue column with the last three turns from the chat logs.
Agent: To clarify, I will call the new column 'recent_dialogue'. Does that sound good?
User: Yes, that's perfect.

_Output_
```json
{{
  "thought": "insert the last three utterances of the conversation from the InteractionLogs into the dialogue column",
  "table": "InteractionLogs",
  "target": "recent_dialogue",
  "support": ["customer_id", "agent_id", "interact_text", "interact_date"]
}}
```

In this next example, the valid tables and columns are:
* Tables: GoogleAds_Q3, SalesRecord_Shopify_0812, Product_Details
* Columns: adID, adSpend, clickCount, campaignInitDate, campaignTermDate, adBounceRate, audienceFocus, adContentCode in GoogleAds_Q3;
orderRef, prodSKU, saleDate, acquisitionCost, buyerID, googleAdRef, revenueGenerated, unitsMoved, fulfillmentStatus, customerNotes in SalesRecord_Shopify_0812;
SKU, itemName, itemCategory, retailPrice, totalCost, stockLevel in Product_Details

_Conversation History_
User: Each row should subtract off the acquisition cost and then add the LTV of each user.
Agent: In order to calculate this, I would need to know the LTV of each user. I can try to calculate it using the revenue generated and sale date, should I proceed?
User: Just use $45 for LTV.

_Output_
```json
{{
  "thought": "calculate the net profit per user by considering the acquisition cost without calculating LTV",
  "table": "SalesRecord_Shopify_0812",
  "target": "netProfit",
  "support": ["acquisitionCost"]
}}
```

For our last example, the valid tables and columns are:
* Tables: FAANG Stocks, CryptoPrices, StockOptions
* Columns: stock_id, stock_name, close_date, close_price, open_price, high_price, low_price, volume in FAANG Stocks;
crypto_id, crypto_name, close_date, close_price, open_price, high_price, low_price, volume in CryptoPrices;
option_id, option_name, stock_id, strike_price, expiration_date, option_type, option_price, volume in StockOptions

_Conversation History_
User: I want to smooth out the closing prices of the stock
Agent: Sure, should I calculate the average of the last few days in a new column?
User: we need the trailing average on a weekly basis

_Output_
```json
{{
  "thought": "calculate the weekly average of the closing prices",
  "table": "FAANG Stocks",
  "target": "weekly_average",
  "support": ["close_date", "close_price"]
}}
```
---
Now it's your turn to tackle the current case. Remember to generate a thought first and then output the appropriate table, target, and support columns. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

remove_duplicates_prompt = """Given the conversation history and valid columns, your task is to determine which tables and columns are most appropriate for removing duplicates.
Start by generating a concise thought concerning the relevant columns and tables mentioned in the final turn of dialogue.
Then, output a list of table and column dicts. If either field is unclear, then label as 'unsure'.
Note that ID related columns are often system-generated and should NOT be considered for duplicate removal unless explicitly requested by the user.
Rather you should focus on who, what, when, where and how to identify unique records.
Your entire response should be a well-formatted JSON with keys for thought (string) and result (list). There should be no further explanations after the JSON output.

For example,
---
# Scenario 1
CustomerID, CustomerName, FavCuisineType, ShippingAddress, ContactNumber, IsActive, Twitter, Instagram, Yelp in CustomerContact;
OrderID, CustomerID, RestaurantID, OrderDate, TotalAmount, DeliveryAddress, OrderStatus, EstDeliveryTime, SpecialInstructions in Orders;
OfferID, OfferTitle, OfferDescription, OrderKey, StartDate, EndDate, DiscountAmount, ApplicableRestaurants, RedemptionCode in MarketingOffers

_Conversation History_
User: Change all the Unknown into N/A
Agent: Sure I've changed all the Unknown into N/A. See table for details.
User: Now can we focus on removing the duplicate customers?

_Output_
```json
{{
  "thought": "We can determine unique customers by their who they are <CustomerName>, where they live <ShippingAddress>, and how to contact them <ContactNumber>.",
  "result": [
    {{"tab": "CustomerContact", "col": "CustomerName"}},
    {{"tab": "CustomerContact", "col": "ContactNumber"}}
  ]
}}
```

# Scenario 2
PurchaseID, Username, ProductID, PurchaseTime, Quantity, DiscountRate, Taxes, UnitPrice, PaymentMethod, ShippingAddress in Stripe_Updated;
OrderID, CustomerID, OrderDate, TotalAmount, Address, City, State, Zip, OrderStatus, Carrier, DeliveryDate, DeliveryStatus in JuneOrders;
ProductID, Rating, Review, Date, Username, ProductName, Price, Category, Description in Product Ratings

_Conversation History_
User: See how we have different status for each order?
Agent: Yes, the orders table has a DeliveryStatus column. What would you like to know?
User: I just want to see the unique orders.

_Output_
```json
{{
  "thought": "An order is likely unique if it has a distinct combination of who <CustomerID>, when <OrderDate>, where <Zip>, and how much <TotalAmount>",
  "result": [
    {{"tab": "JuneOrders", "col": "CustomerID"}},
    {{"tab": "JuneOrders", "col": "OrderDate"}},
    {{"tab": "JuneOrders", "col": "TotalAmount"}}
  ]
}}
```

_Conversation History_
User: Can you get rid of the duplicate product ids from the stripe table?

_Output_
```json
{{
  "thought": "Normally, I would not include an ID column, but this time the user has explicitly asked for it, so <ProductID> is a good choice.",
  "result": [
    {{"tab": "Stripe_Updated", "col": "ProductID"}}
  ]
}}
```

# Scenario 3
user_id, event_name, event_timestamp, session_id, device_type, os_version, browser, page_url, time_spent_on_page, cart_value, items_in_cart, referral_source in MixpanelActivity;
transaction_id, user_id, purchase_date, total_amount, payment_method, currency, shipping_address, billing_address, coupon_code_used, items_purchased, tax_amount, shipping_cost, order_status in transactions_july_august;
feedback_id, user_id, submission_date, overall_rating, ease_of_use, product_quality_rating, shipping_experience, score, comments, survey_completion_time, feedback_category, resolved_status, agent_assigned in Intercom

_Conversation History_
User: Please resolve the remaining conflicts for me.
Agent: I have successfully merged the July and August transaction data. See table for details.
User: It looks like the transaction data has repeated entries now.

_Output_
```json
{{
  "thought": "A unique transaction can be defined as combination of who <user_id> bought what <items_purchased>, when that occured <purchase_date> and where it went <shipping_address>.",
  "result": [
    {{"tab": "transactions_july_august", "col": "user_id"}},
    {{"tab": "transactions_july_august", "col": "items_purchased"}},
    {{"tab": "transactions_july_august", "col": "purchase_date"}},
    {{"tab": "transactions_july_august", "col": "shipping_address"}}
  ]
}}
```

## Scenario 4
gAd_ID, spend(Dollars), clickCount, campaignInitDate, campaignTermDate, audienceFocus, adContentCode in GoogleAds_Q3;
orderRef, prodSKU, saleDate, acquisitionCost, buyerID, gAdRef, revenueGenerated, unitsMoved, fulfillmentStatus, customerNotes in SalesRecord_Shopify_0812;
productID, SKU, itemName, itemCategory, retailPrice, totalCost, stockLevel in Product_Details

_Conversation History_
User: What is the CPC for each product?
Agent: Sure, I will need to verify the cost and clicks first. Does this look right?
User: Yea, clickCount looks good.
Agent: The average CPC for each product is $0.25. See table for breakdown by product.
User: It looks like many of the products are actually the same.

_Output_
```json
{{
  "thought": "I should avoid using productID. Instead, a unique product can be identified by its name <itemName> and category <itemCategory>.",
  "result": [
    {{"tab": "Product_Details", "col": "itemName"}},
    {{"tab": "Product_Details", "col": "itemCategory"}}
  ]
}}
```
---
Now it's your turn to tackle the current case. Please generate a thought and then output a list of relevant table and column dicts.
Avoid using the primary key as a column except when directly mentioned. There should be no explanations or lessons after the JSON output.

## Real Scenario
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

assign_datatype_prompt = """Given the conversation history, we believe the user is assigning a specific data type to a column.
Your task is to determine this target column and data type. The possible data types are:
  * unique - each value holds a unique meaning, including subtypes such as IDs, pre-defined categories, statuses, or boolean values
  * datetime - the values are related to dates or times, including subtypes such as months, weeks, quarters, or timestamps
  * location - the values are related to geographical locations, including subtypes such as cities, states, countries, or addresses
  * number - the values are numeric and can be used for calculations, including subtypes such a percent, currency, or decimals
  * text - the values are textual, including subtypes such as phone numbers, emails, urls, names or general descriptions

To help with this task, you will also be given the valid tables and columns to consider, along with the current data type of each column.
Start by constructing a concise thought about which data type is being assigned to which column. If the user only refers to the subtype, you will need to infer the parent data type.
Then, choosing only from valid options, decide on the most likely column and data type. If any field is unclear, then output 'unsure' rather than guessing.
Your entire response should be in well-formatted JSON including keys for thought, table, column, and datatype, with no further explanations after the JSON output.

For example,
---
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach
* Columns: CourseID (unique), CourseTitle (text), InstructorID (unique), CourseDescription (text), StudentName (text), PresentationDate (datetime), EnrollmentCount (number) in BB_courses;
EnrollmentID (unique), StudentID (unique), EnrollmentDate (datetime), CompletionStatus (unique), Feedback (text), CertificateLink (text), PaymentStatus (unique), ReferralSource (text) in BB_enrollments;
TestimonialID (unique), StudentID (unique), CourseID (unique), TestimonialText (text), TimeProvided (datetime), Rating (number), Featured (text), ApprovalStatus (unique), PhotoLink (text) in Testimonials;
OutreachID (unique), CampaignName (text), TargetAudience (text), Platform (text), ResponseRate (text), Collaborators (unique) in CanvasOutreach

_Conversation History_
User: Can we take a look at the testimonials?
Agent: Sure, what would you like to know?
User: TimeProvided looks off, it should be a date and not a time.

_Output_
```json
{{
  "thought": "The user is assigning a date subtype to TimeProvided, which belongs to the datetime data type",
  "table": "Testimonials",
  "column": "TimeProvided",
  "datatype": "datetime"
}}
```

_Conversation History_
User: So what the average score for the responses then?
Agent: I'm sorry, I have encountered an error when trying to calculate the average response rate. Could you please advise on how to proceed?
User: Response rate should be score from 1 to 100, not text.

_Output_
```json
{{
  "thought": "Response rate is a whole number rather than text, which is the number data type",
  "table": "CanvasOutreach",
  "column": "ResponseRate",
  "datatype": "number"
}}
```

In our next scenario, consider the following:
* Tables: Campaigns, Inventory, Promotions
* Columns: Chb_id (unique), CampaignName (text), LaunchPeriod (datetime), TargetAudience (text), Channel (text), BudgetStatus (unique), ResponseRate (number), CreativeAsset (text) in Campaigns;
ItemID (unique), BrandName (text), Category (unique), Price (number), StockQuantity (number), ArrivalWindow (datetime), Supplier (text), AccountsPayable (number), ReorderLevel (number) in Inventory;
PromoID (unique), PromoName (text), RegistrationDeadline (datetime), DiscountPercentage (number), ApplicableCode (number), ApplicableCity (location), PromoCode (text), RedemptionCount (number) in Promotions

_Conversation History_
User: Can we get rid of the chb column, where did that even come from?
Agent: Apologies, but every table must have at least one column to serve as a primary key, so I cannot remove it at this time.
User: The campaign name is already a unique identifier, can we use that instead?

_Output_
```json
{{
  "thought": "The user is assigning a unique data type to the CampaignName column.",
  "table": "Campaigns",
  "column": "CampaignName",
  "datatype": "unique"
}}
```

_Conversation History_
User: Let's take a look at where we run promotions.
Agent: Sure, what would you like to know?
User: applicable code is actually a zip code not a regular number

_Output_
```json
{{
  "thought": "The user is assigning a zipcode subtype to ApplicableCode, which belongs to the location data type",
  "table": "Promotions",
  "column": "ApplicableCode",
  "datatype": "location"
}}
```

As a last example, suppose the valid tables and columns are:
* Tables: mq_leads, product_launches, subscriptions, user_activity
* Columns: lead_id (unique), first_name (text), last_name (text), email (text), organization (text), lead_source (text), contact_date (datetime), lead_number (number), notes (text), follow_up_date (datetime) in mq_leads;
launch_id (unique), is_secure (unique), provenance (text), version (unique), features (text), documentation_link (text) in data_sources;
subscription_id (unique), user_id (unique), plan_name (text), subcribe_timestamp (datetime), billing_cycle (datetime), payment_status (unique), renewal_notice (datetime) in subscriptions;
activity_id (unique), user_id (unique), activity_type (unique), timestamp (datetime), duration (datetime), data_source (text), outcome (unique), error_log (text) in user_activity

_Conversation History_
User: What does the provenance column mean?
Agent: The provenance column is a text field that describes the origin of the data.
User: Can we change the data type to something more descriptive?

_Output_
```json
{{
  "thought": "Based on context, we can tell the user wants to change the data type of the provenance column, but the target data type is unclear.",
  "table": "data_sources",
  "column": "provenance",
  "datatype": "unsure"
}}
```

_Conversation History_
User: How many unique lead numbers do we have?
Agent: There are 135 unique lead numbers.
User: Actually, that column holds phone numbers, so the data type needs to be adjusted.

_Output_
```json
{{
  "thought": "Phone numbers are not actually numbers, since they cannot be used for calculations. Instead, they belong to the text data type.",
  "table": "mq_leads",
  "column": "lead_number",
  "datatype": "text"
}}
```
---
Now it's your turn! Please think critically about the data type and column being assigned in the conversation. For reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

problems_prompt = """Given the conversation history, your task is to determine the row ids the user is referencing to address the problems in the data.
For our context, the term 'problems' refers to the rows belonging to a data type of subtype that are different from the majority of the column.
To help in this task, you will be given the general status of the column in the form of `Most are <sub_type>, but X are <sub_type> type`.
This is followed by a list of potential problems written as `Row row_id) <sub_type> - <value>`, which might be truncated for brevity.
The possible data types are:
  * unique - each value holds a unique meaning, with subtypes such as IDs, pre-defined categories, statuses, or boolean values
  * datetime - the values are related to dates or times, including subtypes such as months, weeks, quarters, or timestamps
  * location - the values are related to geographical locations, including subtypes such as cities, states, countries, or addresses
  * number - the values are numeric and can be used for calculations, including subtypes such a percent, currency, or decimals
  * text - the values are textual, including subtypes such as phone numbers, emails, names or general descriptions

Using this information, please think deeply about how the user is addressing the problems. The possible methods include:
  * convert - changing the rows to a different data type or sub type without affecting the content
  * update - modifying the content through calculating new values or changing the underlying text
  * delete - removing the rows that are causing the problems
  * beyond - the user is moving on to a different topic beyond the problems displayed
  * unsure - it is unclear how to proceed, such as when the user makes a non-committal response

If we are intepreting values a different type, this is a 'convert' method. If we are changing the underlying value, this is an 'update' method.
Afterwards, please output which rows are being referenced in the final conversation turn based on the row_id.
If the user is referencing all issues at once, use a negative value (like -1) to indicate this. If the user is unclear or going beyond the limits, please output an empty list.
Your entire response should be in well-formatted JSON including keys for thought (string), method (string) and rows (list of integers), with no further explanations after the JSON output.

For example,
---
User: Those are actually dollar amounts.
Agent: Sure, I have changed 21.33 to a currency type. What should I do with the rest?
User: Oh, yea do the same for the rest
Status: Most are currency, but 4 are decimal type
Row 23) decimal - 19.53
Row 22) decimal - 14.36
Row 37) decimal - 15.01
Row 39) decimal - 16.77

_Output_
```json
{{
  "thought": "The discussion involves the currency subtype, so this is a convert method",
  "method": "convert",
  "rows": [-1]
}}
```

Agent: Most of the entries in the Promotions column are the name data type, but I found some general type.
User: This column is a mess, let's just delete the whole thing.
Status: Most are name, but 5 are general type
Row 196) general - JamesHemsley
Row 203) general - NONAME
Row 125) general - AshleyGreen
Row 144) general - Bobbynight
Row 158) general - skip

_Output_
```json
{{
  "thought": "The user is referencing the entire column which goes beyond the problems mentioned",
  "method": "beyond",
  "rows": []
}}
```

User: Can you show me the problems?
Agent: Yes, I found 3 problems in the DropoffTime column. We can update the values, remove the rows, or just ignore the problem.
User: ok, timestamp sounds correct
Status: Most are timestamp, but 3 are date type
Row 122) date - 07-29-2021
Row 123) date - 09-02-2021
Row 124) date - 08-13-2021

_Output_
```json
{{
  "thought": "The user is requesting to convert all the rows to timestamp type",
  "method": "convert",
  "rows": [-1]
}}
```

Agent: The AttendedEvent column is mostly boolean data type, but I found some general text. Here are some samples of both to help compare.
User: the maybes can just be removed
Status: Most are boolean, but 17 are general type
Row 3) general - maybe
Row 17) general - not sure
Row 32) general - maybe
Row 23) general - maybe
Row 35) general - not sure
Row 36) general - maybe
Row 37) general - maybe
Row 41) general - later
[9 more rows ...]

_Output_
```json
{{
  "thought": "The user wants to delete the rows where the text is 'maybe'",
  "method": "delete",
  "rows": [3, 32, 23, 36, 37]
}}
```

User: Please show me.
Agent: The zip_code column is mostly zip codes, but I found some whole numbers. We can update the values, remove the rows, or just ignore the problem.
User: We can change the second one to 01108
Status: Most are zip code, but 5 are whole type
Row 63) whole - 0
Row 68) whole - 1108
Row 73) whole - 111111
Row 74) whole - 0
Row 125) whole - 0

_Output_
```json
{{
  "thought": "The user is changing the underlying value of the second row",
  "method": "update",
  "rows": [68]
}}
```

Agent: Most of the entries in the utm_source column are the url type, but I found some general text. Here are some samples of both to help compare.
User: uhh, these look like urls to me
Status: Most are url, but 43 are general type
Row 61) general - shopify.com
Row 62) general - soleda.ai
Row 63) general - orbitalinsights.ai
Row 64) general - microsoft.azurelake.com
Row 65) general - aws.amazon.com
Row 66) general - soleda.ai
Row 67) general - redhat.com
Row 68) general - cloud.google.com
[35 more rows ...]

_Output_
```json
{{
  "thought": "These rows should be converted to url type to match the user's expectation",
  "method": "convert",
  "rows": [-1]
}}
```

Agent: Done. What would you like to do with the remaining issues?
User: The Thur and Fiday ones should be Thursday and Friday
Status: Most are week, but 4 are general type
251) general - Thur
162) general - Fiday
163) general - weekend
184) general - Thur

_Output_
```json
{{
  "thought": "Thur and Fiday are found in rows 251, 162, and 184",
  "method": "update",
  "rows": [251, 162, 184]
}}
```

Agent: I found 3 problems in the SendToAddress column. We can update the values, remove the rows, or just ignore them.
User: Yes, these are hard to decipher
Status: Most are email, but 3 are general type
Row 98) general - fatumremix@mirrormachines
Row 102) general - sensory_depravation@fardenstate
Row 137) general - qrion@waterfalls

_Output_
```json
{{
  "thought": "The user is unsure how to address the problems",
  "method": "unsure",
  "rows": []
}}
```

Agent: I found 4 mixed data types and 2 unsupported values in the ArrivalDate column. How would you like to proceed?
User: so the first four just need to insert slashes to become dates right
Status: Most are date, but 4 are whole type and 2 are decimal type
Row 584) whole - 08242003
Row 585) whole - 08252003
Row 587) whole - 08242003
Row 588) whole - 08242003
Row 892) unsupported - 8.24.03
Row 899) unsupported - 8.25.03

_Output_
```json
{{
  "thought": "The underlying values don't need to change, we just need to re-interpret them as dates",
  "method": "convert"
  "rows": [584, 585, 587, 588]
}}
```

Agent: The contact_info column is mostly phone numbers, but I found some general text. Here are some samples of both to help compare.
User: These are all still phone numbers, can you fix them?
Status: Most are phone, but 8 are general type
Row 1) general - +1 (650)774 -8901
Row 2) general - + 1 (650)595-2121
Row 3) general - +1 (650)494-1287
Row 4) general - +1 (880)236-1554
Row 5) general - 1 -************
Row 6) general - +1 (556)256-5686
Row 7) general - +1 (650)903-0403
[1 more row ...]

_Output_
```json
{{
  "thought": "We will need to adjust the symbols and spaces to make them phone numbers",
  "method": "update",
  "rows": [-1]
}}
```

Agent: Most of the entries in the Promotions column are the city data type, but I found some state type. Here are some samples of both to help compare.
User: We can just drop the state from the city
Status: Most are city, but 3 are state type
Row 901) state - Morningside, CA
Row 921) state - Evergreen, CO
Row 935) state - Westlake, CA

_Output_
```json
{{
  "thought": "Dropping the state portion requires updating the content",
  "method": "update",
  "rows": [-1]
}}
```

Agent: Most of the entries in the Promotions column are the address data type, but I found some general type. Here are some samples of both to help compare.
User: umm, can we just delete these ones?
Status: Most are address, but 3 are general type
Row 236) general - hollywood blvd
Row 725) general - circle drive
Row 802) general - west 4th avenue

_Output_
```json
{{
  "thought": "We can just delete all these problematic rows",
  "method": "delete"
  "rows": [-1]
}}
```

Agent: I found 4 problems in the SentTime column. We can update the values, remove the rows, or just ignore the problem.
User: The ones with numbers should be converted to times.
Status: Most are time, but 4 are whole type
Row 155) whole - 530
Row 125) whole - 630
Row 136) whole - missing
Row 204) whole - 615

_Output_
```json
{{
  "thought": "The rows with numbers are 155, 125, and 204, 'time' is a data type so we are converting",
  "method": "convert",
  "rows": [155, 125, 204]
}}
```
---
Now it is your turn, please think carefully, then decide on the appropriate method and rows. Remember to only output JSON with no further text.

{history}
Status: {status}
{row_desc}

_Output_
"""

undo_flow_prompt = """Given the valid tables and columns along with the conversation history, your task is to undo the last action taken on the table.
Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.

Your entire response should be in well-formatted JSON including keys for thought (string) and result (list), with no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Placeholder Scenario
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach

## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

persist_preference_prompt = """Given the valid tables and columns along with the conversation history, your task is to save or update a user preference.
Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.

Your entire response should be in well-formatted JSON including keys for thought (string) and result (list), with no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Placeholder Scenario
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach

## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

impute_flow_prompt = """Given the conversation history and available tables, your task is to determine which source and target columns are relevant for imputation.
The source columns are the ones that will be used to fill in the target column, while the target column is the one that needs to be filled in.

When deciding on source columns, keep in mind:
  * If it is unclear what the source columns are, then output up to eight (8) columns that may be useful in determining the target.
  * At this early stage, prefer to include more columns rather than less when there is uncertainty.
  * You can use the wildcard '*' to represent all columns in the table, but only do so if there are 8 or fewer columns in the table.
  * If the user has explicitly mentioned the columns to use, then include those and no others.

When deciding on the target column, keep in mind:
  * If it is unclear what the target column is, then output an empty list as the target.
  * If there are multiple possible options for the target, then include all of them so we can review them.
  * Occasionally, there may even be multiple target columns, which is why the target is a list.

Please start by thinking out loud about the how we might impute the missing values and which columns are involved.
Your entire response should be in well-formatted JSON including keys for thought (string), source (list), and target (list).
Both the source and target should be a list of dicts with keys for tab (string) and col (string). There should be no further explanations after the JSON output.

For example,
---
_Conversation History_
User: You see how many of the cities are missing?
Agent: Yes, I found 24 rows with missing cities. What would you like to do with those?
User: Is there a way to grab it from the shipping address?

_Valid Tables and Columns_
* Tables: customerContact, customerOrders, marketingOffers
* Columns: CustomerID, CustomerName, FavCuisineType, ShippingAddress, customerCity, customerState, ContactNumber, IsActive in customerContact;
OrderID, CustomerID, RestaurantID, OrderDate, TotalAmount, DeliveryAddress, OrderStatus, EstDeliveryTime, SpecialInstructions in customerOrders;
OfferID, OfferTitle, OfferDescription, OrderKey, StartDate, EndDate, DiscountAmount, ApplicableRestaurants, RedemptionCode in marketingOffers

_Output_
```json
{{
  "thought": "I should impute the missing cities by grabbing it from the shipping address.",
  "source": [ {{"tab": "customerContact", "col": "ShippingAddress"}} ],
  "target": [ {{"tab": "customerContact", "col": "customerCity"}} ]
}}
```

_Conversation History_
User: Sure, please show me
Agent: The ad spend is 0 for three of the campaigns, should I delete those rows?
User: No, please use the average ad spend from the other campaigns to fill in the blanks.

_Valid Tables and Columns_
* Tables: Inventory; Analytics Download
* Columns: product_id, product_name, bed_size, has_pillows, includes_headboard, bed_material, manufacturer, price in Inventory;
campaign_name, ad_group, ad_title, ad_spend, num_impressions, num_views, num_clicks in Analytics Download

_Output_
```json
{{
  "thought": "I should impute the missing ad spend by taking the average from the other, existing campaigns. This means the source and target are the same.",
  "source": [ {{"tab": "Analytics Download", "col": "ad_spend"}} ],
  "target": [ {{"tab": "Analytics Download", "col": "ad_spend"}} ]
}}
```

_Conversation History_
User: How do determine when AccountsPayable is overdue?
Agent: AccountsPayable is overdue if the DueDate is before June 1st.
User: Can you update this to September 1st instead?
Agent: Sure I have updated the AccountsPayable column, how does this look?
User: Actually, many of them are still missing.

_Valid Tables and Columns_
* Tables: PurchaseStock, Promotions
* Columns: BrandName, Category, StockQuantity, ArrivalWindow, Supplier, AccountsPayable, ReorderLevel, DueDate in PurchaseStock;
PromoID, PromoName, RegistrationDeadline, DiscountPercentage, ApplicableCity, ApplicableProducts, PromoCode, RedemptionCount in Promotions


_Output_
```json
{{
  "thought": "It's unclear whether I should target DueDate or AccountsPayable. This also makes it hard to figure out the relevant source columns. Since there are only 8 of them, I will include them all.",
  "source": [ {{"tab": "PurchaseStock", "col": "*"}} ],
  "target": [
    {{"tab": "PurchaseStock", "col": "AccountsPayable"}},
    {{"tab": "PurchaseStock", "col": "DueDate"}}
  ]
}}
```

_Conversation History_
User: Please fill in all the empty approval statuses.

_Valid Tables and Columns_
* Tables: Testimonials, CanvasOutreach
* Columns: TestimonialID, StudentID, CourseID, CourseName, TestimonialText, DateProvided, Rating, Featured, ApprovalStatus, PhotoLink in Testimonials;
CampaignName, TargetAudience, ViewCount, ClickCount, CostPerClick, SignupCount, ResponseRate, Collaborators in CanvasOutreach

_Output_
```json
{{
  "thought": "It's clear we are targeting the ApprovalStatus column in Testimonials, but it's uncertain which ones to use as source. I will pick multiple columns that might provide clues.",
  "source": [
    {{"tab": "Testimonials", "col": "CourseName"}},
    {{"tab": "Testimonials", "col": "TestimonialText"}},
    {{"tab": "Testimonials", "col": "DateProvided"}},
    {{"tab": "Testimonials", "col": "Rating"}},
    {{"tab": "Testimonials", "col": "Featured"}},
    {{"tab": "Testimonials", "col": "ApprovalStatus"}}
  ],
  "target": [ {{"tab": "Testimonials", "col": "ApprovalStatus"}} ]
}}
```

_Conversation History_
User: So the last updated column is blank for many of the rows since we just started using the system.
Agent: Yes, I see there are 1153 rows with missing timestamps.
User: Can you use all the other dates to fill it in? Basically, take the most recent date from the other columns.

_Valid Tables and Columns_
* Tables: OrderAgreements
* Columns: order_id, utm_source, utm_medium, utm_campaign, created_at, order_date, ship_date, last_updated, total_amount, payment_method in Order Agreements

_Output_
```json
{{
  "thought": "I will have to pull from multiple columns in order to fill in the missing updated timestamp. I will use the created_at, order_date, and ship_date columns to determine the most recent date.",
  "source": [
    {{"tab": "OrderAgreements", "col": "created_at"}},
    {{"tab": "OrderAgreements", "col": "order_date"}},
    {{"tab": "OrderAgreements", "col": "ship_date"}}
  ],
  "target": [ {{"tab": "OrderAgreements", "col": "last_updated"}} ]
}}
```

_Conversation History_
User: Can you extract the URLs from the feedback form?
Agent: No problem. Just to clarify, which columns in particular contain the URLs?
User: I'm interested to see if they mentioned any competitor brands in their comments.

_Valid Tables and Columns_
* Tables: Hubspot - FeedbackForm; Salesforce - CustomerInfo
* Columns: feedback_id, submission_date, customer_id, product_rating, service_rating, nps_score, comments, sentiment, feedback_category, follow_up_required, mentioned_links in Hubspot - FeedbackForm;
customer_id, signup_date, customer_first, customer_last, email_address, region, tracking_id, channel, acct_status in Salesforce - CustomerInfo

_Output_
```json
{{
  "thought": "I can extract the competitor URLs from the comments in the Hubspot table using a regular expressions to place into the mentioned_links column.",
  "source": [ {{"tab": "Hubspot - FeedbackForm", "col": "comments"}} ],
  "target": [ {{"tab": "Hubspot - FeedbackForm", "col": "mentioned_links"}} ]
}}
```
---
Now it's your turn to tackle the current case. Choosing only from the valid tables and columns, output the source and target columns for imputation.

_Conversation History_
{history}

_Valid Tables and Columns_
{valid_tab_col}

_Output_
"""