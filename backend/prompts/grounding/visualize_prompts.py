plot_flow_prompt = """Given the conversation history and supporting details, your task is to determine the relevant columns for visualizing the user's request.
Supporting details includes the valid tables and columns, along with the previous dialogue state, written as the table name followed by a list of column names.

Start by constructing a concise thought concerning what information is useful for generating a plot or figure.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the visualization.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.
Your entire response should be in well-formatted JSON including keys for thought (string) and result (list) where each item is a dict. There should be no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Straightforward Scenario
Suppose the valid tables and columns are:
* Tables: mq_leads, product_launches, subscriptions, user_activity
* Columns: lead_id, first_name, last_name, email, organization, lead_source, contact_date, status, notes, follow_up_date in mq_leads;
launch_id, is_secure, provenance, version, features, documentation_link in data_sources;
subscription_id, user_id, plan_name, sub_timestamp, billing_cycle, payment_status, renewal_notice in subscriptions;
activity_id, user_id, activity_type, timestamp, duration, data_source, outcome, error_log in user_activity

_Conversation History_
User: How many leads do we have from the brightline account?
Agent: I do not see any organizations named brightline in our leads database.
User: Which accounts have the most leads then?

_Previous State_
mq_leads - [lead_id, organization]

_Output_
```json
{{
  "thought": "I can count the number of leads using lead_id. I will then group by organization to find the number of leads per account.",
  "result": [
    {{"tab": "mq_leads", "col": "organization"}},
    {{"tab": "mq_leads", "col": "lead_id"}}
  ]
}}
```

## 2. Carrying over from prior state
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach
* Columns: CourseID, CourseTitle, InstructorID, CourseDescription, StartDate, EndDate, Duration, CourseFormat, Category, EnrollmentCount in BB_courses;
EnrollmentID, CourseID, StudentID, EnrollmentDate, CompletionStatus, Feedback, CertificateLink, PaymentStatus, ReferralSource in BB_enrollments;
TestimonialID, StudentID, CourseID, TestimonialText, DateProvided, Rating, Featured, ApprovalStatus, PhotoLink in Testimonials;
OutreachID, CampaignName, TargetAudience, Platform, ResponseRate, Collaborators in CanvasOutreach

_Conversation History_
User: how many of the students enrolled in the Biology course provided a testimonial?
Agent: 12 students enrolled in Biology provided an approved testimonial.
User: can you show me the number of students who provided testimonials for each course?

_Previous State_
BB_courses - [CourseID, CourseTitle, Category]
BB_enrollments - [CourseID, StudentID, EnrollmentDate]
Testimonials - [CourseID, StudentID, TestimonialText, ApprovalStatus]

_Output_
```json
{{
  "thought": "StudentID can be used to count number of students. BB_Courses is used to group by courses. TestimonialText should be checked to make sure it is not empty, and ApprovalStatus is useful to make sure it can be used. CourseID can be used to group by course and join tables.",
  "result": [
    {{"tab": "BB_courses", "col": "CourseID"}},
    {{"tab": "BB_courses", "col": "CourseTitle"}},
    {{"tab": "BB_enrollments", "col": "StudentID"}},
    {{"tab": "Testimonials", "col": "CourseID"}},
    {{"tab": "Testimonials", "col": "StudentID"}},
    {{"tab": "Testimonials", "col": "TestimonialText"}},
    {{"tab": "Testimonials", "col": "ApprovalStatus"}}
  ]
}}
```

_Lesson_
Pay close attention to the full conversation history since current columns are often carried over from the previous state. CourseTitle is not strictly necessary but can be useful for labeling the plot.

## 3. Infer Values from Past State
Suppose the valid tables and columns are:
* Tables: CustContact, CustOrders, MarketingOffers
* Columns: CustomerID, CustName, FavCuisineType, ShippingAddress, ContactNumber, IsActive, Twitter, Instagram, Yelp in CustContact;
OrderID, CustomerID, RestaurantID, OrderDate, TotalAmount, DeliveryAddress, OrderStatus, EstDeliveryTime, SpecialInstructions in CustOrders;
OfferID, OfferTitle, OfferDescription, OrderKey, RedemptionDate, DiscountAmount, ApplicableRestaurants, RedemptionCode in MarketingOffers

_Conversation History_
User: ok, let's also check for the SEND40OFF code
Agent: The SEND40OFF redemption code was used by 23 customers.
User: Get the number of customers who used that code each day as a line graph

_Previous State_
CustOrders - [OrderID, CustomerID]
MarketingOffers - [OrderKey, RedemptionDate, RedemptionCode]

_Output_
```json
{{
  "thought": "RedemptionCode can be used to filter for SEND40OFF and I can sum the number of customers with CustomerID. I can likely join the MarketingOffers tables with CustOrders table using OrderID and OrderKey. Then, I can group by RedemptionDate.",
  "result": [
    {{"tab": "CustOrders", "col": "OrderID"}},
    {{"tab": "CustOrders", "col": "CustomerID"}},
    {{"tab": "MarketingOffers", "col": "OrderKey"}},
    {{"tab": "MarketingOffers", "col": "RedemptionDate"}},
    {{"tab": "MarketingOffers", "col": "RedemptionCode"}}
  ]
}}
```

_Lesson_
Although the user didn't mention the code in their second query, you can infer the code from the previous state. This demonstrates the importance of considering the full conversation history.

## 4. Visualization of a Previous Query
Suppose the valid tables and columns are:
* Tables: Customers, Campaigns, Inventory, Promotions
* Columns: CustomerID, FirstName, LastName, Email, PhoneNumber, DateRegistered, PurchaseHistory, LoyaltyPoints, Address, PreferredBrand in Customers;
CampaignID, CampaignName, StartDate, EndDate, TargetAudience, Channel, Budget, ResponseRate, CreativeAsset in Campaigns;
ItemID, BrandName, Category, Price, StockQuantity, DateAdded, Supplier in Inventory;
PromoID, PromoName, StartDate, EndDate, DiscountPercentage, ApplicableProducts, PromoCode, RedemptionCount in Promotions

_Conversation History_
User: What are the most popular brands by preference for each month?
Agent: The most popular brand are Gucci, Chanel, and Louis Vuitton. See table for details.
User: Can I get that in a chart?

_Previous State_
Customers - [CampaignSource, PreferredBrand]
Campaigns - [CampaignID, StartDate, EndDate]

_Output_
```json
{{
  "thought": "I should copy over the previous state's columns since the user is asking for the same information.",
  "result": [
    {{"tab": "Customers", "col": "CampaignSource"}},
    {{"tab": "Customers", "col": "PreferredBrand"}},
    {{"tab": "Campaigns", "col": "CampaignID"}},
    {{"tab": "Campaigns", "col": "StartDate"}},
    {{"tab": "Campaigns", "col": "EndDate"}}
  ]
}}
```

## 5. Handling Ambiguous Situations
Suppose the valid tables and columns are:
* Tables: members, classes, packages
* Columns: member_id, full_name, email_address, phone_number, date_joined, membership_type, membership_fee, expiration_date, packages_bought, emergency_contact in members;
class_id, class_name, instructor_id, class_date, start_time, end_time, room_number, class_capacity, enrolled_count, description, equipment_required in classes;
package_id, package_name, duration, price, included_classes, additional_benefits in packages

_Conversation History_
User: What's the 3 most expensive ones?
Agent: The three most expensive packages are the Platinum Package, the Gold Package, and the 100-Class Super Pack.
User: What is the average size for each of those?

_Previous State_
packages - [package_id, package_name, price]

_Output_
```json
{{
  "thought": "I should build upon the previous state's query since the user is asking for the same packages. I can then adjust it by adding size.",
  "result": [
    {{"tab": "packages", "col": "package_id"}},
    {{"tab": "packages", "col": "package_name"}},
    {{"tab": "packages", "col": "price"}},
    {{"tab": "packages", "col": "duration", "ambiguous": True}}
    {{"tab": "packages", "col": "included_classes", "ambiguous": True}}
  ]
}}
```

_Lesson_
The package_name in the previous state was relevant even though the user did not explicity ask for it. This is because name-related columns (ie. full_name, class_name, package_name) are useful for displaying the final result in a graph.
Also note that size is ambiguous because it may refer to the number of classes included or the duration of the package.

## 6. Ambiguous Column Inferred From Utterances
Suppose the valid tables and columns are:
* Tables: Bookings, Promotions, CustomerSupport
* Columns: BookingID, CustomerID, VehicleID, StartDate, EndDate, PickupLocation, DropoffLocation, BookingStatus, TotalAmount, PaymentStatus in Bookings;
PromotionID, DiscountAmount, ApplicableVehicleTypes, TermsConditions, RedemptionCount in Promotions;
TicketID, CustomerID, IssueDate, IssueType, IssueDescription, AssignedAgent, ResolutionStatus, ResolutionDate, Feedback, FollowUpRequired in CustomerSupport

_Conversation History_
User: Yea, Los Angeles looks right
Agent: Sure, I have merged those terms into Los Angeles. Based on the results, the most popular pickup locations are Chicago, San Francisco, and Los Angeles. See table for more details.
User: What is the average price for all bookings in those locations.

_Previous State_
(temporary) - [RelatedLocationTerms]

_Output_
```json
{{
  "thought": "Based on prior agent remarks, I should use PickupLocation rather than DropoffLocation for filtering and grouping the cities. I can calculate the average price using TotalAmount.",
  "result": [
    {{"tab": "Bookings", "col": "PickupLocation"}},
    {{"tab": "Bookings", "col": "TotalAmount"}}
  ]
}}
```

_Lesson_
You should always take into account the prior dialogue history when deciding on relevant columns. You can sometimes infer ambiguous columns by reviewing the provided utterances.

## Current Scenario
For our current case, start with a concise thought followed by JSON output. there should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

trend_flow_prompt = """Given the valid tables and columns along with the conversation history, your task is to identify a pattern or trend in the data by running some chart-based analysis.
Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.

Your entire response should be in well-formatted JSON with keys for thought (string) and result (list) where each item is a dict. There should be no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Placeholder Scenario
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach

## Current Scenario
For our current case, start with a concise thought followed by a result list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

explain_flow_prompt = """Given the valid tables and columns along with the conversation history, your task is to generate an explanation or summary of the chart or graph.
Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.

Your entire response should be in well-formatted JSON with keys for thought (string) and result (list) where each item is a dict. There should be no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Placeholder Scenario
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach

## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

manage_report_prompt = """Given the valid tables and columns along with the conversation history, your task is to manage dashboard settings, such as recurring reports or data refresh rates.
Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.

Your entire response should be in well-formatted JSON with keys for thought (string) and result (list) where each item is a dict. There should be no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Placeholder Scenario
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach

## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

save_to_dashboard_prompt = """Given the valid tables and columns along with the conversation history, your task is to save the current visualization to the dashboard or to a file.
Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.

Your entire response should be in well-formatted JSON with keys for thought (string) and result (list) where each item is a dict. There should be no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Placeholder Scenario
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach

## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

design_chart_prompt = """Given the valid tables and columns along with the conversation history, your task is to design the layout and appearance of the dashboard.
Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.

Your entire response should be in well-formatted JSON with keys for thought (string) and result (list) where each item is a dict. There should be no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Placeholder Scenario
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach

## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

style_table_prompt = """Given the valid tables and columns along with the conversation history, your task is to style the appearance of a permanent derived table.
Start by constructing a concise thought concerning what information is useful for generating a SQL query regarding the final user utterance.
Then, choosing only from valid tables and columns, generate the list of relevant targets needed to create the query.
If it is unclear what tables are being discussed, output 'unsure'. If a column is confusing or uncertain, mark it as ambiguous. If no columns are relevant, then just leave the list empty.

Your entire response should be in well-formatted JSON with keys for thought (string) and result (list) where each item is a dict. There should be no further explanations after the JSON output.
Let's consider six example scenarios, and then tackle the current case.

## 1. Placeholder Scenario
Suppose the valid tables and columns are:
* Tables: BB_courses, BB_enrollments, Testimonials, CanvasOutreach

## Current Scenario
For our current case, start with a concise thought followed by a JSON-formatted list of tabs and cols. There should be no explanations or lessons after the JSON output. As reference, the valid tables and columns are:
{valid_tab_col}

_Conversation History_
{history}

_Output_
"""

