<script lang="ts">
  import DialoguePanel from './components/dialogue_panel/dialoguePanel.svelte';
  import DisplayPanel from './components/display_panel/displayPanel.svelte';
  import { onMount, onDestroy } from 'svelte';
  import { showResetModal, resetChat, initializeSocket } from '@store';
  import ResetModal from './lib/resetModal.svelte';
  import { goto } from '$app/navigation';
  import { checkAuthentication } from '$lib/apiUtils';
  import { clearAlert } from '@alert';

  // Flag to prevent multiple redirects
  let isNavigating = false;
  let browserWindow = null;
  
  onMount(async () => {
    try {
      const isAuthenticated = await checkAuthentication();
      
      if (!isAuthenticated) {
        console.log('User not authenticated, redirecting to login page');
        // Set navigating flag to prevent duplicate redirects
        if (!isNavigating) {
          isNavigating = true;
          goto('/login');
        }
        return;
      }
      
      const socket = await initializeSocket();
      
      browserWindow = window;
      browserWindow.history.pushState(null, null, browserWindow.location.href);
      browserWindow.onpopstate = function () {
        browserWindow.history.go(1);
      };
      clearAlert();
    } catch (error) {
      console.error('Authentication error:', error);
      if (!isNavigating) {
        isNavigating = true;
        goto('/login');
      }
    }
  });

  onDestroy(() => {
    if (browserWindow) {
      browserWindow.onpopstate = null;
    }
  });
</script>

<svelte:head>
  <title>Soleda: Dana</title>
</svelte:head>

<div class="flex flex-col flex-grow md:flex-row h-full max-w-screen w-screen p-3 gap-4">
  <DialoguePanel />
  <DisplayPanel />
</div>

{#if $showResetModal}
  <ResetModal message="Do you want to clear out the existing conversation to start a new session?"
      onConfirm={resetChat} onCancel={() => showResetModal.set(false)} />
{/if}
