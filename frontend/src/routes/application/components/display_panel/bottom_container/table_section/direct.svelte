<script>
  import { searchQuery, tableView, pushEdit, popEdit } from '@store';
  import { selectedTable, fetchData, sheetData, tabSwitchTrigger } from '@store'; // from dataStore
  import { onDestroy, onMount } from 'svelte';
  import { fade } from 'svelte/transition';
  import { get } from 'svelte/store';
  import { getSortFunction, copy, paste } from './tableUtils.js';
  import { logger } from '$lib/logger';
  let tableRegion;
  let columnNames = [];
  let columnWidths = {};
  let dataToDisplay = [];
  let numberOfRows = 0;

  let selectedCell = '';
  let cellCoordinates = null;
  let editCoordinates = { col: '', row: -1 };
  let foundCells = new Set();
  let anchorPoints;

  let isLoading = false;
  let isEditing = false;
  let isWriting = false;
  let isDataEnd = false;
  let endMessageTriggered = false;
  let unsubscribe;

  onMount(() => {
    unsubscribe = tableView.subscribe(displayData);
    window.addEventListener('keydown', navigateTo);
    document.addEventListener('click', handleClick);
    tableRegion.addEventListener('scroll', handleScroll);
  });
  onDestroy(() => {
    if (unsubscribe) unsubscribe();
    window.removeEventListener('keydown', navigateTo);
    document.removeEventListener('click', handleClick);
  });

  function displayData(tableData) {
    if (tableData) {
      columnNames = Object.keys(tableData[0]);
      columnNames.forEach((name) => {
        if (!(name in columnWidths)) {
          columnWidths[name] = 100;
        }  // default width of 100 px
      });
      numberOfRows = tableData.length;
      // a list of dicts, where each dict is has keys for column names and values are cell values
      dataToDisplay = tableData.map((row, rowIndex) => {
        let transformedRow = Object.entries(row).map(([key, value], cellIndex) => {
          return { colId: key, value: value };
        });
        return { row: transformedRow, rowId: rowIndex + 1 };
      });
    }
  }

  function handleClick(event) {
    if (isEditing || isWriting || selectedCell.length > 0) {
      if (!tableRegion.contains(event.target)) {
        resetTable({ content: true, selection: true });
      }
    }
  }

  function handleScroll(event) {
    if (tableRegion.scrollTop + tableRegion.clientHeight >= tableRegion.scrollHeight - 10) {
      const { hasMoreData } = $sheetData[$selectedTable];
      if (hasMoreData && !isLoading) {
        isLoading = true;
        fetchData($selectedTable).then(() => {
          isLoading = false;
        });
      }
    }
  }

  // if the sheetData is updated, change the tableView and note if we reached the end of data
  $: if ($sheetData && $selectedTable in $sheetData) {
    const tabData = $sheetData[$selectedTable];
    updateTableFromSheetData(tabData);
  }

  function updateTableFromSheetData(tabData) {
    if (tabData.rows?.length > 0) {  // extra safety mechanism to avoid empty tables
      tableView.set(tabData.rows);

      if (!tabData.hasMoreData && !endMessageTriggered) {
        isDataEnd = true;
        setTimeout(() => {
          isDataEnd = false;
          endMessageTriggered = true;  // Show the end message for 4 seconds
        }, 4000);
      }
    }
    anchorPoints = tabData.anchorPoints;
  }
  
  $: if ($tabSwitchTrigger && tableRegion) {
    tableRegion.scrollTop = 0; // to avoid immediately fetching data on tab switch
  }

  function resetTable({ content = false, selection = false } = {}) {
    isEditing = false;
    isWriting = false;
    editCoordinates = { col: '', row: -1 };

    if (content) {
      originalContent = '';
      novelContent = '';
    }
    if (selection) {
      cellCoordinates = null;
      selectedCell = '';
    }
  }

  let sortConfig = { columns: [], direction: 'none', table: $selectedTable }; // [none, desc, asc]
  let originalToSorted = {};
  let sortedToOriginal = {};

  function navigateTo(event) {
    if (isEditing || isWriting) return; // Ignore key presses when editing or writing

    if (cellCoordinates) {
      if (event.preventDefault) {
        event.preventDefault();
      }

      if ((event.ctrlKey || event.metaKey) && event.key === 'c') {
        copy(cellCoordinates, dataToDisplay);
        return;
      } else if ((event.ctrlKey || event.metaKey) && event.key === 'v') {
        paste(cellCoordinates, dataToDisplay).then(newData => { dataToDisplay = newData; });
        return;
      } else if ((event.ctrlKey || event.metaKey) && event.key === 'z') {
        undo();
        return;
      } else if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
        arrowNavigation(event);
      } else {
        keyboardNavigation(event);
      }
    }
  }

  function keyboardNavigation(event) {
    switch (event.key) {
      case 'Tab':
        event.preventDefault(); // Prevents the default tab behavior
        navigateTo({ key: 'ArrowRight' });
        break;

      case 'Backspace':
      case 'Delete':
        activateEditMode(cellCoordinates.col, cellCoordinates.row, true);
        novelContent = ''; // empty the cell contents
        break;

      case 'Enter':
        activateEditMode(cellCoordinates.col, cellCoordinates.row);
        break;

      default: // If alphanumeric
        if (/^[a-zA-Z0-9]$/.test(event.key)) {
          activateEditMode(cellCoordinates.col, cellCoordinates.row, true);
          novelContent = event.key; // Initialize the novelContent with the pressed key
        }
    }
  }

  function arrowNavigation(event) {
    const jumpToEnd = event.metaKey || event.ctrlKey; // Check for Cmd (Mac) or Ctrl (Windows) key
    let { row: rowIndex, col: colId } = cellCoordinates;
    let sortedRowIndex = originalToSorted[rowIndex] || rowIndex; // Sorted row for display
    let colIndex = columnNames.indexOf(colId);

    if (jumpToEnd) {
      switch (event.key) {
        case 'ArrowUp':
          sortedRowIndex = 1;
          rowIndex = sortedToOriginal[sortedRowIndex] || sortedRowIndex;
          break;
        case 'ArrowDown':
          sortedRowIndex = numberOfRows;
          rowIndex = sortedToOriginal[sortedRowIndex] || sortedRowIndex;
          break;
        case 'ArrowLeft':
          colIndex = 0;
          break;
        case 'ArrowRight':
          colIndex = columnNames.length - 1;
          break;
      }
    } else {
      // Navigate in sorted order if sorting exists
      switch (event.key) {
        case 'ArrowUp':
          sortedRowIndex = nextVisibleRow(sortedRowIndex, 'ArrowUp');
          rowIndex = sortedToOriginal[sortedRowIndex] || sortedRowIndex;
          break;
        case 'ArrowDown':
          sortedRowIndex = nextVisibleRow(sortedRowIndex, 'ArrowDown');
          rowIndex = sortedToOriginal[sortedRowIndex] || sortedRowIndex;
          break;
        case 'ArrowLeft':
          colIndex = colIndex > 0 ? colIndex - 1 : colIndex;
          break;
        case 'ArrowRight':
          colIndex = colIndex < columnNames.length - 1 ? colIndex + 1 : colIndex;
          break;
      }
    }
    selectRegion(columnNames[colIndex], rowIndex);
  }

  function nextVisibleRow(currentIndex, direction) {
    if (direction === 'ArrowDown') {
      for (let i = currentIndex + 1; i <= numberOfRows; i++) {
        if (dataToDisplay[i - 1].isVisible) return i;
      }
    } else if (direction === 'ArrowUp') {
      for (let i = currentIndex - 1; i >= 1; i--) {
        if (dataToDisplay[i - 1].isVisible) return i;
      }
    }
    return currentIndex; // If no visible row is found, return the current index
  }

  // For searching
  $: {
    foundCells.clear();
    dataToDisplay.forEach((rowData) => {
      let rowHasMatchingCell = false;

      rowData.row.forEach((cellData) => {
        if ($searchQuery) {
          const queryText = $searchQuery.toLowerCase();
          if (cellData.value.toString().toLowerCase().includes(queryText)) {
            foundCells.add(`${cellData.colId}-${rowData.rowId}`);
            rowHasMatchingCell = true;
          }
        }
      });
      // Mark the row as visible if at least one cell matches the search query
      rowData.isVisible = !$searchQuery || rowHasMatchingCell;
    });
    dataToDisplay = dataToDisplay.slice(); // Force Svelte to recognize the change
  }

  function selectRegion(colName, rowId) {
    cellCoordinates = { col: colName, row: rowId };
    selectedCell = `${colName}-${rowId}`;

    const currentCell = document.querySelector(`[data-id="${selectedCell}"]`);
    const tableContainer = document.querySelector('#full-table');

    if (rowId < 2 && tableContainer) {
      tableContainer.scrollTop = 0;
    } else if (currentCell) {
      currentCell.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'nearest' });
    }
    // console.log(cellCoordinates);
  }

  function sortTable(colName) {
    if (isResizing) return; // ignore clicks when resizing columns
    const existingColumn = sortConfig.columns.find((c) => c.column === colName);
    // const existingColumn = sortConfig.columns.find(c => c.column.includes(colName));

    let newDirection;
    if (existingColumn) {
      newDirection = existingColumn.direction === 'asc' ? 'desc' : 'none';
    } else {
      newDirection = 'asc';
    }

    if (newDirection === 'none') {
      sortConfig.columns = sortConfig.columns.filter((c) => c.column !== colName);
    } else {
      if (existingColumn) {
        existingColumn.direction = newDirection;
      } else {
        sortConfig.columns.push({ column: colName, direction: newDirection });
      }
    } // Force Svelte to recognize the change
    sortConfig = { ...sortConfig, columns: [...sortConfig.columns] };

    logger.info('datasheet_sort', 'DataSheet', {
      details: {
        tableType: 'direct',
        direction: newDirection,
        column: colName,
      },
    });
  }

  // Tables will reactively sort itself whenever sortConfig is updated
  $: if (sortConfig.table === $selectedTable) {
    const noColumns = sortConfig.columns.length === 0;
    const noDirections = sortConfig.columns.every((c) => c.direction === 'none');
    let allNone = noColumns || noDirections;

    if (allNone) {
      dataToDisplay = [...dataToDisplay].sort((a, b) => a.rowId - b.rowId);
      originalToSorted = {}; // Reset the mapping
      sortedToOriginal = {};
    } else {
      const sortFunctions = sortConfig.columns.map(getSortFunction);
      dataToDisplay = [...dataToDisplay].sort((a, b) => {
        for (const sortFn of sortFunctions) {
          const result = sortFn(a, b);
          if (result !== 0) return result;
        }
        return 0;
      });
      dataToDisplay.forEach((row, index) => {
        const sortedIndex = index + 1;
        originalToSorted[row.rowId] = sortedIndex;
        sortedToOriginal[sortedIndex] = row.rowId;
      });
    }
  } else {
    sortConfig = { columns: [], direction: 'none', table: $selectedTable };
    originalToSorted = {};
    sortedToOriginal = {};
  }

  let isResizing = false;
  let activelyResizingColumn = null;
  let startX = 0;

  function startResize(event, colName) {
    isResizing = true;
    activelyResizingColumn = colName;
    startX = event.clientX;
    document.addEventListener('mousemove', resizeColumn);
    document.addEventListener('mouseup', stopResize);
  }
  function resizeColumn(event) {
    if (!isResizing) return;
    const dx = event.clientX - startX;
    columnWidths[activelyResizingColumn] += dx;
    startX = event.clientX;
  }
  function stopResize() {
    isResizing = false;
    document.removeEventListener('mousemove', resizeColumn);
    document.removeEventListener('mouseup', stopResize);
  }

  function autoResize(colName) {
    // Change table layout to auto
    const table = document.querySelector('table');
    table.style.tableLayout = 'auto';
    // Remove width style from the column we want to auto-resize
    const headerCell = document.querySelector(`th[data-id="${colName}"]`);
    headerCell.style.width = '';
    // Force a reflow so browser recalculates
    void table.offsetHeight;

    // Get the width of the header and body cells
    const headerWidth = parseInt(window.getComputedStyle(headerCell).width);
    const rowsToConsider = Math.min(128, dataToDisplay.length);
    let bodyWidth = 0;
    for (let i = 0; i < rowsToConsider; i++) {
      const bodyCell = document.querySelector(`td[data-id="${colName}-${dataToDisplay[i].rowId}"]`);
      const cellWidth = parseInt(window.getComputedStyle(bodyCell).width);
      if (cellWidth > bodyWidth) {
        bodyWidth = cellWidth;
      }
    }
    // Revert back to table-layout: fixed and set the final captured width
    const finalWidth = `${Math.min(Math.max(headerWidth, bodyWidth), 1024)}px`;
    table.style.tableLayout = 'fixed';
    headerCell.style.width = finalWidth;
    columnWidths[colName] = parseInt(finalWidth);
  }

  let originalContent = ''; // To store original content during editing
  let novelContent = ''; // To store new, edited content during editing
  function activateEditMode(colName, rowId, writing = false) {
    const row = dataToDisplay.find((r) => r.rowId === rowId);
    const cell = row.row.find((c) => c.colId === colName);

    if (cell) {
      originalContent = cell.value;
      novelContent = originalContent;
    }
    isEditing = true;
    isWriting = writing;
    editCoordinates = { col: colName, row: rowId };
  }

  function saveChanges() {
    const { row: rawId, col: colIndex } = editCoordinates;

    if (rawId > 0 && colIndex.length > 0) {
      const rowIndex = originalToSorted[rawId] - 1 || rawId - 1; // Handle sorting and off-by-one
      const cellData = dataToDisplay[rowIndex].row.find((cell) => cell.colId === colIndex);

      if (cellData) {
        cellData.value = novelContent;
        storeEditHistory(rowIndex, colIndex);
      }
      dataToDisplay = [...dataToDisplay]; // Force Svelte to recognize the change
    }

    resetTable({ content: true, selection: false });
  }

  function storeEditHistory(row, col) {
    const changeType = novelContent.length === 0 ? 'clearCell' : 'updateCell';

    // Update the editHistory with the changed data
    pushEdit({
      type: changeType,
      row: row,
      col: col,
      originalValue: originalContent,
      newValue: novelContent,
      timestamp: new Date().toISOString(), // ISO string format of the current time
    });
  }

  function undo() {
    const lastAction = popEdit();
    if (!lastAction) return; // Return immediately if there is no previous action

    const cellData = dataToDisplay[lastAction.row].row.find(
      (cell) => cell.colId === lastAction.col
    );
    if (cellData) {
      cellData.value = lastAction.originalValue;
      dataToDisplay = [...dataToDisplay];
      selectRegion(lastAction.col, lastAction.row + 1);
    }
  }

  function handleEditEvents(event) {
    const currentInput = event.target;
    const cursorPosition = currentInput.selectionStart;
    event.stopPropagation();

    switch (event.key) {
      // Delete and Up/Down/Left/Right all follow default input box behavior, no need to handle
      case 'Enter':
        saveChanges();
        navigateTo({ key: 'ArrowDown' });
        break;
      case 'Tab':
        event.preventDefault(); // Prevents the default tab behavior
        saveChanges();
        navigateTo({ key: 'ArrowRight' });
        break;
      case 'Escape':
        // Revert the value to the original value
        resetTable({ content: true, selection: false });
        break;

      case 'ArrowLeft':
      case 'ArrowRight':
      case 'ArrowUp':
      case 'ArrowDown':
        if (isWriting) {
          saveChanges();
          navigateTo(event);
        }
    }
  }
</script>

<div class="border-gray-200 border mt-1 lg:mt-2 inline-block w-auto max-h-full overflow-y-auto table-region"
    transition:fade={{ duration: 400 }} bind:this={tableRegion}>
  <table class="text-sm text-left text-gray-500 table-fixed w-full">
    <thead class="text-gray-50 h-8">
      <tr>{#each columnNames as col}
          <th class="whitespace-normal bg-teal-500 relative px-2 text-clip sticky top-0 z-10"
            scope="col" data-id={col} style={`width: ${columnWidths[col]}px`}>
            <span class="cursor-pointer" on:click={() => sortTable(col)}>{col}</span>
            {#if sortConfig.columns.some((c) => c.column === col && c.direction === 'asc')}
              ▲
            {:else if sortConfig.columns.some((c) => c.column === col && c.direction === 'desc')}
              ▼
            {/if}
            <div class="absolute top-0 bottom-0 right-0 w-0.5 bg-stone-400 cursor-col-resize
              opacity-50 hover:opacity-100 hover:w-1 z-10" on:mousedown={(e) => startResize(e, col)}
              on:click|stopPropagation on:dblclick|stopPropagation={() => autoResize(col)}/>
          </th>
        {/each}
      </tr>
    </thead>

    <tbody>
      {#each dataToDisplay as rows (rows.rowId)}
        <tr class="border-b bg-white h-7" class:hidden={!rows.isVisible}>
          {#each rows.row as cell (cell.colId)}
            <td data-id={`${cell.colId}-${rows.rowId}`}
              class="whitespace-nowrap overflow-hidden text-clip border-r px-3 cursor-default relative"
              class:selected={selectedCell === `${cell.colId}-${rows.rowId}`}
              class:bg-yellow-100={foundCells.has(`${cell.colId}-${rows.rowId}`)}
              on:dblclick={() => activateEditMode(cell.colId, rows.rowId)}
              on:click={() => selectRegion(cell.colId, rows.rowId)}>
              {cell.value}
              <!-- Input Overlay for editing content -->
              {#if editCoordinates.row === rows.rowId && editCoordinates.col === cell.colId}
                <input class="absolute top-0 left-0 h-7 w-full pl-3 pb-1 border-none" on:blur={saveChanges}
                    bind:value={novelContent} on:keydown|stopPropagation={handleEditEvents} autofocus/>
              {/if}
            </td>
          {/each}
        </tr>
      {/each}
    </tbody>
  </table>
</div>

<div class="text-center py-2">
  <p class="text-gray-600 text-sm">
  {#if isLoading}
    Loading more data...
  {:else if isDataEnd}
    You've reached the end!
  {/if}
  </p>
</div>

<style>
  .selected {   /*  dark blue  */
    border: 2px solid #0c5cd3;
  }
</style>
