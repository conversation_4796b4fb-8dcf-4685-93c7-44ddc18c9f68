<script>
  import { searchQuery, tableView, tempData, selectedTable, tableStore, sheetData } from '@store';
  import { onDestroy, onMount } from 'svelte';
  import { fade } from 'svelte/transition';
  import { getSortFunction, copy } from './tableUtils.js';
  import { logger } from '$lib/logger';

  let tableRegion;
  let columnNames = [];
  let dataToDisplay = [];
  let numberOfRows = 0;
  let selectedCell = '';

  let cellCoordinates = null;
  let unsubscribe;

  onMount(() => {
    unsubscribe = tableStore.subscribe(({ tableData, tableType }) => {
      displayData(tableData, tableType);
    });
    window.addEventListener('keydown', navigateTo);
    document.addEventListener('click', handleClick);
  });

  onDestroy(() => {
    if (unsubscribe) unsubscribe();
    window.removeEventListener('keydown', navigateTo);
    document.removeEventListener('click', handleClick);
    
    if ($tempData && $tempData.name) {
      sheetData.update(currentSheet => {
        if (currentSheet[$tempData.name]) {
          delete currentSheet[$tempData.name];
        }
        return currentSheet;
      });
    }
  });

  function displayData(tableData, tableType) {
    if (tableType === 'derived' && tableData) {
      columnNames = Object.keys(tableData[0]);
      numberOfRows = tableData.length;
      // a list of dicts, where each dict is has keys for column names and values are cell values
      dataToDisplay = tableData.map((row, rowIndex) => {
        let transformedRow = Object.entries(row).map(([key, value], cellIndex) => {
          return { colId: key, value: value };
        });
        return { row: transformedRow, rowId: rowIndex + 1 };
      });
    }
  }

  function handleClick(event) {
    if (selectedCell.length > 0) {
      if (!tableRegion.contains(event.target)) {
        cellCoordinates = null;
        selectedCell = '';
      }
    }
  }

  $: if ($tempData) {
    const derivedData = $tempData.rows;
    if (derivedData.length > 0) {
      tableView.set(derivedData);
    }
  }

  let sortConfig = { columns: [], direction: 'none', table: $selectedTable }; // [none, desc, asc]
  let originalToSorted = {};
  let sortedToOriginal = {};

  function navigateTo(event) {
    if (cellCoordinates) {
      if (event.preventDefault) {
        event.preventDefault();
      }

      if ((event.ctrlKey || event.metaKey) && event.key === 'c') {
        copy(cellCoordinates, dataToDisplay);
        return;
      } else if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
        arrowNavigation(event);
      }
    }
  }

  function arrowNavigation(event) {
    const jumpToEnd = event.metaKey || event.ctrlKey; // Check for Cmd (Mac) or Ctrl (Windows) key
    let { row: rowIndex, col: colId } = cellCoordinates;
    let sortedRowIndex = originalToSorted[rowIndex] || rowIndex; // Sorted row for display
    let colIndex = columnNames.indexOf(colId);

    if (jumpToEnd) {
      switch (event.key) {
        case 'ArrowUp':
          sortedRowIndex = 1;
          rowIndex = sortedToOriginal[sortedRowIndex] || sortedRowIndex;
          break;
        case 'ArrowDown':
          sortedRowIndex = numberOfRows;
          rowIndex = sortedToOriginal[sortedRowIndex] || sortedRowIndex;
          break;
        case 'ArrowLeft':
          colIndex = 0;
          break;
        case 'ArrowRight':
          colIndex = columnNames.length - 1;
          break;
      }
    } else {
      // Navigate in sorted order if sorting exists
      switch (event.key) {
        case 'ArrowUp':
          sortedRowIndex = nextVisibleRow(sortedRowIndex, 'ArrowUp');
          rowIndex = sortedToOriginal[sortedRowIndex] || sortedRowIndex;
          break;
        case 'ArrowDown':
          sortedRowIndex = nextVisibleRow(sortedRowIndex, 'ArrowDown');
          rowIndex = sortedToOriginal[sortedRowIndex] || sortedRowIndex;
          break;
        case 'ArrowLeft':
          colIndex = colIndex > 0 ? colIndex - 1 : colIndex;
          break;
        case 'ArrowRight':
          colIndex = colIndex < columnNames.length - 1 ? colIndex + 1 : colIndex;
          break;
      }
    }
    selectRegion(columnNames[colIndex], rowIndex);
  }

  function nextVisibleRow(currentIndex, direction) {
    if (direction === 'ArrowDown') {
      for (let i = currentIndex + 1; i <= numberOfRows; i++) {
        if (dataToDisplay[i - 1].isVisible) return i;
      }
    } else if (direction === 'ArrowUp') {
      for (let i = currentIndex - 1; i >= 1; i--) {
        if (dataToDisplay[i - 1].isVisible) return i;
      }
    }
    return currentIndex; // If no visible row is found, return the current index
  }

  let foundCells = new Set();
  // For searching
  $: {
    foundCells.clear();
    dataToDisplay.forEach((rowData) => {
      let rowHasMatchingCell = false;

      rowData.row.forEach((cellData) => {
        if ($searchQuery) {
          const queryText = $searchQuery.toLowerCase();
          if (cellData.value.toString().toLowerCase().includes(queryText)) {
            foundCells.add(`${cellData.colId}-${rowData.rowId}`);
            rowHasMatchingCell = true;
          }
        }
      });
      // Mark the row as visible if at least one cell matches the search query
      rowData.isVisible = !$searchQuery || rowHasMatchingCell;
    });
    dataToDisplay = dataToDisplay.slice(); // Force Svelte to recognize the change
  }

  function selectRegion(colName, rowId) {
    cellCoordinates = { col: colName, row: rowId };
    selectedCell = `${colName}-${rowId}`;

    const currentCell = document.querySelector(`[data-id="${selectedCell}"]`);
    const tableContainer = document.querySelector('#full-table');

    if (rowId < 2 && tableContainer) {
      tableContainer.scrollTop = 0;
    } else if (currentCell) {
      currentCell.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'nearest' });
    }
  }

  function sortTable(colName) {
    const existingColumn = sortConfig.columns.find((c) => c.column === colName);

    let newDirection;
    if (existingColumn) {
      newDirection = existingColumn.direction === 'asc' ? 'desc' : 'none';
    } else {
      newDirection = 'asc';
    }

    if (newDirection === 'none') {
      sortConfig.columns = sortConfig.columns.filter((c) => c.column !== colName);
    } else {
      if (existingColumn) {
        existingColumn.direction = newDirection;
      } else {
        sortConfig.columns.push({ column: colName, direction: newDirection });
      }
    } // Force Svelte to recognize the change
    sortConfig = { ...sortConfig, columns: [...sortConfig.columns] };

    logger.info('datasheet_sort', 'DataSheet', {
      details: {
        tableType: 'derived',
        direction: newDirection,
        column: colName,
      },
    });
  }

  // Tables will reactively sort itself whenever sortConfig is updated
  $: if (sortConfig.table === $selectedTable) {
    const noColumns = sortConfig.columns.length === 0;
    const noDirections = sortConfig.columns.every((c) => c.direction === 'none');
    let allNone = noColumns || noDirections;

    if (allNone) {
      dataToDisplay = [...dataToDisplay].sort((a, b) => a.rowId - b.rowId);
      originalToSorted = {}; // Reset the mapping
      sortedToOriginal = {};
    } else {
      const sortFunctions = sortConfig.columns.map(getSortFunction);
      dataToDisplay = [...dataToDisplay].sort((a, b) => {
        for (const sortFn of sortFunctions) {
          const result = sortFn(a, b);
          if (result !== 0) return result;
        }
        return 0;
      });
      dataToDisplay.forEach((row, index) => {
        const sortedIndex = index + 1;
        originalToSorted[row.rowId] = sortedIndex;
        sortedToOriginal[sortedIndex] = row.rowId;
      });
    }
  } else {
    sortConfig = { columns: [], direction: 'none', table: $selectedTable };
    originalToSorted = {};
    sortedToOriginal = {};
  }
</script>

<div class="border-gray-200 border mt-1 lg:mt-2 inline-block table-region" 
    transition:fade={{ duration: 400 }} bind:this={tableRegion}>
  <table class="text-sm text-left text-gray-500 table-fixed">
    <thead class="text-xs uppercase text-gray-50 bg-cyan-500 h-7 hover:cursor-pointer">
      <tr>{#each columnNames as col}
          <th class="whitespace-normal relative px-2 text-clip" scope="col" data-id={col}>
            <span class="cursor-pointer" on:click={() => sortTable(col)}>{col}</span>
            {#if sortConfig.columns.some((c) => c.column === col && c.direction === 'asc')}
              ▲
            {:else if sortConfig.columns.some((c) => c.column === col && c.direction === 'desc')}
              ▼
            {/if}
          </th>
        {/each}
      </tr>
    </thead>

    <tbody>
      {#each dataToDisplay as rows (rows.rowId)}
        <tr class="border-b bg-white hover:bg-sky-100 h-7" class:hidden={!rows.isVisible}>
          {#each rows.row as cell (cell.colId)}
            <td data-id={`${cell.colId}-${rows.rowId}`}
              class="whitespace-nowrap overflow-hidden text-clip border-r px-3 cursor-default relative"
              class:selected={selectedCell === `${cell.colId}-${rows.rowId}`}
              class:bg-yellow-100={foundCells.has(`${cell.colId}-${rows.rowId}`)}
              on:click={() => selectRegion(cell.colId, rows.rowId)}>
              {cell.value}
            </td>
          {/each}
        </tr>
      {/each}
    </tbody>
  </table>
</div>

<style>
  .selected {   /*  dark blue  */
    border: 2px solid #0c5cd3;
  }
</style>
