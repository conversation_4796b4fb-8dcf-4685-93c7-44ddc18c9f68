<script lang="ts">
  import { selectSheet, availableSheets, initializeSocket, activeConnector, agentStatus, disconnectionMessage, connectionStatus } from '@store';
  import { displayAlert } from '@alert';
  import AlertBox from '../../shared/alertBox.svelte';
  
  let hoveredSheet = null;

  async function localSheetSelect(spreadsheet) {
    const socketConnection = await initializeSocket();
    if (socketConnection) {
      selectSheet(spreadsheet);
    } else {
      displayAlert('error', 'Failed to load spreadsheet due to socket connection issue.');
    }
  }

  // Show error message when agent is disconnected
  $: if ($agentStatus === false && $connectionStatus === 'disconnected' && $disconnectionMessage) {
    displayAlert('error', $disconnectionMessage, true /* Persistent */);
  }
</script>

<div class="m-8 p-0 md:p-2 lg:px-4 lg:py-2">
  <AlertBox />

  <p class="font-medium text-xl mb-1">Welcome to Soleda!</p>
  <p class="my-2">
    To get started, please upload your CSV files by dragging into the bottom panel. You can also
    select one of the pre-loaded spreadsheets from the list of options below:
  </p>
  <ol class="mx-8 my-1 list-disc text-lg">
    {#each $availableSheets as spreadsheet}
      <li class="cursor-pointer"
        on:mouseover={() => (hoveredSheet = spreadsheet.ssName)}
        on:mouseout={() => (hoveredSheet = null)}
        on:click={() => {
          localSheetSelect(spreadsheet);
          $activeConnector = null;
        }}
        on:focus={() => (hoveredSheet = spreadsheet.ssName)}
        on:keydown={(e) => { if (e.key === 'Enter') {localSheetSelect(spreadsheet);} }}
        on:blur={() => (hoveredSheet = null)}>

        <div class="flex items-center">
          <span class="italic transition-all duration-300 ease-in-out
            {hoveredSheet === spreadsheet.ssName ? 'text-teal-500 font-bold' : ''}">
            {spreadsheet.ssName}
          </span>: {spreadsheet.tabNames.join(', ')}

          <div class="px-2 hover:text-teal-500">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              class="w-6 h-6 transition-opacity transition-visibility ease-in-out duration-300
              {hoveredSheet !== spreadsheet.ssName ? 'opacity-0' : 'opacity-100'}"
              stroke="currentColor" on:click={() => localSheetSelect(spreadsheet)} >
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M12.75 15l3-3m0 0l-3-3m3 3h-7.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        </div>

      </li>
    {/each}
  </ol>

</div>